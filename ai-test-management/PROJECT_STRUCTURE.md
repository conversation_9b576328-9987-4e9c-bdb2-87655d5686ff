# AI测试管理系统项目结构

## 项目概览

```
ai-test-management/
├── README.md                    # 项目说明文档
├── DEPLOYMENT.md               # 部署指南
├── PROJECT_STRUCTURE.md       # 项目结构说明（本文件）
├── start.sh                    # 一键启动脚本
├── docker-compose.yml          # 生产环境Docker配置
├── docker-compose.dev.yml     # 开发环境Docker配置
├── backend/                    # 后端代码
├── frontend/                   # 前端代码
└── database/                   # 数据库脚本
```

## 后端结构 (FastAPI + Python)

```
backend/
├── app/                        # 应用主目录
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── init_db.py             # 数据库初始化脚本
│   ├── core/                   # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py          # 应用配置
│   │   ├── database.py        # 数据库配置
│   │   └── security.py        # 安全认证
│   ├── models/                 # SQLAlchemy数据模型
│   │   ├── __init__.py
│   │   ├── user.py            # 用户模型
│   │   ├── project.py         # 项目模型
│   │   ├── requirement.py     # 需求模型
│   │   └── testcase.py        # 测试用例模型
│   ├── schemas/                # Pydantic数据模式
│   │   ├── __init__.py
│   │   ├── user.py            # 用户数据模式
│   │   ├── project.py         # 项目数据模式
│   │   ├── requirement.py     # 需求数据模式
│   │   └── testcase.py        # 测试用例数据模式
│   ├── controllers/            # 业务逻辑控制器
│   │   ├── __init__.py
│   │   ├── base.py            # 基础CRUD控制器
│   │   ├── user.py            # 用户业务逻辑
│   │   ├── project.py         # 项目业务逻辑
│   │   ├── requirement.py     # 需求业务逻辑
│   │   └── testcase.py        # 测试用例业务逻辑
│   ├── agents/                 # AI智能体模块
│   │   ├── __init__.py
│   │   ├── llms.py            # LLM模型客户端
│   │   ├── utils.py           # 工具函数
│   │   ├── requirement_agents.py  # 需求分析智能体
│   │   └── testcase_agents.py     # 测试用例生成智能体
│   └── api/                    # API路由
│       ├── __init__.py
│       └── v1/                 # API版本1
│           ├── __init__.py
│           ├── auth.py         # 认证相关API
│           ├── users.py        # 用户管理API
│           ├── projects.py     # 项目管理API
│           ├── requirements.py # 需求管理API
│           ├── testcases.py    # 测试用例API
│           ├── dashboard.py    # 仪表盘API
│           └── websocket.py    # WebSocket API
├── alembic/                    # 数据库迁移
│   ├── env.py                 # Alembic环境配置
│   ├── script.py.mako         # 迁移脚本模板
│   └── versions/              # 迁移版本文件
├── uploads/                    # 文件上传目录
├── requirements.txt            # Python依赖
├── start.py                   # 启动脚本
├── Dockerfile                 # Docker构建文件
├── alembic.ini               # Alembic配置
├── .env                      # 环境变量
└── .env.example              # 环境变量示例
```

## 前端结构 (Vue 3 + TypeScript)

```
frontend/
├── public/                     # 静态资源
│   └── index.html             # HTML模板
├── src/                       # 源代码
│   ├── main.ts                # 应用入口
│   ├── App.vue                # 根组件
│   ├── api/                   # API接口
│   │   ├── request.ts         # HTTP请求配置
│   │   ├── auth.ts            # 认证API
│   │   ├── projects.ts        # 项目API
│   │   ├── requirements.ts    # 需求API
│   │   ├── testcases.ts       # 测试用例API
│   │   └── dashboard.ts       # 仪表盘API
│   ├── components/            # 通用组件
│   │   └── (待扩展)
│   ├── views/                 # 页面组件
│   │   ├── Login.vue          # 登录页面
│   │   ├── Dashboard.vue      # 仪表盘
│   │   ├── Projects/          # 项目管理
│   │   │   └── index.vue
│   │   ├── Requirements/      # 需求管理
│   │   │   ├── Analysis.vue   # 需求分析
│   │   │   └── List.vue       # 需求列表
│   │   ├── TestCases/         # 测试用例
│   │   │   ├── Generation.vue # 用例生成
│   │   │   └── Management.vue # 用例管理
│   │   ├── Automation/        # 自动化测试
│   │   │   ├── UI.vue         # UI自动化
│   │   │   ├── API.vue        # 接口自动化
│   │   │   ├── Performance.vue # 性能测试
│   │   │   └── Security.vue   # 安全测试
│   │   ├── Defects/           # 缺陷管理
│   │   │   └── index.vue
│   │   ├── Reports/           # 测试报告
│   │   │   └── index.vue
│   │   └── Settings/          # 系统设置
│   │       └── Users.vue      # 用户管理
│   ├── layout/                # 布局组件
│   │   ├── index.vue          # 主布局
│   │   └── components/        # 布局子组件
│   │       ├── Header.vue     # 顶部导航
│   │       └── Sidebar.vue    # 侧边栏
│   ├── router/                # 路由配置
│   │   └── index.ts
│   ├── stores/                # 状态管理 (Pinia)
│   │   ├── auth.ts            # 认证状态
│   │   └── app.ts             # 应用状态
│   ├── types/                 # TypeScript类型定义
│   │   ├── auth.ts            # 认证相关类型
│   │   ├── project.ts         # 项目相关类型
│   │   ├── requirement.ts     # 需求相关类型
│   │   ├── testcase.ts        # 测试用例相关类型
│   │   └── dashboard.ts       # 仪表盘相关类型
│   ├── utils/                 # 工具函数
│   │   ├── index.ts           # 通用工具函数
│   │   └── websocket.ts       # WebSocket管理
│   ├── constants/             # 常量定义
│   │   └── index.ts
│   └── styles/                # 样式文件
│       ├── index.scss         # 全局样式
│       └── variables.scss     # SCSS变量
├── package.json               # 项目依赖
├── tsconfig.json             # TypeScript配置
├── tsconfig.node.json        # Node.js TypeScript配置
├── vite.config.ts            # Vite构建配置
├── Dockerfile                # Docker构建文件
└── nginx.conf                # Nginx配置
```

## 数据库结构

```
database/
└── init.sql                   # 数据库初始化脚本
```

### 数据表结构

1. **users** - 用户表
   - 用户基本信息
   - 权限管理

2. **projects** - 项目表
   - 项目基本信息
   - 创建者关联

3. **requirements** - 需求表
   - 需求详细信息
   - 项目关联
   - 评审人关联

4. **testcases** - 测试用例表
   - 用例基本信息
   - 需求关联
   - 项目关联

5. **test_steps** - 测试步骤表
   - 测试步骤详情
   - 用例关联

## 核心功能模块

### 1. 认证授权模块
- JWT Token认证
- 用户权限管理
- 登录状态维护

### 2. 项目管理模块
- 项目CRUD操作
- 项目搜索过滤
- 项目统计信息

### 3. 需求管理模块
- 需求文档上传
- AI需求分析
- 需求结构化存储

### 4. 测试用例模块
- AI用例生成
- 用例生命周期管理
- 测试步骤管理

### 5. AI智能体模块
- AutoGen多智能体架构
- 需求分析智能体链
- 测试用例生成智能体链

### 6. WebSocket通信
- 实时消息推送
- AI处理进度反馈
- 用户交互支持

## 技术特性

### 后端特性
- **异步处理**: FastAPI异步支持
- **数据验证**: Pydantic数据验证
- **ORM映射**: SQLAlchemy ORM
- **数据库迁移**: Alembic迁移工具
- **API文档**: 自动生成Swagger文档
- **AI集成**: AutoGen智能体框架

### 前端特性
- **响应式设计**: 适配多种设备
- **组件化开发**: Vue 3 Composition API
- **类型安全**: TypeScript类型检查
- **状态管理**: Pinia状态管理
- **构建优化**: Vite快速构建
- **UI组件**: Element Plus组件库

### 部署特性
- **容器化**: Docker容器部署
- **环境隔离**: Docker Compose编排
- **反向代理**: Nginx配置
- **一键启动**: Shell脚本自动化

## 扩展指南

### 添加新的API接口
1. 在 `models/` 中定义数据模型
2. 在 `schemas/` 中定义数据模式
3. 在 `controllers/` 中实现业务逻辑
4. 在 `api/v1/` 中添加路由

### 添加新的前端页面
1. 在 `views/` 中创建页面组件
2. 在 `router/` 中配置路由
3. 在 `api/` 中添加接口调用
4. 在 `types/` 中定义类型

### 添加新的AI智能体
1. 在 `agents/` 中创建智能体文件
2. 定义智能体类和消息处理
3. 在WebSocket API中集成
4. 在前端添加调用接口

## 开发规范

### 代码规范
- 后端遵循PEP 8
- 前端使用ESLint + Prettier
- 提交信息遵循Conventional Commits

### 文件命名
- 使用小写字母和下划线（Python）
- 使用驼峰命名（TypeScript）
- 组件使用PascalCase

### 注释规范
- 函数和类添加文档字符串
- 复杂逻辑添加行内注释
- API接口添加详细说明

这个项目结构为AI测试管理系统提供了完整的技术架构，支持快速开发和部署。
