// 全局样式文件
@use './variables.scss' as vars;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 主题色
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  // 眼睛保护色
  --bg-color: #f2f8ea;
  --bg-color-light: #f8fdf5;
  --bg-color-dark: #e8f4dc;
}

// 布局样式
.layout-container {
  height: 100vh;
  background-color: var(--bg-color);
}

.main-content {
  background-color: var(--bg-color);
  min-height: calc(100vh - 60px);
  padding: 20px;
}

// 卡片样式
.custom-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

// 按钮样式
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

// 表格样式
.el-table {
  background-color: white;
}

.el-table th {
  background-color: var(--bg-color-light);
}

// 表单样式
.form-container {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 响应式
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
  }
}
