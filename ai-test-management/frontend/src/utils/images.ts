// 图片资源路径常量

export const IMAGES = {
  // Logo相关
  LOGO: '/logo.svg',
  LOGO_MINI: '/logo-mini.svg',
  FAVICON: '/favicon.svg',
  
  // 背景图片
  LOGIN_BG: '/images/login-bg.svg',
  DASHBOARD_BG: '/images/dashboard-bg.svg',
  
  // 状态插图
  EMPTY_STATE: '/images/empty-state.svg',
  ERROR_404: '/images/404.svg',
  SUCCESS: '/images/success.svg',
  
  // AI相关
  AI_PROCESSING: '/images/ai-processing.svg',
} as const

// 图片预加载函数
export const preloadImages = (imageUrls: string[]): Promise<void[]> => {
  const promises = imageUrls.map((url) => {
    return new Promise<void>((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
      img.src = url
    })
  })
  
  return Promise.all(promises)
}

// 预加载所有关键图片
export const preloadCriticalImages = (): Promise<void[]> => {
  const criticalImages = [
    IMAGES.LOGO,
    IMAGES.LOGO_MINI,
    IMAGES.AI_PROCESSING,
    IMAGES.EMPTY_STATE
  ]
  
  return preloadImages(criticalImages)
}

// 获取图片URL的辅助函数
export const getImageUrl = (imageName: keyof typeof IMAGES): string => {
  return IMAGES[imageName]
}

// 图片懒加载指令
export const lazyLoadDirective = {
  mounted(el: HTMLImageElement, binding: { value: string }) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          el.src = binding.value
          observer.unobserve(el)
        }
      })
    })
    
    observer.observe(el)
  }
}
