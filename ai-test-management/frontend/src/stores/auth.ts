import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, getCurrentUser } from '@/api/auth'
import type { LoginForm, User } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const user = ref<User | null>(null)

  const isAuthenticated = computed(() => !!token.value)

  const login = async (loginForm: LoginForm) => {
    try {
      const response = await apiLogin(loginForm)
      token.value = response.access_token
      localStorage.setItem('token', response.access_token)
      
      // 获取用户信息
      await fetchUserInfo()
      
      return response
    } catch (error) {
      throw error
    }
  }

  const logout = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
  }

  const fetchUserInfo = async () => {
    try {
      const userInfo = await getCurrentUser()
      user.value = userInfo
      return userInfo
    } catch (error) {
      logout()
      throw error
    }
  }

  const initAuth = async () => {
    console.log('Initializing auth, token:', token.value)
    if (token.value) {
      try {
        await fetchUserInfo()
        console.log('Auth initialized successfully, user:', user.value)
      } catch (error) {
        console.log('Token validation failed, logging out:', error)
        logout()
      }
    } else {
      console.log('No token found')
    }
  }

  return {
    token,
    user,
    isAuthenticated,
    login,
    logout,
    fetchUserInfo,
    initAuth
  }
})
