import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, getCurrentUser } from '@/api/auth'
import type { LoginForm, User } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>('')
  const user = ref<User | null>(null)

  const isAuthenticated = computed(() => !!token.value && !!user.value)

  const login = async (loginForm: LoginForm) => {
    try {
      console.log('Attempting login with:', loginForm.username)
      const response = await apiLogin(loginForm)
      console.log('Login response:', response)

      token.value = response.access_token
      localStorage.setItem('token', response.access_token)
      console.log('Token saved to localStorage:', response.access_token)

      // 获取用户信息
      await fetchUserInfo()
      console.log('Login completed, user:', user.value)

      return response
    } catch (error) {
      console.log('Login failed:', error)
      throw error
    }
  }

  const logout = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
  }

  const fetchUserInfo = async () => {
    try {
      const userInfo = await getCurrentUser()
      user.value = userInfo
      return userInfo
    } catch (error) {
      logout()
      throw error
    }
  }

  const initAuth = async () => {
    const savedToken = localStorage.getItem('token')
    console.log('Initializing auth, saved token:', savedToken)

    if (savedToken) {
      token.value = savedToken
      try {
        await fetchUserInfo()
        console.log('Auth initialized successfully, user:', user.value)
      } catch (error) {
        console.log('Token validation failed, logging out:', error)
        logout()
      }
    } else {
      console.log('No token found in localStorage')
    }
  }

  return {
    token,
    user,
    isAuthenticated,
    login,
    logout,
    fetchUserInfo,
    initAuth
  }
})
