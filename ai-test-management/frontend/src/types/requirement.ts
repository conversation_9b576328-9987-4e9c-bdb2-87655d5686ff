// 需求相关类型定义

export interface Requirement {
  id: number
  name: string
  description: string
  category: RequirementCategory
  parent?: string
  module?: string
  level: RequirementLevel
  reviewer: string
  estimated: number
  criteria?: string
  remark?: string
  keywords?: string
  project_id: number
  reviewer_id?: number
  created_at: string
  updated_at?: string
}

export interface RequirementCreate {
  name: string
  description: string
  category: RequirementCategory
  parent?: string
  module?: string
  level?: RequirementLevel
  reviewer: string
  estimated?: number
  criteria?: string
  remark?: string
  keywords?: string
  project_id: number
}

export interface RequirementUpdate {
  name?: string
  description?: string
  category?: RequirementCategory
  parent?: string
  module?: string
  level?: RequirementLevel
  reviewer?: string
  estimated?: number
  criteria?: string
  remark?: string
  keywords?: string
}

export interface RequirementQuery {
  skip?: number
  limit?: number
  search?: string
  project_id?: number
}

export enum RequirementCategory {
  FUNCTIONAL = "功能",
  PERFORMANCE = "性能",
  SECURITY = "安全",
  INTERFACE = "接口",
  EXPERIENCE = "体验",
  IMPROVEMENT = "改进",
  OTHER = "其它"
}

export enum RequirementLevel {
  BR = "BR"
}
