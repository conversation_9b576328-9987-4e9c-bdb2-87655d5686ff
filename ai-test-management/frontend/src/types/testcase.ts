// 测试用例相关类型定义

export interface TestCase {
  id: number
  title: string
  description?: string
  priority: TestCasePriority
  status: TestCaseStatus
  test_type: TestCaseType
  preconditions?: string
  postconditions?: string
  creator: string
  requirement_id: number
  project_id: number
  creator_id?: number
  created_at: string
  updated_at?: string
  steps: TestStep[]
}

export interface TestCaseCreate {
  title: string
  description?: string
  priority: TestCasePriority
  status?: TestCaseStatus
  test_type: TestCaseType
  preconditions?: string
  postconditions?: string
  creator: string
  requirement_id: number
  project_id: number
  steps?: TestStepBase[]
}

export interface TestCaseUpdate {
  title?: string
  description?: string
  priority?: TestCasePriority
  status?: TestCaseStatus
  test_type?: TestCaseType
  preconditions?: string
  postconditions?: string
}

export interface TestCaseQuery {
  skip?: number
  limit?: number
  search?: string
  project_id?: number
  requirement_id?: number
}

export interface TestStep {
  id: number
  step_number: number
  description: string
  expected_result: string
  testcase_id: number
  created_at: string
}

export interface TestStepBase {
  description: string
  expected_result: string
}

export enum TestCasePriority {
  HIGH = "高",
  MEDIUM = "中",
  LOW = "低"
}

export enum TestCaseStatus {
  NOT_STARTED = "未开始",
  IN_PROGRESS = "进行中",
  PASSED = "通过",
  FAILED = "失败",
  BLOCKED = "阻塞"
}

export enum TestCaseType {
  UNIT = "单元测试",
  INTERFACE = "接口测试",
  FUNCTIONAL = "功能测试",
  PERFORMANCE = "性能测试",
  SECURITY = "安全测试"
}
