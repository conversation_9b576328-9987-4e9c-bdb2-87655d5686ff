<template>
  <div class="api-automation">
    <h1 class="page-title">接口自动化测试</h1>
    
    <el-card>
      <div class="demo-content">
        <div class="demo-illustration">
          <img src="/images/empty-state.svg" alt="开发中" class="demo-image">
        </div>
        <el-result
          icon="info"
          title="接口自动化测试"
          sub-title="此功能正在开发中，敬请期待"
        >
          <template #extra>
            <el-button type="primary">了解更多</el-button>
          </template>
        </el-result>
        
        <div class="feature-list">
          <h3>即将支持的功能：</h3>
          <ul>
            <li>RESTful API测试</li>
            <li>GraphQL API测试</li>
            <li>接口性能测试</li>
            <li>数据驱动测试</li>
            <li>接口文档自动生成</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 接口自动化测试Demo页面
</script>

<style scoped>
.api-automation {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 24px;
}

.demo-content {
  text-align: center;
  padding: 40px;
}

.feature-list {
  margin-top: 40px;
  text-align: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.feature-list h3 {
  color: #303133;
  margin-bottom: 16px;
}

.feature-list ul {
  list-style-type: disc;
  padding-left: 20px;
}

.feature-list li {
  margin-bottom: 8px;
  color: #606266;
}

.demo-illustration {
  margin-bottom: 20px;
}

.demo-image {
  width: 200px;
  height: 150px;
  opacity: 0.8;
}
</style>
