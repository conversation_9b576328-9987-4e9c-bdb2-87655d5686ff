<template>
  <div class="dashboard-container">
    <h1 class="page-title">仪表盘</h1>
    
    <!-- 概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon projects">
              <el-icon size="32"><FolderOpened /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_projects || 0 }}</div>
              <div class="card-label">项目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon requirements">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_requirements || 0 }}</div>
              <div class="card-label">需求总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon testcases">
              <el-icon size="32"><Files /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_testcases || 0 }}</div>
              <div class="card-label">用例总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon defects">
              <el-icon size="32"><Warning /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-number">{{ stats?.overview.total_defects || 0 }}</div>
              <div class="card-label">缺陷总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>测试用例状态分布</span>
          </template>
          <div class="chart-container">
            <v-chart :option="testcaseStatusOption" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>需求类别分布</span>
          </template>
          <div class="chart-container">
            <v-chart :option="requirementCategoryOption" />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近项目 -->
    <el-card class="recent-projects">
      <template #header>
        <span>最近项目</span>
      </template>
      <el-table :data="stats?.recent_projects" style="width: 100%">
        <el-table-column prop="project_code" label="项目编号" width="120" />
        <el-table-column prop="name" label="项目名称" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewProject(row.id)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import VChart from 'vue-echarts'
import 'echarts'
import { getDashboardStats } from '@/api/dashboard'
import type { DashboardStats } from '@/types/dashboard'

const router = useRouter()
const stats = ref<DashboardStats>()

const testcaseStatusOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      name: '测试用例状态',
      type: 'pie',
      radius: '50%',
      data: stats.value?.testcase_stats.by_status.map(item => ({
        value: item.count,
        name: item.status
      })) || []
    }
  ]
}))

const requirementCategoryOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'category',
    data: stats.value?.requirement_stats.by_category.map(item => item.category) || []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '需求数量',
      type: 'bar',
      data: stats.value?.requirement_stats.by_category.map(item => item.count) || []
    }
  ]
}))

const fetchStats = async () => {
  try {
    stats.value = await getDashboardStats()
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const viewProject = (projectId: number) => {
  router.push(`/projects/${projectId}`)
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-image: url('/images/dashboard-bg.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: calc(100vh - 60px);
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 24px;
}

.overview-cards {
  margin-bottom: 24px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.card-icon.projects {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.requirements {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.testcases {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.defects {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.recent-projects {
  margin-bottom: 24px;
}
</style>
