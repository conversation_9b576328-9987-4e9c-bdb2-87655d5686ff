<template>
  <div class="testcase-generation">
    <h1 class="page-title">生成测试用例</h1>
    
    <el-card class="generation-card">
      <template #header>
        <span>AI测试用例生成</span>
      </template>
      
      <el-form :model="generationForm" label-width="120px">
        <el-form-item label="选择需求">
          <el-select v-model="generationForm.requirements" multiple placeholder="请选择需求" style="width: 100%">
            <el-option
              v-for="req in requirements"
              :key="req.id"
              :label="req.name"
              :value="req.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="生成策略">
          <el-radio-group v-model="generationForm.strategy">
            <el-radio label="comprehensive">全面覆盖</el-radio>
            <el-radio label="focused">重点关注</el-radio>
            <el-radio label="boundary">边界测试</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="generateTestCases" :loading="generating">
            <el-icon><Magic /></el-icon>
            AI生成测试用例
          </el-button>
        </el-form-item>
      </el-form>

      <!-- AI处理中的动画 -->
      <div v-if="generating" class="ai-processing">
        <div class="ai-animation">
          <img src="/images/ai-processing.svg" alt="AI处理中" class="ai-icon">
        </div>
        <p class="processing-text">AI正在生成测试用例，请稍候...</p>
      </div>
    </el-card>
    
    <!-- 生成结果 -->
    <el-card v-if="generatedTestCases.length > 0" class="result-card">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>生成的测试用例 ({{ generatedTestCases.length }}条)</span>
          <el-button type="success" @click="saveTestCases">
            <el-icon><Check /></el-icon>
            保存用例
          </el-button>
        </div>
      </template>
      
      <div class="testcase-list">
        <div v-for="(testcase, index) in generatedTestCases" :key="index" class="testcase-item">
          <h4>{{ testcase.title }}</h4>
          <p><strong>描述：</strong>{{ testcase.description }}</p>
          <p><strong>优先级：</strong><el-tag :type="getPriorityType(testcase.priority)">{{ testcase.priority }}</el-tag></p>
          <p><strong>测试类型：</strong>{{ testcase.test_type }}</p>
          <div v-if="testcase.steps && testcase.steps.length > 0">
            <strong>测试步骤：</strong>
            <ol>
              <li v-for="(step, stepIndex) in testcase.steps" :key="stepIndex">
                {{ step.description }}
                <br><small>预期结果：{{ step.expected_result }}</small>
              </li>
            </ol>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const generating = ref(false)
const requirements = ref<any[]>([])
const generatedTestCases = ref<any[]>([])

const generationForm = reactive({
  requirements: [] as number[],
  strategy: 'comprehensive'
})

const fetchRequirements = async () => {
  // 模拟数据
  requirements.value = [
    { id: 1, name: '用户登录功能' },
    { id: 2, name: '数据展示功能' },
    { id: 3, name: '权限管理功能' }
  ]
}

const generateTestCases = async () => {
  if (generationForm.requirements.length === 0) {
    ElMessage.warning('请选择需求')
    return
  }
  
  generating.value = true
  
  // 模拟AI生成过程
  setTimeout(() => {
    generatedTestCases.value = [
      {
        title: '验证用户正常登录',
        description: '验证用户使用正确的用户名和密码能够成功登录系统',
        priority: '高',
        test_type: '功能测试',
        steps: [
          { description: '打开登录页面', expected_result: '页面正常显示' },
          { description: '输入正确的用户名和密码', expected_result: '输入框正常显示内容' },
          { description: '点击登录按钮', expected_result: '成功跳转到主页面' }
        ]
      },
      {
        title: '验证用户密码错误登录失败',
        description: '验证用户使用错误密码无法登录系统',
        priority: '高',
        test_type: '功能测试',
        steps: [
          { description: '打开登录页面', expected_result: '页面正常显示' },
          { description: '输入正确用户名和错误密码', expected_result: '输入框正常显示内容' },
          { description: '点击登录按钮', expected_result: '显示密码错误提示' }
        ]
      }
    ]
    generating.value = false
    ElMessage.success('测试用例生成完成')
  }, 3000)
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return typeMap[priority] || 'info'
}

const saveTestCases = () => {
  ElMessage.success('测试用例保存成功')
}

onMounted(() => {
  fetchRequirements()
})
</script>

<style scoped>
.testcase-generation {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 24px;
}

.generation-card {
  margin-bottom: 24px;
}

.result-card {
  margin-bottom: 24px;
}

.testcase-list {
  max-height: 600px;
  overflow-y: auto;
}

.testcase-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.testcase-item h4 {
  color: #303133;
  margin-bottom: 8px;
}

.testcase-item p {
  margin-bottom: 8px;
  color: #606266;
}

.testcase-item ol {
  margin-top: 8px;
  padding-left: 20px;
}

.testcase-item li {
  margin-bottom: 4px;
}

.ai-processing {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  margin: 20px 0;
}

.ai-animation {
  margin-bottom: 16px;
}

.ai-icon {
  width: 120px;
  height: 120px;
  animation: pulse 2s infinite;
}

.processing-text {
  font-size: 16px;
  color: #606266;
  margin: 0;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
