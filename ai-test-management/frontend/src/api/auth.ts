import request from './request'
import type { <PERSON>ginForm, LoginResponse, User } from '@/types/auth'

// 用户登录
export const login = (data: LoginForm): Promise<LoginResponse> => {
  const formData = new FormData()
  formData.append('username', data.username)
  formData.append('password', data.password)
  
  return request({
    url: '/auth/login',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取当前用户信息
export const getCurrentUser = (): Promise<User> => {
  return request({
    url: '/auth/me',
    method: 'get'
  })
}
