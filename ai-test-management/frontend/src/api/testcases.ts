import request from './request'
import type { TestCase, TestCaseCreate, TestCaseUpdate, TestCaseQuery } from '@/types/testcase'

// 获取测试用例列表
export const getTestCases = (params?: TestCaseQuery): Promise<TestCase[]> => {
  return request({
    url: '/testcases',
    method: 'get',
    params
  })
}

// 创建测试用例
export const createTestCase = (data: TestCaseCreate): Promise<TestCase> => {
  return request({
    url: '/testcases',
    method: 'post',
    data
  })
}

// 获取测试用例详情
export const getTestCase = (id: number): Promise<TestCase> => {
  return request({
    url: `/testcases/${id}`,
    method: 'get'
  })
}

// 更新测试用例
export const updateTestCase = (id: number, data: TestCaseUpdate): Promise<TestCase> => {
  return request({
    url: `/testcases/${id}`,
    method: 'put',
    data
  })
}

// 删除测试用例
export const deleteTestCase = (id: number): Promise<void> => {
  return request({
    url: `/testcases/${id}`,
    method: 'delete'
  })
}

// 根据需求ID获取测试用例
export const getTestCasesByRequirement = (requirementId: number): Promise<TestCase[]> => {
  return request({
    url: `/testcases/by-requirement/${requirementId}`,
    method: 'get'
  })
}
