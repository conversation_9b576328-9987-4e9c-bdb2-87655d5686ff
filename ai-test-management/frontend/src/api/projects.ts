import request from './request'
import type { Project, ProjectCreate, ProjectUpdate, ProjectQuery } from '@/types/project'

// 获取项目列表
export const getProjects = (params?: ProjectQuery): Promise<Project[]> => {
  return request({
    url: '/projects/',
    method: 'get',
    params
  })
}

// 创建项目
export const createProject = (data: ProjectCreate): Promise<Project> => {
  return request({
    url: '/projects/',
    method: 'post',
    data
  })
}

// 获取项目详情
export const getProject = (id: number): Promise<Project> => {
  return request({
    url: `/projects/${id}`,
    method: 'get'
  })
}

// 更新项目
export const updateProject = (id: number, data: ProjectUpdate): Promise<Project> => {
  return request({
    url: `/projects/${id}`,
    method: 'put',
    data
  })
}

// 删除项目
export const deleteProject = (id: number): Promise<void> => {
  return request({
    url: `/projects/${id}`,
    method: 'delete'
  })
}
