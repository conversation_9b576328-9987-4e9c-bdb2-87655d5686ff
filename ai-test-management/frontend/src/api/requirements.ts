import request from './request'
import type { Requirement, RequirementCreate, RequirementUpdate, RequirementQuery } from '@/types/requirement'

// 获取需求列表
export const getRequirements = (params?: RequirementQuery): Promise<Requirement[]> => {
  return request({
    url: '/requirements',
    method: 'get',
    params
  })
}

// 创建需求
export const createRequirement = (data: RequirementCreate): Promise<Requirement> => {
  return request({
    url: '/requirements',
    method: 'post',
    data
  })
}

// 获取需求详情
export const getRequirement = (id: number): Promise<Requirement> => {
  return request({
    url: `/requirements/${id}`,
    method: 'get'
  })
}

// 更新需求
export const updateRequirement = (id: number, data: RequirementUpdate): Promise<Requirement> => {
  return request({
    url: `/requirements/${id}`,
    method: 'put',
    data
  })
}

// 删除需求
export const deleteRequirement = (id: number): Promise<void> => {
  return request({
    url: `/requirements/${id}`,
    method: 'delete'
  })
}

// 上传需求文档
export const uploadRequirementFile = (file: File): Promise<any> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/requirements/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
