<template>
  <div class="success-result">
    <div class="success-illustration">
      <img src="/images/success.svg" alt="成功" class="success-image">
    </div>
    
    <div class="success-content">
      <h3 class="success-title">{{ title }}</h3>
      <p class="success-description" v-if="description">{{ description }}</p>
      
      <div class="success-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  description?: string
}

defineProps<Props>()
</script>

<style scoped>
.success-result {
  text-align: center;
  padding: 40px 20px;
}

.success-illustration {
  margin-bottom: 24px;
}

.success-image {
  width: 200px;
  height: 200px;
  max-width: 100%;
}

.success-title {
  font-size: 24px;
  color: #67c23a;
  margin-bottom: 12px;
  font-weight: 600;
}

.success-description {
  font-size: 16px;
  color: #606266;
  margin-bottom: 24px;
  line-height: 1.6;
}

.success-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .success-image {
    width: 150px;
    height: 150px;
  }
  
  .success-title {
    font-size: 20px;
  }
  
  .success-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
