<template>
  <div class="loading-container" v-if="visible">
    <div class="loading-content">
      <div class="loading-animation">
        <img src="/images/ai-processing.svg" alt="加载中" class="loading-icon">
      </div>
      <p class="loading-text">{{ text }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible?: boolean
  text?: string
}

withDefaults(defineProps<Props>(), {
  visible: false,
  text: '加载中...'
})
</script>

<style scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.loading-animation {
  margin-bottom: 16px;
}

.loading-icon {
  width: 80px;
  height: 80px;
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: 16px;
  color: #606266;
  margin: 0;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
