// 常量定义

// 需求类别选项
export const REQUIREMENT_CATEGORIES = [
  { label: '功能', value: '功能', color: 'primary' },
  { label: '性能', value: '性能', color: 'success' },
  { label: '安全', value: '安全', color: 'warning' },
  { label: '接口', value: '接口', color: 'info' },
  { label: '体验', value: '体验', color: 'danger' },
  { label: '改进', value: '改进', color: 'warning' },
  { label: '其它', value: '其它', color: 'info' }
]

// 需求级别选项
export const REQUIREMENT_LEVELS = [
  { label: 'BR', value: 'BR' }
]

// 测试用例优先级选项
export const TESTCASE_PRIORITIES = [
  { label: '高', value: '高', color: 'danger' },
  { label: '中', value: '中', color: 'warning' },
  { label: '低', value: '低', color: 'info' }
]

// 测试用例状态选项
export const TESTCASE_STATUSES = [
  { label: '未开始', value: '未开始', color: 'info' },
  { label: '进行中', value: '进行中', color: 'warning' },
  { label: '通过', value: '通过', color: 'success' },
  { label: '失败', value: '失败', color: 'danger' },
  { label: '阻塞', value: '阻塞', color: 'warning' }
]

// 测试用例类型选项
export const TESTCASE_TYPES = [
  { label: '单元测试', value: '单元测试' },
  { label: '接口测试', value: '接口测试' },
  { label: '功能测试', value: '功能测试' },
  { label: '性能测试', value: '性能测试' },
  { label: '安全测试', value: '安全测试' }
]

// 文件类型限制
export const ALLOWED_FILE_TYPES = {
  document: ['.txt', '.docx', '.pdf'],
  image: ['.jpg', '.jpeg', '.png', '.gif'],
  excel: ['.xlsx', '.xls', '.csv']
}

// 文件大小限制（字节）
export const FILE_SIZE_LIMITS = {
  document: 10 * 1024 * 1024, // 10MB
  image: 5 * 1024 * 1024,     // 5MB
  excel: 20 * 1024 * 1024     // 20MB
}

// 分页配置
export const PAGINATION = {
  defaultPageSize: 20,
  pageSizes: [10, 20, 50, 100]
}

// 表格配置
export const TABLE_CONFIG = {
  stripe: true,
  border: true,
  size: 'default',
  emptyText: '暂无数据'
}

// 主题色配置
export const THEME_COLORS = {
  primary: '#409eff',
  success: '#67c23a',
  warning: '#e6a23c',
  danger: '#f56c6c',
  info: '#909399'
}

// 路由白名单（不需要登录的页面）
export const ROUTE_WHITE_LIST = ['/login', '/register', '/forgot-password']

// 本地存储键名
export const STORAGE_KEYS = {
  token: 'ai_test_token',
  user: 'ai_test_user',
  settings: 'ai_test_settings',
  theme: 'ai_test_theme'
}

// API响应状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
}

// WebSocket消息类型
export const WS_MESSAGE_TYPES = {
  REQUIREMENT_ANALYSIS: 'requirement_analysis',
  TESTCASE_GENERATION: 'testcase_generation',
  ERROR: 'error'
}

// 正则表达式
export const REGEX_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^1[3-9]\d{9}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  username: /^[a-zA-Z0-9_]{3,20}$/,
  projectCode: /^[A-Z0-9_]{2,20}$/
}
