import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘', icon: 'DataBoard' }
      },
      {
        path: '/projects',
        name: 'Projects',
        component: () => import('@/views/Projects/index.vue'),
        meta: { title: '项目管理', icon: 'FolderOpened' }
      },
      {
        path: '/requirements',
        name: 'Requirements',
        redirect: '/requirements/analysis',
        meta: { title: '需求管理', icon: 'Document' },
        children: [
          {
            path: '/requirements/analysis',
            name: 'RequirementAnalysis',
            component: () => import('@/views/Requirements/Analysis.vue'),
            meta: { title: '需求分析', icon: 'EditPen' }
          },
          {
            path: '/requirements/list',
            name: 'RequirementList',
            component: () => import('@/views/Requirements/List.vue'),
            meta: { title: '需求列表', icon: 'List' }
          }
        ]
      },
      {
        path: '/testcases',
        name: 'TestCases',
        redirect: '/testcases/generation',
        meta: { title: '用例管理', icon: 'Files' },
        children: [
          {
            path: '/testcases/generation',
            name: 'TestCaseGeneration',
            component: () => import('@/views/TestCases/Generation.vue'),
            meta: { title: '生成测试用例', icon: 'Magic' }
          },
          {
            path: '/testcases/management',
            name: 'TestCaseManagement',
            component: () => import('@/views/TestCases/Management.vue'),
            meta: { title: '测试用例管理', icon: 'Management' }
          }
        ]
      },
      {
        path: '/automation',
        name: 'Automation',
        redirect: '/automation/ui',
        meta: { title: '自动化测试', icon: 'Setting' },
        children: [
          {
            path: '/automation/ui',
            name: 'UIAutomation',
            component: () => import('@/views/Automation/UI.vue'),
            meta: { title: 'UI自动化', icon: 'Monitor' }
          },
          {
            path: '/automation/api',
            name: 'APIAutomation',
            component: () => import('@/views/Automation/API.vue'),
            meta: { title: '接口自动化', icon: 'Connection' }
          },
          {
            path: '/automation/performance',
            name: 'PerformanceTest',
            component: () => import('@/views/Automation/Performance.vue'),
            meta: { title: '性能测试', icon: 'TrendCharts' }
          },
          {
            path: '/automation/security',
            name: 'SecurityTest',
            component: () => import('@/views/Automation/Security.vue'),
            meta: { title: '安全测试', icon: 'Lock' }
          }
        ]
      },
      {
        path: '/defects',
        name: 'Defects',
        component: () => import('@/views/Defects/index.vue'),
        meta: { title: '缺陷管理', icon: 'Warning' }
      },
      {
        path: '/reports',
        name: 'Reports',
        component: () => import('@/views/Reports/index.vue'),
        meta: { title: '测试报告', icon: 'DataAnalysis' }
      },
      {
        path: '/settings',
        name: 'Settings',
        redirect: '/settings/users',
        meta: { title: '系统设置', icon: 'Tools' },
        children: [
          {
            path: '/settings/users',
            name: 'UserManagement',
            component: () => import('@/views/Settings/Users.vue'),
            meta: { title: '用户管理', icon: 'User' }
          }
        ]
      }
    ]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  console.log('Route guard:', {
    path: to.path,
    requiresAuth: to.meta.requiresAuth,
    isAuthenticated: authStore.isAuthenticated,
    token: authStore.token
  })

  if (to.meta.requiresAuth !== false && !authStore.isAuthenticated) {
    console.log('Redirecting to login - not authenticated')
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    console.log('Redirecting to dashboard - already authenticated')
    next('/dashboard')
  } else {
    console.log('Allowing navigation')
    next()
  }
})

export default router
