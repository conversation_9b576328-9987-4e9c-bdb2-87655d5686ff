{"name": "sass", "description": "A pure JavaScript implementation of Sass.", "license": "MIT", "bugs": "https://github.com/sass/dart-sass/issues", "homepage": "https://github.com/sass/dart-sass", "repository": {"type": "git", "url": "https://github.com/sass/dart-sass"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nex3"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}, "keywords": ["style", "scss", "sass", "preprocessor", "css"], "types": "types/index.d.ts", "exports": {"types": "./types/index.d.ts", "node": {"require": "./sass.node.js", "default": "./sass.node.mjs"}, "default": {"require": "./sass.default.cjs", "default": "./sass.default.js"}}, "version": "1.89.1", "bin": {"sass": "sass.js"}, "main": "sass.node.js"}