{"el-affix": {"attributes": ["offset", "position", "target", "z-index", "change", "scroll"], "description": "Fix the element to a specific visible area.\n\n[Docs](https://element-plus.org/en-US/component/affix.html#affix)"}, "el-alert": {"attributes": ["title", "type", "description", "closable", "center", "close-text", "show-icon", "effect", "close"], "description": "Displays important alert messages.\n\n[Docs](https://element-plus.org/en-US/component/alert.html#alert)"}, "el-anchor": {"attributes": ["change", "click"], "description": "Through the anchor point, you can quickly find the position of the information content on the current page.\n\n[Docs](https://element-plus.org/en-US/component/anchor.html#anchor)"}, "el-anchor-link": {"description": "[Docs](https://element-plus.org/en-US/component/anchor.html#anchorlink)"}, "el-autocomplete": {"attributes": ["model-value", "placeholder", "clearable", "disabled", "value-key", "debounce", "placement", "fetch-suggestions", "trigger-on-focus", "select-when-unmatched", "name", "aria-label", "hide-loading", "popper-class", "teleported", "append-to", "highlight-first-item", "fit-input-width", "popper-append-to-body", "blur", "focus", "input", "clear", "select", "change"], "description": "Get some recommended tips based on the current input.\n\n[Docs](https://element-plus.org/en-US/component/autocomplete.html#autocomplete)"}, "el-avatar": {"attributes": ["icon", "size", "shape", "src", "src-set", "alt", "fit", "error"], "description": "Avatars can be used to represent people or objects. It supports images, Icons, or characters.\n\n[Docs](https://element-plus.org/en-US/component/avatar.html#avatar)"}, "el-backtop": {"attributes": ["target", "visibility-height", "right", "bottom", "click"], "description": "A button to back to top.\n\n[Docs](https://element-plus.org/en-US/component/backtop.html#backtop)"}, "el-badge": {"attributes": ["value", "max", "is-dot", "hidden", "type", "show-zero", "color", "offset", "badge-style", "badge-class"], "description": "A number or status mark on buttons and icons.\n\n[Docs](https://element-plus.org/en-US/component/badge.html#badge)"}, "el-breadcrumb": {"attributes": ["separator", "separator-icon"], "subtags": ["el-breadcrumb-item"], "description": "Displays the location of the current page, making it easier to browser back.\n\n[Docs](https://element-plus.org/en-US/component/breadcrumb.html#breadcrumb)"}, "el-breadcrumb-item": {"attributes": ["to", "replace"], "description": "[Docs](https://element-plus.org/en-US/component/breadcrumb.html#breadcrumbitem)"}, "el-button": {"attributes": ["size", "type", "plain", "text", "bg", "link", "round", "circle", "loading", "loading-icon", "disabled", "icon", "autofocus", "native-type", "auto-insert-space", "color", "dark", "tag"], "description": "Commonly used button.\n\n[Docs](https://element-plus.org/en-US/component/button.html#button)"}, "el-button-group": {"attributes": ["size", "type"], "subtags": ["el-button"], "description": "[Docs](https://element-plus.org/en-US/component/button.html#buttongroup)"}, "el-calendar": {"attributes": ["model-value", "range"], "description": "Display date.\n\n[Docs](https://element-plus.org/en-US/component/calendar.html#calendar)"}, "el-card": {"attributes": ["header", "footer", "body-style", "header-class", "body-class", "footer-class", "shadow"], "description": "Integrate information in a card container.\n\n[Docs](https://element-plus.org/en-US/component/card.html#card)"}, "el-carousel": {"attributes": ["height", "initial-index", "trigger", "autoplay", "interval", "indicator-position", "arrow", "type", "card-scale", "loop", "direction", "pause-on-hover", "motion-blur", "change"], "subtags": ["el-carousel-item"], "description": "Loop a series of images or texts in a limited space\n\n[Docs](https://element-plus.org/en-US/component/carousel.html#carousel)"}, "el-carousel-item": {"attributes": ["name", "label"], "description": "[Docs](https://element-plus.org/en-US/component/carousel.html#carousel-item)"}, "el-cascader": {"attributes": ["model-value", "options", "props", "size", "placeholder", "disabled", "clearable", "show-all-levels", "collapse-tags", "collapse-tags-tooltip", "separator", "filterable", "filter-method", "debounce", "before-filter", "popper-class", "teleported", "tag-type", "tag-effect", "validate-event", "max-collapse-tags", "empty-values", "value-on-clear", "persistent", "fallback-placements", "placement", "popper-append-to-body", "change", "expand-change", "blur", "focus", "clear", "visible-change", "remove-tag"], "description": "If the options have a clear hierarchical structure, Cascader can be used to view and select them.\n\n[Docs](https://element-plus.org/en-US/component/cascader.html#cascader)"}, "el-cascader-panel": {"attributes": ["model-value", "options", "props", "change", "expand-change", "close"], "description": "[Docs](https://element-plus.org/en-US/component/cascader.html#cascaderpanel)"}, "el-checkbox": {"attributes": ["model-value", "value", "label", "true-value", "false-value", "disabled", "border", "size", "name", "checked", "indeterminate", "validate-event", "tabindex", "id", "aria-controls", "true-label", "false-label", "controls", "change"], "description": "A group of options for multiple choices.\n\n[Docs](https://element-plus.org/en-US/component/checkbox.html#checkbox)"}, "el-checkbox-group": {"attributes": ["model-value", "size", "disabled", "min", "max", "aria-label", "text-color", "fill", "tag", "validate-event", "label", "change"], "subtags": ["el-checkbox", "el-checkbox-button"], "description": "[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxgroup)"}, "el-checkbox-button": {"attributes": ["value", "label", "true-value", "false-value", "disabled", "name", "checked", "true-label", "false-label"], "description": "[Docs](https://element-plus.org/en-US/component/checkbox.html#checkboxbutton)"}, "el-collapse": {"attributes": ["model-value", "accordion", "expand-icon-position", "before-collapse", "change"], "subtags": ["el-collapse-item"], "description": "Use Collapse to store contents.\n\n[Docs](https://element-plus.org/en-US/component/collapse.html#collapse)"}, "el-collapse-item": {"attributes": ["name", "title", "icon", "disabled"], "description": "[Docs](https://element-plus.org/en-US/component/collapse.html#collapse-item)"}, "el-color-picker": {"attributes": ["model-value", "disabled", "size", "show-alpha", "color-format", "popper-class", "predefine", "validate-event", "tabindex", "aria-label", "id", "teleported", "label", "change", "active-change", "focus", "blur"], "description": "ColorPicker is a color selector supporting multiple color formats.\n\n[Docs](https://element-plus.org/en-US/component/color-picker.html#colorpicker)"}, "el-config-provider": {"attributes": ["locale", "size", "z-index", "namespace", "button", "link", "message", "experimental-features", "empty-values", "value-on-clear"], "description": "Config Provider is used for providing global configurations, which enables your entire application to access these configurations everywhere.\n\n[Docs](https://element-plus.org/en-US/component/config-provider.html#config-provider)"}, "el-container": {"attributes": ["direction"], "subtags": ["el-container", "el-header", "el-aside", "el-main", "el-footer"], "description": "Container components for scaffolding basic structure of the page:\n\n[Docs](https://element-plus.org/en-US/component/container.html#container)"}, "el-header": {"attributes": ["height"], "description": "[Docs](https://element-plus.org/en-US/component/container.html#header)"}, "el-aside": {"attributes": ["width"], "description": "[Docs](https://element-plus.org/en-US/component/container.html#aside)"}, "el-main": {"description": "[Docs](https://element-plus.org/en-US/component/container.html#main)"}, "el-footer": {"attributes": ["height"], "description": "[Docs](https://element-plus.org/en-US/component/container.html#footer)"}, "el-date-picker": {"attributes": ["model-value", "readonly", "disabled", "size", "editable", "clearable", "placeholder", "start-placeholder", "end-placeholder", "type", "format", "popper-class", "popper-options", "range-separator", "default-value", "default-time", "value-format", "id", "name", "unlink-panels", "prefix-icon", "clear-icon", "validate-event", "disabled-date", "shortcuts", "cell-class-name", "teleported", "empty-values", "value-on-clear", "fallback-placements", "placement", "change", "blur", "focus", "clear", "calendar-change", "panel-change", "visible-change"], "description": "Use Date Picker for date input.\n\n[Docs](https://element-plus.org/en-US/component/date-picker.html#datepicker)"}, "el-descriptions": {"attributes": ["border", "column", "direction", "size", "title", "extra", "label-width"], "subtags": ["el-descriptions-item"], "description": "Display multiple fields in list form.\n\n[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptions)"}, "el-descriptions-item": {"attributes": ["label", "span", "rowspan", "width", "min-width", "label-width", "align", "label-align", "class-name", "label-class-name"], "description": "[Docs](https://element-plus.org/en-US/component/descriptions.html#descriptionsitem)"}, "el-dialog": {"attributes": ["model-value", "title", "width", "fullscreen", "top", "modal", "modal-class", "header-class", "body-class", "footer-class", "append-to-body", "append-to", "lock-scroll", "open-delay", "close-delay", "close-on-click-modal", "close-on-press-escape", "show-close", "before-close", "draggable", "overflow", "center", "align-center", "destroy-on-close", "close-icon", "z-index", "header-aria-level", "custom-class", "open", "opened", "close", "closed", "open-auto-focus", "close-auto-focus"], "description": "Informs users while preserving the current page state.\n\n[Docs](https://element-plus.org/en-US/component/dialog.html#dialog)"}, "el-divider": {"attributes": ["direction", "border-style", "content-position"], "description": "The dividing line that separates the content.\n\n[Docs](https://element-plus.org/en-US/component/divider.html#divider)"}, "el-drawer": {"attributes": ["model-value", "append-to-body", "append-to", "lock-scroll", "before-close", "close-on-click-modal", "close-on-press-escape", "open-delay", "close-delay", "destroy-on-close", "modal", "direction", "show-close", "size", "title", "with-header", "modal-class", "header-class", "body-class", "footer-class", "z-index", "header-aria-level", "custom-class", "open", "opened", "close", "closed", "open-auto-focus", "close-auto-focus"], "description": "Sometimes, `Dialog` does not always satisfy our requirements, let's say you have a massive form, or you need space to display something like `terms & conditions`, `Drawer` has almost identical API with `Dialog`, but it introduces different user experience.\n\n[Docs](https://element-plus.org/en-US/component/drawer.html#drawer)"}, "el-dropdown": {"attributes": ["type", "size", "button-props", "max-height", "split-button", "disabled", "placement", "trigger", "trigger-keys", "hide-on-click", "show-timeout", "hide-timeout", "role", "tabindex", "popper-class", "popper-options", "teleported", "persistent", "click", "command", "visible-change"], "subtags": ["el-dropdown-menu"], "description": "Toggleable menu for displaying lists of links and actions.\n\n[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown)"}, "el-dropdown-menu": {"subtags": ["el-dropdown-item"], "description": "[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-menu)"}, "el-dropdown-item": {"attributes": ["command", "disabled", "divided", "icon"], "description": "[Docs](https://element-plus.org/en-US/component/dropdown.html#dropdown-item)"}, "el-empty": {"attributes": ["image", "image-size", "description"], "description": "Placeholder hints for empty states.\n\n[Docs](https://element-plus.org/en-US/component/empty.html#empty)"}, "el-form": {"attributes": ["model", "rules", "inline", "label-position", "label-width", "label-suffix", "hide-required-asterisk", "require-asterisk-position", "show-message", "inline-message", "status-icon", "validate-on-rule-change", "size", "disabled", "scroll-to-error", "scroll-into-view-options", "validate"], "subtags": ["el-form-item"], "description": "Form consists of `input`, `radio`, `select`, `checkbox` and so on. With form, you can collect, verify and submit data.\n\n[Docs](https://element-plus.org/en-US/component/form.html#form)"}, "el-form-item": {"attributes": ["prop", "label", "label-position", "label-width", "required", "rules", "error", "show-message", "inline-message", "size", "for", "validate-status"], "description": "[Docs](https://element-plus.org/en-US/component/form.html#formitem)"}, "el-icon": {"attributes": ["color", "size"], "description": "Element Plus provides a set of common icons.\n\n[Docs](https://element-plus.org/en-US/component/icon.html#icon)"}, "el-image": {"attributes": ["src", "fit", "hide-on-click-modal", "loading", "lazy", "scroll-container", "alt", "referrerpolicy", "crossorigin", "preview-src-list", "z-index", "initial-index", "close-on-press-escape", "preview-teleported", "infinite", "zoom-rate", "min-scale", "max-scale", "show-progress", "load", "error", "switch", "close", "show"], "description": "Besides the native features of img, support lazy load, custom placeholder and load failure, etc.\n\n[Docs](https://element-plus.org/en-US/component/image.html#image)"}, "el-image-viewer": {"attributes": ["url-list", "z-index", "initial-index", "infinite", "hide-on-click-modal", "teleported", "zoom-rate", "min-scale", "max-scale", "close-on-press-escape", "show-progress", "close", "switch", "rotate"], "description": "[Docs](https://element-plus.org/en-US/component/image.html#image-viewer)"}, "el-input-number": {"attributes": ["model-value", "min", "max", "step", "step-strictly", "precision", "size", "readonly", "disabled", "controls", "controls-position", "name", "aria-label", "placeholder", "id", "value-on-clear", "validate-event", "label", "change", "blur", "focus"], "description": "Input numerical values with a customizable range.\n\n[Docs](https://element-plus.org/en-US/component/input-number.html#input-number)"}, "el-input-tag": {"attributes": ["model-value", "max", "tag-type", "tag-effect", "trigger", "draggable", "delimiter", "size", "save-on-blur", "clearable", "disabled", "validate-event", "readonly", "autofocus", "id", "tabindex", "maxlength", "minlength", "placeholder", "autocomplete", "aria-label", "change", "input", "add-tag", "remove-tag", "focus", "blur", "clear"], "description": "The InputTag component allows users to add content as tags.\n\n[Docs](https://element-plus.org/en-US/component/input-tag.html#inputtag)"}, "el-input": {"attributes": ["type", "model-value", "maxlength", "minlength", "show-word-limit", "placeholder", "clearable", "formatter", "parser", "show-password", "disabled", "size", "prefix-icon", "suffix-icon", "rows", "autosize", "autocomplete", "name", "readonly", "max", "min", "step", "resize", "autofocus", "form", "aria-label", "tabindex", "validate-event", "input-style", "label", "blur", "focus", "change", "input", "clear"], "description": "Input data using mouse or keyboard.\n\n[Docs](https://element-plus.org/en-US/component/input.html#input)"}, "el-row": {"attributes": ["gutter", "justify", "align", "tag"], "subtags": ["el-col"], "description": "[Docs](https://element-plus.org/en-US/component/layout.html#row)"}, "el-col": {"attributes": ["span", "offset", "push", "pull", "xs", "sm", "md", "lg", "xl", "tag"], "description": "[Docs](https://element-plus.org/en-US/component/layout.html#col)"}, "el-link": {"attributes": ["type", "underline", "disabled", "href", "target", "icon"], "description": "Text hyperlink\n\n[Docs](https://element-plus.org/en-US/component/link.html#link)"}, "el-mention": {"attributes": ["options", "prefix", "split", "filter-option", "placement", "show-arrow", "offset", "whole", "check-is-whole", "loading", "model-value", "popper-class", "popper-options", "[input props]", "search", "select", "[input events]"], "description": "Used to mention someone or something in an input.\n\n[Docs](https://element-plus.org/en-US/component/mention.html#mention)"}, "el-menu": {"attributes": ["mode", "collapse", "ellipsis", "ellipsis-icon", "popper-offset", "default-active", "default-openeds", "unique-opened", "menu-trigger", "router", "collapse-transition", "popper-effect", "close-on-click-outside", "popper-class", "show-timeout", "hide-timeout", "background-color", "text-color", "active-text-color", "persistent", "select", "open", "close"], "subtags": ["el-sub-menu", "el-menu-item", "el-menu-item-group"], "description": "Menu that provides navigation for your website.\n\n[Docs](https://element-plus.org/en-US/component/menu.html#menu)"}, "el-sub-menu": {"attributes": ["index", "popper-class", "show-timeout", "hide-timeout", "disabled", "teleported", "popper-offset", "expand-close-icon", "expand-open-icon", "collapse-close-icon", "collapse-open-icon"], "subtags": ["el-sub-menu", "el-menu-item", "el-menu-item-group"], "description": "[Docs](https://element-plus.org/en-US/component/menu.html#submenu)"}, "el-menu-item": {"attributes": ["index", "route", "disabled", "click"], "description": "[Docs](https://element-plus.org/en-US/component/menu.html#menu-item)"}, "el-menu-item-group": {"attributes": ["title"], "subtags": ["el-menu-item"], "description": "[Docs](https://element-plus.org/en-US/component/menu.html#menu-item-group)"}, "el-page-header": {"attributes": ["icon", "title", "content", "back"], "description": "If path of the page is simple, it is recommended to use PageHeader instead of the Breadcrumb.\n\n[Docs](https://element-plus.org/en-US/component/page-header.html#page-header)"}, "el-pagination": {"attributes": ["size", "background", "page-size", "default-page-size", "total", "page-count", "pager-count", "current-page", "default-current-page", "layout", "page-sizes", "append-size-to", "popper-class", "prev-text", "prev-icon", "next-text", "next-icon", "disabled", "teleported", "hide-on-single-page", "small", "size-change", "current-change", "change", "prev-click", "next-click"], "description": "If you have too much data to display in one page, use pagination.\n\n[Docs](https://element-plus.org/en-US/component/pagination.html#pagination)"}, "el-popconfirm": {"attributes": ["title", "confirm-button-text", "cancel-button-text", "confirm-button-type", "cancel-button-type", "icon", "icon-color", "hide-icon", "hide-after", "teleported", "persistent", "width", "confirm", "cancel"], "description": "A simple confirmation dialog of an element click action.\n\n[Docs](https://element-plus.org/en-US/component/popconfirm.html#popconfirm)"}, "el-popover": {"attributes": ["trigger", "trigger-keys", "title", "effect", "content", "width", "placement", "disabled", "visible", "offset", "transition", "show-arrow", "popper-options", "popper-class", "popper-style", "show-after", "hide-after", "auto-close", "tabindex", "teleported", "append-to", "persistent", "virtual-triggering", "virtual-ref", "show", "before-enter", "after-enter", "hide", "before-leave", "after-leave"], "description": "[Docs](https://element-plus.org/en-US/component/popover.html#popover)"}, "el-progress": {"attributes": ["percentage", "type", "stroke-width", "text-inside", "status", "indeterminate", "duration", "color", "width", "show-text", "stroke-linecap", "format", "striped", "striped-flow"], "description": "Progress is used to show the progress of current operation, and inform the user the current status.\n\n[Docs](https://element-plus.org/en-US/component/progress.html#progress)"}, "el-radio": {"attributes": ["model-value", "value", "label", "disabled", "border", "size", "name", "change"], "description": "Single selection among multiple options.\n\n[Docs](https://element-plus.org/en-US/component/radio.html#radio)"}, "el-radio-group": {"attributes": ["model-value", "size", "disabled", "validate-event", "aria-label", "name", "id", "label", "change"], "subtags": ["el-radio", "el-radio-button"], "description": "[Docs](https://element-plus.org/en-US/component/radio.html#radiogroup)"}, "el-radio-button": {"attributes": ["value", "label", "disabled", "name", "text-color", "fill"], "description": "[Docs](https://element-plus.org/en-US/component/radio.html#radiobutton)"}, "el-rate": {"attributes": ["model-value", "max", "size", "disabled", "allow-half", "low-threshold", "high-threshold", "colors", "void-color", "disabled-void-color", "icons", "void-icon", "disabled-void-icon", "show-text", "show-score", "text-color", "texts", "score-template", "clearable", "id", "aria-label", "label", "change"], "description": "Used for rating\n\n[Docs](https://element-plus.org/en-US/component/rate.html#rate)"}, "el-result": {"attributes": ["title", "sub-title", "icon"], "description": "Used to give feedback on the result of user's operation or access exception.\n\n[Docs](https://element-plus.org/en-US/component/result.html#result)"}, "el-scrollbar": {"attributes": ["height", "max-height", "native", "wrap-style", "wrap-class", "view-style", "view-class", "noresize", "tag", "always", "min-size", "id", "role", "aria-label", "aria-orientation", "tabindex", "scroll"], "description": "Used to replace the browser's native scrollbar.\n\n[Docs](https://element-plus.org/en-US/component/scrollbar.html#scrollbar)"}, "el-segmented": {"attributes": ["model-value", "options", "props", "size", "block", "disabled", "validate-event", "name", "id", "aria-label", "direction", "change"], "description": "Display multiple options and allow users to select a single option.\n\n[Docs](https://element-plus.org/en-US/component/segmented.html#segmented)"}, "el-virtualized-select": {"attributes": ["model-value", "options", "props", "multiple", "disabled", "value-key", "size", "clearable", "clear-icon", "collapse-tags", "multiple-limit", "name", "effect", "autocomplete", "placeholder", "filterable", "allow-create", "filter-method", "loading", "loading-text", "reserve-keyword", "no-match-text", "no-data-text", "popper-class", "teleported", "append-to", "persistent", "popper-options", "automatic-dropdown", "fit-input-width", "suffix-icon", "height", "item-height", "scrollbar-always-on", "remote", "remote-method", "validate-event", "offset", "show-arrow", "placement", "fallback-placements", "collapse-tags-tooltip", "max-collapse-tags", "tag-type", "tag-effect", "aria-label", "empty-values", "value-on-clear", "popper-append-to-body", "tabindex", "change", "visible-change", "remove-tag", "clear", "blur", "focus"], "description": ":::tip\n\n[Docs](https://element-plus.org/en-US/component/select-v2.html#virtualized-select)"}, "el-select": {"attributes": ["model-value", "multiple", "disabled", "value-key", "size", "clearable", "collapse-tags", "collapse-tags-tooltip", "multiple-limit", "name", "effect", "autocomplete", "placeholder", "filterable", "allow-create", "filter-method", "remote", "remote-method", "remote-show-suffix", "loading", "loading-text", "no-match-text", "no-data-text", "popper-class", "reserve-keyword", "default-first-option", "teleported", "append-to", "persistent", "automatic-dropdown", "clear-icon", "fit-input-width", "suffix-icon", "tag-type", "tag-effect", "validate-event", "offset", "show-arrow", "placement", "fallback-placements", "max-collapse-tags", "popper-options", "aria-label", "empty-values", "value-on-clear", "suffix-transition", "tabindex", "change", "visible-change", "remove-tag", "clear", "blur", "focus", "popup-scroll"], "subtags": ["el-option-group", "el-option"], "description": "When there are plenty of options, use a drop-down menu to display and select desired ones.\n\n[Docs](https://element-plus.org/en-US/component/select.html#select)"}, "el-option-group": {"attributes": ["label", "disabled"], "subtags": ["el-option"], "description": "[Docs](https://element-plus.org/en-US/component/select.html#option-group)"}, "el-option": {"attributes": ["value", "label", "disabled"], "description": "[Docs](https://element-plus.org/en-US/component/select.html#option)"}, "el-skeleton": {"attributes": ["animated", "count", "loading", "rows", "throttle"], "description": "When loading data, and you need a rich experience for visual and interactions for your end users, you can choose `skeleton`.\n\n[Docs](https://element-plus.org/en-US/component/skeleton.html#skeleton)"}, "el-skeleton-item": {"attributes": ["variant"], "description": "[Docs](https://element-plus.org/en-US/component/skeleton.html#skeletonitem)"}, "el-slider": {"attributes": ["model-value", "min", "max", "disabled", "step", "show-input", "show-input-controls", "size", "input-size", "show-stops", "show-tooltip", "format-tooltip", "range", "vertical", "height", "aria-label", "range-start-label", "range-end-label", "format-value-text", "debounce", "tooltip-class", "placement", "marks", "validate-event", "persistent", "label", "change", "input"], "description": "Drag the slider within a fixed range.\n\n[Docs](https://element-plus.org/en-US/component/slider.html#slider)"}, "el-space": {"attributes": ["alignment", "class", "direction", "prefix-cls", "style", "spacer", "size", "wrap", "fill", "fill-ratio"], "description": "Even though we have [Divider]\n\n[Docs](https://element-plus.org/en-US/component/space.html#space)"}, "el-statistic": {"description": "Display statistics.\n\n[Docs](https://element-plus.org/en-US/component/statistic.html#statistic)"}, "el-countdown": {"description": ":::demo Countdown component, support to add other components control countdown.\n\n[Docs](https://element-plus.org/en-US/component/statistic.html#countdown)"}, "el-steps": {"attributes": ["space", "direction", "active", "process-status", "finish-status", "align-center", "simple"], "subtags": ["el-step"], "description": "Guide the user to complete tasks in accordance with the process. Its steps can be set according to the actual application scenario and the number of the steps can't be less than 2.\n\n[Docs](https://element-plus.org/en-US/component/steps.html#steps)"}, "el-step": {"attributes": ["title", "description", "icon", "status"], "description": "[Docs](https://element-plus.org/en-US/component/steps.html#step)"}, "el-switch": {"attributes": ["model-value", "disabled", "loading", "size", "width", "inline-prompt", "active-icon", "inactive-icon", "active-action-icon", "inactive-action-icon", "active-text", "inactive-text", "active-value", "inactive-value", "name", "validate-event", "before-change", "id", "tabindex", "aria-label", "active-color", "inactive-color", "border-color", "label", "change"], "description": "Switch is used for switching between two opposing states.\n\n[Docs](https://element-plus.org/en-US/component/switch.html#switch)"}, "el-table-v2": {"attributes": ["cache", "estimated-row-height", "header-class", "header-props", "header-cell-props", "header-height", "footer-height", "row-class", "row-key", "row-props", "row-height", "row-event-handlers", "cell-props", "columns", "data", "data-getter", "fixed-data", "expand-column-key", "expanded-row-keys", "default-expanded-row-keys", "class", "fixed", "width", "height", "max-height", "indent-size", "h-scrollbar-size", "v-scrollbar-size", "scrollbar-always-on", "sort-by", "sort-state", "column-sort", "expanded-rows-change", "end-reached", "scroll", "rows-rendered", "row-expand"], "description": "[Docs](https://element-plus.org/en-US/component/table-v2.html#tablev2)"}, "el-table": {"attributes": ["data", "height", "max-height", "stripe", "border", "size", "fit", "show-header", "highlight-current-row", "current-row-key", "row-class-name", "row-style", "cell-class-name", "cell-style", "header-row-class-name", "header-row-style", "header-cell-class-name", "header-cell-style", "row-key", "empty-text", "default-expand-all", "expand-row-keys", "default-sort", "tooltip-effect", "tooltip-options", "append-filter-panel-to", "show-summary", "sum-text", "summary-method", "span-method", "select-on-indeterminate", "indent", "lazy", "load", "tree-props", "table-layout", "scrollbar-always-on", "show-overflow-tooltip", "flexible", "scrollbar-tabindex", "allow-drag-last-column", "tooltip-formatter", "preserve-expanded-content", "select", "select-all", "selection-change", "cell-mouse-enter", "cell-mouse-leave", "cell-click", "cell-dblclick", "cell-contextmenu", "row-click", "row-contextmenu", "row-dblclick", "header-click", "header-contextmenu", "sort-change", "filter-change", "current-change", "header-dragend", "expand-change", "scroll"], "subtags": ["el-table-column"], "description": "Display multiple data with similar format. You can sort, filter, compare your data in a table.\n\n[Docs](https://element-plus.org/en-US/component/table.html#table)"}, "el-table-column": {"attributes": ["type", "index", "label", "column-key", "prop", "width", "min-width", "fixed", "render-header", "sortable", "sort-method", "sort-by", "sort-orders", "resizable", "formatter", "show-overflow-tooltip", "align", "header-align", "class-name", "label-class-name", "selectable", "reserve-selection", "filters", "filter-placement", "filter-class-name", "filter-multiple", "filter-method", "filtered-value", "tooltip-formatter"], "description": "[Docs](https://element-plus.org/en-US/component/table.html#table-column)"}, "el-tabs": {"attributes": ["model-value", "type", "closable", "addable", "editable", "tab-position", "stretch", "before-leave", "tab-click", "tab-change", "tab-remove", "tab-add", "edit"], "subtags": ["el-tab-pane"], "description": "Divide data collections which are related yet belong to different types.\n\n[Docs](https://element-plus.org/en-US/component/tabs.html#tabs)"}, "el-tab-pane": {"attributes": ["label", "disabled", "name", "closable", "lazy"], "description": "[Docs](https://element-plus.org/en-US/component/tabs.html#tab-pane)"}, "el-tag": {"attributes": ["type", "closable", "disable-transitions", "hit", "color", "size", "effect", "round", "click", "close"], "description": "Used for marking and selection.\n\n[Docs](https://element-plus.org/en-US/component/tag.html#tag)"}, "el-check-tag": {"attributes": ["checked", "disabled", "type", "change"], "description": "[Docs](https://element-plus.org/en-US/component/tag.html#checktag)"}, "el-text": {"attributes": ["type", "size", "truncated", "line-clamp", "tag"], "description": "Used for text.\n\n[Docs](https://element-plus.org/en-US/component/text.html#text)"}, "el-time-picker": {"attributes": ["model-value", "readonly", "disabled", "editable", "clearable", "size", "placeholder", "start-placeholder", "end-placeholder", "is-range", "arrow-control", "popper-class", "range-separator", "format", "default-value", "value-format", "id", "name", "aria-label", "prefix-icon", "clear-icon", "disabled-hours", "disabled-minutes", "disabled-seconds", "teleported", "tabindex", "empty-values", "value-on-clear", "label", "change", "blur", "focus", "clear", "visible-change"], "description": "Use Time Picker for time input.\n\n[Docs](https://element-plus.org/en-US/component/time-picker.html#timepicker)"}, "el-time-select": {"attributes": ["model-value", "disabled", "editable", "clearable", "include-end-time", "size", "placeholder", "name", "effect", "prefix-icon", "clear-icon", "start", "end", "step", "min-time", "max-time", "format", "empty-values", "value-on-clear", "change", "blur", "focus", "clear"], "description": "Use Time Select for time input.\n\n[Docs](https://element-plus.org/en-US/component/time-select.html#timeselect)"}, "el-timeline": {"subtags": ["el-timeline-item"], "description": "Visually display timeline.\n\n[Docs](https://element-plus.org/en-US/component/timeline.html#timeline)"}, "el-timeline-item": {"attributes": ["timestamp", "hide-timestamp", "center", "placement", "type", "color", "size", "icon", "hollow"], "description": "[Docs](https://element-plus.org/en-US/component/timeline.html#timeline-item)"}, "el-tooltip": {"attributes": ["append-to", "effect", "content", "raw-content", "placement", "fallback-placements", "visible", "disabled", "offset", "transition", "popper-options", "arrow-offset", "show-after", "show-arrow", "hide-after", "auto-close", "popper-class", "enterable", "teleported", "trigger", "virtual-triggering", "virtual-ref", "trigger-keys", "persistent", "aria-label"], "description": "Display prompt information for mouse hover.\n\n[Docs](https://element-plus.org/en-US/component/tooltip.html#tooltip)"}, "el-tour": {"description": "A popup component for guiding users through a product. Use when you want to guide users through a product.\n\n[Docs](https://element-plus.org/en-US/component/tour.html#tour)"}, "el-tour-step": {"description": "[Docs](https://element-plus.org/en-US/component/tour.html#tourstep)"}, "el-transfer": {"attributes": ["model-value", "data", "filterable", "filter-placeholder", "filter-method", "target-order", "titles", "button-texts", "render-content", "format", "props", "left-default-checked", "right-default-checked", "validate-event", "change", "left-check-change", "right-check-change"], "description": "[Docs](https://element-plus.org/en-US/component/transfer.html#transfer)"}, "el-own": {"attributes": ["cache-data"], "description": "[Docs](https://element-plus.org/en-US/component/tree-select.html#own)"}, "el-tree-v2": {"attributes": ["data", "empty-text", "props", "highlight-current", "expand-on-click-node", "check-on-click-node", "check-on-click-leaf", "default-expanded-keys", "show-checkbox", "check-strictly", "default-checked-keys", "current-node-key", "filter-method", "indent", "icon", "item-size", "node-click", "node-drop", "node-contextmenu", "check-change", "check", "current-change", "node-expand", "node-collapse"], "description": "[Docs](https://element-plus.org/en-US/component/tree-v2.html#treev2)"}, "el-tree": {"attributes": ["data", "empty-text", "node-key", "props", "render-after-expand", "load", "render-content", "highlight-current", "default-expand-all", "expand-on-click-node", "check-on-click-node", "check-on-click-leaf", "auto-expand-parent", "default-expanded-keys", "show-checkbox", "check-strictly", "default-checked-keys", "current-node-key", "filter-node-method", "accordion", "indent", "icon", "lazy", "draggable", "allow-drag", "allow-drop", "node-click", "node-contextmenu", "check-change", "check", "current-change", "node-expand", "node-collapse", "node-drag-start", "node-drag-enter", "node-drag-leave", "node-drag-over", "node-drag-end", "node-drop"], "description": "Display a set of data with hierarchies.\n\n[Docs](https://element-plus.org/en-US/component/tree.html#tree)"}, "el-upload": {"attributes": ["action", "headers", "method", "multiple", "data", "name", "with-credentials", "show-file-list", "drag", "accept", "crossorigin", "on-preview", "on-remove", "on-success", "on-error", "on-progress", "on-change", "on-exceed", "before-upload", "before-remove", "file-list", "list-type", "auto-upload", "http-request", "disabled", "limit"], "description": "Upload files by clicking or drag-and-drop.\n\n[Docs](https://element-plus.org/en-US/component/upload.html#upload)"}, "el-watermark": {"attributes": ["width", "height", "rotate", "z-index", "image", "content", "font", "gap", "offset"], "description": "Add specific text or patterns to the page.\n\n[Docs](https://element-plus.org/en-US/component/watermark.html#watermark)"}}