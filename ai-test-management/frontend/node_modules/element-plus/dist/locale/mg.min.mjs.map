{"version": 3, "file": "mg.min.mjs", "sources": ["../../../../packages/locale/lang/mg.ts"], "sourcesContent": ["export default {\n  name: 'mg',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'EN<PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>ny',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'ENY',\n      selectDate: 'Misafidy daty',\n      selectTime: 'Misafidy ora',\n      startDate: '<PERSON><PERSON> fanombohana',\n      startTime: '<PERSON>a fanombohana',\n      endDate: '<PERSON>ty farany',\n      endTime: 'Ora farany',\n      prevYear: 'Taona teo aloha',\n      nextYear: '<PERSON>na manaraka',\n      prevMonth: '<PERSON>ana teo aloha',\n      nextMonth: '<PERSON>ana manaraka',\n      year: '',\n      month1: 'Janoary',\n      month2: 'Febroary',\n      month3: 'Martsa',\n      month4: 'Aprily',\n      month5: 'May',\n      month6: 'Jona',\n      month7: 'Jolay',\n      month8: 'A<PERSON><PERSON><PERSON>',\n      month9: 'Septambra',\n      month10: 'Ok<PERSON><PERSON>',\n      month11: 'Nova<PERSON><PERSON>',\n      month12: '<PERSON><PERSON><PERSON>',\n      week: 'herinandro',\n      weeks: {\n        sun: 'Lad',\n        mon: 'Ala',\n        tue: 'Tal',\n        wed: 'Lar',\n        thu: 'Lak',\n        fri: 'Zom',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Jon',\n        jul: 'Jol',\n        aug: 'Aog',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Eo ampiandrasana',\n      noMatch: 'Tsy misy angona mifanentana',\n      noData: 'Tsy misy angona',\n      placeholder: 'Safidy',\n    },\n    mention: {\n      loading: 'Eo ampiandrasana',\n    },\n    cascader: {\n      noMatch: 'Tsy misy angona mifanentana',\n      loading: 'Eo ampiandrasana',\n      placeholder: 'Safidy',\n      noData: 'Tsy misy angona',\n    },\n    pagination: {\n      goto: 'Mandeha any',\n      pagesize: '/page',\n      total: 'Totaly {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'Fampiasana tsy ampiasaina intsony no hita, azafady mba jereo ny tahirin-kevitra el-pagination raha mila fanazavana fanampiny',\n    },\n    messagebox: {\n      title: 'Hafatra',\n      confirm: 'ENY',\n      cancel: 'Hanafoana',\n      error: 'Fampidirana tsy ara-dalàna',\n    },\n    upload: {\n      deleteTip: 'tsindrio fafana raha hanala',\n      delete: 'Fafana',\n      preview: 'Topi-maso',\n      continue: 'Hanoy',\n    },\n    table: {\n      emptyText: 'Tsy misy angona',\n      confirmFilter: 'Manamarina',\n      resetFilter: 'Averina',\n      clearFilter: 'Rehetra',\n      sumText: 'Atambatra',\n    },\n    tree: {\n      emptyText: 'Tsy misy angona',\n    },\n    transfer: {\n      noMatch: 'Tsy misy angona mifanentana',\n      noData: 'Tsy misy angona',\n      titles: ['Lisitra 1', 'Lisitra 2'], // to be translated\n      filterPlaceholder: 'Ampidiro teny fanalahidy', // to be translated\n      noCheckedFormat: '{total} zavatra', // to be translated\n      hasCheckedFormat: '{checked}/{total} voamarina', // to be translated\n    },\n    image: {\n      error: 'TSY NAHOMBY',\n    },\n    pageHeader: {\n      title: 'Miverina', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Eny',\n      cancelButtonText: 'Tsy',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,8HAA8H,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}