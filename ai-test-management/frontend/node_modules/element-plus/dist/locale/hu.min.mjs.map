{"version": 3, "file": "hu.min.mjs", "sources": ["../../../../packages/locale/lang/hu.ts"], "sourcesContent": ["export default {\n  name: 'hu',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Törl<PERSON>',\n    },\n    datepicker: {\n      now: 'Most',\n      today: 'Ma',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Törl<PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON>',\n      selectTime: 'Id<PERSON>pont',\n      startDate: 'D<PERSON>tum-tól',\n      startTime: 'Időpont-tól',\n      endDate: 'Dátum-ig',\n      endTime: 'Időpont-ig',\n      prevYear: 'Előző év',\n      nextYear: 'Következő év',\n      prevMonth: '<PERSON><PERSON><PERSON>ő hónap',\n      nextMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hónap',\n      year: '',\n      month1: 'Január',\n      month2: 'Február',\n      month3: '<PERSON><PERSON><PERSON><PERSON>',\n      month4: 'Április',\n      month5: '<PERSON><PERSON><PERSON><PERSON>',\n      month6: '<PERSON><PERSON><PERSON>',\n      month7: '<PERSON><PERSON><PERSON>',\n      month8: '<PERSON><PERSON><PERSON><PERSON>',\n      month9: '<PERSON>zeptember',\n      month10: 'Október',\n      month11: 'November',\n      month12: 'December',\n      weeks: {\n        sun: 'Vas',\n        mon: 'Hét',\n        tue: 'Ked',\n        wed: 'Sze',\n        thu: '<PERSON>sü',\n        fri: 'P<PERSON>',\n        sat: 'Szo',\n      },\n      months: {\n        jan: '<PERSON>',\n        feb: 'Feb',\n        mar: 'Már',\n        apr: '<PERSON>pr',\n        may: 'M<PERSON>j',\n        jun: 'Jún',\n        jul: 'Júl',\n        aug: 'Aug',\n        sep: 'Szep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Betöltés',\n      noMatch: 'Nincs találat',\n      noData: 'Nincs adat',\n      placeholder: 'Válassz',\n    },\n    mention: {\n      loading: 'Betöltés',\n    },\n    cascader: {\n      noMatch: 'Nincs találat',\n      loading: 'Betöltés',\n      placeholder: 'Válassz',\n      noData: 'Nincs adat',\n    },\n    pagination: {\n      goto: 'Ugrás',\n      pagesize: '/oldal',\n      total: 'Össz {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Üzenet',\n      confirm: 'OK',\n      cancel: 'Mégse',\n      error: 'Hibás adat',\n    },\n    upload: {\n      deleteTip: 'kattints a törléshez',\n      delete: 'Törlés',\n      preview: 'Előnézet',\n      continue: 'Tovább',\n    },\n    table: {\n      emptyText: 'Nincs adat',\n      confirmFilter: 'Megerősít',\n      resetFilter: 'Alaphelyet',\n      clearFilter: 'Mind',\n      sumText: 'Összeg',\n    },\n    tree: {\n      emptyText: 'Nincs adat',\n    },\n    transfer: {\n      noMatch: 'Nincs találat',\n      noData: 'Nincs adat',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Kulcsszó',\n      noCheckedFormat: '{total} elem',\n      hasCheckedFormat: '{checked}/{total} kiválasztva',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,qBAAqB,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,yBAAyB,CAAC,SAAS,CAAC,0BAA0B,CAAC,SAAS,CAAC,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,aAAa,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}