{"version": 3, "file": "it.mjs", "sources": ["../../../../../packages/locale/lang/it.ts"], "sourcesContent": ["export default {\n  name: 'it',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON>gg<PERSON>',\n      cancel: '<PERSON><PERSON>a',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Seleziona data',\n      selectTime: 'Seleziona ora',\n      startDate: 'Data inizio',\n      startTime: 'Ora inizio',\n      endDate: 'Data fine',\n      endTime: 'Ora fine',\n      prevYear: 'Anno precedente',\n      nextYear: 'Anno successivo',\n      prevMonth: 'Mese precedente',\n      nextMonth: 'Mese successivo',\n      year: '',\n      month1: 'Gennaio',\n      month2: 'Febbraio',\n      month3: 'Marzo',\n      month4: 'Aprile',\n      month5: 'Maggio',\n      month6: 'Giugno',\n      month7: 'Luglio',\n      month8: 'Agosto',\n      month9: 'Settembre',\n      month10: '<PERSON><PERSON>',\n      month11: 'Novembre',\n      month12: 'Di<PERSON><PERSON>',\n      // week: 'setti<PERSON>',\n      weeks: {\n        sun: 'Dom',\n        mon: 'Lun',\n        tue: 'Mar',\n        wed: 'Mer',\n        thu: 'G<PERSON>',\n        fri: 'Ven',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Gen',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mag',\n        jun: 'Giu',\n        jul: 'Lug',\n        aug: 'Ago',\n        sep: 'Set',\n        oct: 'Ott',\n        nov: 'Nov',\n        dec: 'Dic',\n      },\n    },\n    select: {\n      loading: 'Caricamento',\n      noMatch: 'Nessuna corrispondenza',\n      noData: 'Nessun dato',\n      placeholder: 'Seleziona',\n    },\n    mention: {\n      loading: 'Caricamento',\n    },\n    cascader: {\n      noMatch: 'Nessuna corrispondenza',\n      loading: 'Caricamento',\n      placeholder: 'Seleziona',\n      noData: 'Nessun dato',\n    },\n    pagination: {\n      goto: 'Vai a',\n      pagesize: '/page',\n      total: 'Totale {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Cancella',\n      error: 'Input non valido',\n    },\n    upload: {\n      deleteTip: 'Premi cancella per rimuovere',\n      delete: 'Cancella',\n      preview: 'Anteprima',\n      continue: 'Continua',\n    },\n    table: {\n      emptyText: 'Nessun dato',\n      confirmFilter: 'Conferma',\n      resetFilter: 'Reset',\n      clearFilter: 'Tutti',\n      sumText: 'Somma',\n    },\n    tree: {\n      emptyText: 'Nessun dato',\n    },\n    transfer: {\n      noMatch: 'Nessuna corrispondenza',\n      noData: 'Nessun dato',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Inserisci filtro',\n      noCheckedFormat: '{total} elementi',\n      hasCheckedFormat: '{checked}/{total} selezionati',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,WAAW,EAAE,WAAW;AAC9B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,aAAa;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,8BAA8B;AAC/C,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,aAAa;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,kBAAkB;AAC3C,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,gBAAgB,EAAE,+BAA+B;AACvD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}