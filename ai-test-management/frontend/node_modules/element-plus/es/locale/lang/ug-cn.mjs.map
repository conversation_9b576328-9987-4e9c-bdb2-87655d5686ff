{"version": 3, "file": "ug-cn.mjs", "sources": ["../../../../../packages/locale/lang/ug-cn.ts"], "sourcesContent": ["export default {\n  name: 'ug-cn',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'جەزملەش',\n      clear: 'قۇرۇقداش',\n    },\n    datepicker: {\n      now: 'ھازىرقى ۋاقىت',\n      today: 'بۈگۈن',\n      cancel: 'بىكار قىلىش',\n      clear: 'قۇرۇقداش',\n      confirm: 'جەزملەش',\n      selectDate: 'چىسلا تاللاڭ',\n      selectTime: 'ۋاقىت تاللاڭ',\n      startDate: 'باشلانغان چىسلا',\n      startTime: 'باشلانغان ۋاقىت',\n      endDate: 'ئاخىرلاشقان چىسلا',\n      endTime: 'ئاخىرلاشقان ۋاقىت',\n      prevYear: 'ئالدىنقى يىل',\n      nextYear: 'كىيىنكى يىل',\n      prevMonth: 'ئالدىنقى ئاي',\n      nextMonth: 'كىيىنكى ئاي',\n      year: '- يىل',\n      month1: '1-ئاي',\n      month2: '2-ئاي',\n      month3: '3-ئاي',\n      month4: '4-ئاي',\n      month5: '5-ئاي',\n      month6: '6-ئاي',\n      month7: '7-ئاي',\n      month8: '8-ئاي',\n      month9: '9-ئاي',\n      month10: '10-ئاي',\n      month11: '11-ئاي',\n      month12: '12-ئاي',\n      // week: '周次',\n      weeks: {\n        sun: 'يەكشەنبە',\n        mon: 'دۈشەنبە',\n        tue: 'سەيشەنبە',\n        wed: 'چارشەنبە',\n        thu: 'پەيشەنبە',\n        fri: 'جۈمە',\n        sat: 'شەنبە',\n      },\n      months: {\n        jan: '1-ئاي',\n        feb: '2-ئاي',\n        mar: '3-ئاي',\n        apr: '4-ئاي',\n        may: '5-ئاي',\n        jun: '6-ئاي',\n        jul: '7-ئاي',\n        aug: '8-ئاي',\n        sep: '9-ئاي',\n        oct: '10-ئاي',\n        nov: '11-ئاي',\n        dec: '12-ئاي',\n      },\n    },\n    select: {\n      loading: 'يۈكلىنىۋاتىدۇ',\n      noMatch: 'ئۇچۇر تېپىلمىدى',\n      noData: 'ئۇچۇر يوق',\n      placeholder: 'تاللاڭ',\n    },\n    mention: {\n      loading: 'يۈكلىنىۋاتىدۇ',\n    },\n    cascader: {\n      noMatch: 'ئۇچۇر تېپىلمىدى',\n      loading: 'يۈكلىنىۋاتىدۇ',\n      placeholder: 'تاللاڭ',\n      noData: 'ئۇچۇر يوق',\n    },\n    pagination: {\n      goto: 'كىيىنكى بەت',\n      pagesize: 'تال/بەت',\n      total: 'جەمئىي {total} تال',\n      pageClassifier: 'بەت',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'ئەسكەرتىش',\n      confirm: 'جەزملەش',\n      cancel: 'بىكار قىلىش',\n      error: 'كىرگۈزگەن ئۇچۇرىڭىزدا خاتالىق بار!',\n    },\n    upload: {\n      deleteTip: 'delete كۇنپكىسىنى بېسىپ ئۆچۈرەلەيسىز',\n      delete: 'ئۆچۈرۈش',\n      preview: 'رەسىمنى كۆرۈش',\n      continue: 'رەسىم يوللاش',\n    },\n    table: {\n      emptyText: 'ئۇچۇر يوق',\n      confirmFilter: 'سۈزگۈچ',\n      resetFilter: 'قايتا تولدۇرۇش',\n      clearFilter: 'ھەممە',\n      sumText: 'جەمئىي',\n    },\n    tree: {\n      emptyText: 'ئۇچۇر يوق',\n    },\n    transfer: {\n      noMatch: 'ئۇچۇر تېپىلمىدى',\n      noData: 'ئۇچۇر يوق',\n      titles: ['جەدۋەل 1', 'جەدۋەل 2'],\n      filterPlaceholder: 'ئىزدىمەكچى بولغان مەزمۇننى كىرگۈزۈڭ',\n      noCheckedFormat: 'جەمئىي {total} تۈر',\n      hasCheckedFormat: 'تاللانغىنى {checked}/{total} تۈر',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,WAAe;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,KAAK,EAAE,kDAAkD;AAC/D,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,2EAA2E;AACtF,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,+DAA+D;AAC7E,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,UAAU,EAAE,qEAAqE;AACvF,MAAM,UAAU,EAAE,qEAAqE;AACvF,MAAM,SAAS,EAAE,uFAAuF;AACxG,MAAM,SAAS,EAAE,uFAAuF;AACxG,MAAM,OAAO,EAAE,mGAAmG;AAClH,MAAM,OAAO,EAAE,mGAAmG;AAClH,MAAM,QAAQ,EAAE,qEAAqE;AACrF,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,IAAI,EAAE,sBAAsB;AAClC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,MAAM,EAAE,sBAAsB;AACpC,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,uBAAuB;AACpC,QAAQ,GAAG,EAAE,uBAAuB;AACpC,QAAQ,GAAG,EAAE,uBAAuB;AACpC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,MAAM,EAAE,mDAAmD;AACjE,MAAM,WAAW,EAAE,sCAAsC;AACzD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,gFAAgF;AAC/F,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,MAAM,EAAE,mDAAmD;AACjE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,+DAA+D;AAC3E,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,KAAK,EAAE,iEAAiE;AAC9E,MAAM,cAAc,EAAE,oBAAoB;AAC1C,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,wDAAwD;AACrE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,MAAM,EAAE,+DAA+D;AAC7E,MAAM,KAAK,EAAE,0LAA0L;AACvM,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6KAA6K;AAC9L,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,QAAQ,EAAE,qEAAqE;AACrF,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,mDAAmD;AACpE,MAAM,aAAa,EAAE,sCAAsC;AAC3D,MAAM,WAAW,EAAE,iFAAiF;AACpG,MAAM,WAAW,EAAE,gCAAgC;AACnD,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,mDAAmD;AACpE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,MAAM,EAAE,mDAAmD;AACjE,MAAM,MAAM,EAAE,CAAC,wCAAwC,EAAE,wCAAwC,CAAC;AAClG,MAAM,iBAAiB,EAAE,qMAAqM;AAC9N,MAAM,eAAe,EAAE,iEAAiE;AACxF,MAAM,gBAAgB,EAAE,mGAAmG;AAC3H,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}