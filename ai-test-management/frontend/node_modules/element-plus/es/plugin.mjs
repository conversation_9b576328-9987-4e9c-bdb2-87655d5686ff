import { ElInfiniteScroll } from './components/infinite-scroll/index.mjs';
import { ElLoading } from './components/loading/index.mjs';
import { ElMessage } from './components/message/index.mjs';
import { ElMessageBox } from './components/message-box/index.mjs';
import { ElNotification } from './components/notification/index.mjs';
import { ElPopoverDirective } from './components/popover/index.mjs';

var Plugins = [
  ElInfiniteScroll,
  ElLoading,
  ElMessage,
  ElMessageBox,
  ElNotification,
  ElPopoverDirective
];

export { Plugins as default };
//# sourceMappingURL=plugin.mjs.map
