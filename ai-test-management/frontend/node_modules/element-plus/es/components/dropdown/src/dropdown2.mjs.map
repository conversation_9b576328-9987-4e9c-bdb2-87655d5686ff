{"version": 3, "file": "dropdown2.mjs", "sources": ["../../../../../../packages/components/dropdown/src/dropdown.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is('disabled', disabled)]\">\n    <el-tooltip\n      ref=\"popperRef\"\n      :role=\"role\"\n      :effect=\"effect\"\n      :fallback-placements=\"['bottom', 'top']\"\n      :popper-options=\"popperOptions\"\n      :gpu-acceleration=\"false\"\n      :hide-after=\"trigger === 'hover' ? hideTimeout : 0\"\n      :manual-mode=\"true\"\n      :placement=\"placement\"\n      :popper-class=\"[ns.e('popper'), popperClass]\"\n      :reference-element=\"referenceElementRef?.$el\"\n      :trigger=\"trigger\"\n      :trigger-keys=\"triggerKeys\"\n      :trigger-target-el=\"contentRef\"\n      :show-after=\"trigger === 'hover' ? showTimeout : 0\"\n      :stop-popper-mouse-event=\"false\"\n      :virtual-ref=\"triggeringElementRef\"\n      :virtual-triggering=\"splitButton\"\n      :disabled=\"disabled\"\n      :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n      :teleported=\"teleported\"\n      pure\n      :persistent=\"persistent\"\n      @before-show=\"handleBeforeShowTooltip\"\n      @show=\"handleShowTooltip\"\n      @before-hide=\"handleBeforeHideTooltip\"\n    >\n      <template #content>\n        <el-scrollbar\n          ref=\"scrollbar\"\n          :wrap-style=\"wrapStyle\"\n          tag=\"div\"\n          :view-class=\"ns.e('list')\"\n        >\n          <el-roving-focus-group\n            :loop=\"loop\"\n            :current-tab-id=\"currentTabId\"\n            orientation=\"horizontal\"\n            @current-tab-id-change=\"handleCurrentTabIdChange\"\n            @entry-focus=\"handleEntryFocus\"\n          >\n            <el-dropdown-collection>\n              <slot name=\"dropdown\" />\n            </el-dropdown-collection>\n          </el-roving-focus-group>\n        </el-scrollbar>\n      </template>\n      <template v-if=\"!splitButton\" #default>\n        <el-only-child\n          :id=\"triggerId\"\n          ref=\"triggeringElementRef\"\n          role=\"button\"\n          :tabindex=\"tabindex\"\n        >\n          <slot name=\"default\" />\n        </el-only-child>\n      </template>\n    </el-tooltip>\n    <template v-if=\"splitButton\">\n      <el-button-group>\n        <el-button\n          ref=\"referenceElementRef\"\n          v-bind=\"buttonProps\"\n          :size=\"dropdownSize\"\n          :type=\"type\"\n          :disabled=\"disabled\"\n          :tabindex=\"tabindex\"\n          @click=\"handlerMainButtonClick\"\n        >\n          <slot name=\"default\" />\n        </el-button>\n        <el-button\n          :id=\"triggerId\"\n          ref=\"triggeringElementRef\"\n          v-bind=\"buttonProps\"\n          role=\"button\"\n          :size=\"dropdownSize\"\n          :type=\"type\"\n          :class=\"ns.e('caret-button')\"\n          :disabled=\"disabled\"\n          :tabindex=\"tabindex\"\n          :aria-label=\"t('el.dropdown.toggleDropdown')\"\n        >\n          <el-icon :class=\"ns.e('icon')\"><arrow-down /></el-icon>\n        </el-button>\n      </el-button-group>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onBeforeUnmount,\n  provide,\n  ref,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport ElButton from '@element-plus/components/button'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElIcon from '@element-plus/components/icon'\nimport ElRovingFocusGroup from '@element-plus/components/roving-focus-group'\nimport { ElOnlyChild } from '@element-plus/components/slot'\nimport { useFormSize } from '@element-plus/components/form'\nimport { addUnit, ensureArray } from '@element-plus/utils'\nimport { ArrowDown } from '@element-plus/icons-vue'\nimport { useId, useLocale, useNamespace } from '@element-plus/hooks'\nimport { ElCollection as ElDropdownCollection, dropdownProps } from './dropdown'\nimport { DROPDOWN_INJECTION_KEY } from './tokens'\n\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { CSSProperties } from 'vue'\n\nconst { ButtonGroup: ElButtonGroup } = ElButton\n\nexport default defineComponent({\n  name: 'ElDropdown',\n  components: {\n    ElButton,\n    ElButtonGroup,\n    ElScrollbar,\n    ElDropdownCollection,\n    ElTooltip,\n    ElRovingFocusGroup,\n    ElOnlyChild,\n    ElIcon,\n    ArrowDown,\n  },\n  props: dropdownProps,\n  emits: ['visible-change', 'click', 'command'],\n  setup(props, { emit }) {\n    const _instance = getCurrentInstance()\n    const ns = useNamespace('dropdown')\n    const { t } = useLocale()\n\n    const triggeringElementRef = ref()\n    const referenceElementRef = ref()\n    const popperRef = ref<TooltipInstance>()\n    const contentRef = ref<HTMLElement>()\n    const scrollbar = ref(null)\n    const currentTabId = ref<string | null>(null)\n    const isUsingKeyboard = ref(false)\n\n    const wrapStyle = computed<CSSProperties>(() => ({\n      maxHeight: addUnit(props.maxHeight),\n    }))\n    const dropdownTriggerKls = computed(() => [ns.m(dropdownSize.value)])\n    const trigger = computed(() => ensureArray(props.trigger))\n\n    const defaultTriggerId = useId().value\n    const triggerId = computed<string>(() => props.id || defaultTriggerId)\n\n    // The goal of this code is to focus on the tooltip triggering element when it is hovered.\n    // This is a temporary fix for where closing the dropdown through pointerleave event focuses on a\n    // completely different element. For a permanent solution, remove all calls to any \"element.focus()\"\n    // that are triggered through pointer enter/leave events.\n    watch(\n      [triggeringElementRef, trigger],\n      ([triggeringElement, trigger], [prevTriggeringElement]) => {\n        if (prevTriggeringElement?.$el?.removeEventListener) {\n          prevTriggeringElement.$el.removeEventListener(\n            'pointerenter',\n            onAutofocusTriggerEnter\n          )\n        }\n        if (triggeringElement?.$el?.removeEventListener) {\n          triggeringElement.$el.removeEventListener(\n            'pointerenter',\n            onAutofocusTriggerEnter\n          )\n        }\n        if (\n          triggeringElement?.$el?.addEventListener &&\n          trigger.includes('hover')\n        ) {\n          triggeringElement.$el.addEventListener(\n            'pointerenter',\n            onAutofocusTriggerEnter\n          )\n        }\n      },\n      { immediate: true }\n    )\n\n    onBeforeUnmount(() => {\n      if (triggeringElementRef.value?.$el?.removeEventListener) {\n        triggeringElementRef.value.$el.removeEventListener(\n          'pointerenter',\n          onAutofocusTriggerEnter\n        )\n      }\n    })\n\n    function handleClick() {\n      handleClose()\n    }\n\n    function handleClose() {\n      popperRef.value?.onClose()\n    }\n\n    function handleOpen() {\n      popperRef.value?.onOpen()\n    }\n\n    const dropdownSize = useFormSize()\n\n    function commandHandler(...args: any[]) {\n      emit('command', ...args)\n    }\n\n    function onAutofocusTriggerEnter() {\n      triggeringElementRef.value?.$el?.focus()\n    }\n\n    function onItemEnter() {\n      // NOOP for now\n    }\n\n    function onItemLeave() {\n      const contentEl = unref(contentRef)\n\n      trigger.value.includes('hover') && contentEl?.focus()\n      currentTabId.value = null\n    }\n\n    function handleCurrentTabIdChange(id: string) {\n      currentTabId.value = id\n    }\n\n    function handleEntryFocus(e: Event) {\n      if (!isUsingKeyboard.value) {\n        e.preventDefault()\n        e.stopImmediatePropagation()\n      }\n    }\n\n    function handleBeforeShowTooltip() {\n      emit('visible-change', true)\n    }\n\n    function handleShowTooltip(event?: Event) {\n      if (event?.type === 'keydown') {\n        contentRef.value?.focus()\n      }\n    }\n\n    function handleBeforeHideTooltip() {\n      emit('visible-change', false)\n    }\n\n    provide(DROPDOWN_INJECTION_KEY, {\n      contentRef,\n      role: computed(() => props.role),\n      triggerId,\n      isUsingKeyboard,\n      onItemEnter,\n      onItemLeave,\n    })\n\n    provide('elDropdown', {\n      instance: _instance,\n      dropdownSize,\n      handleClick,\n      commandHandler,\n      trigger: toRef(props, 'trigger'),\n      hideOnClick: toRef(props, 'hideOnClick'),\n    })\n\n    const onFocusAfterTrapped = (e: Event) => {\n      e.preventDefault()\n      contentRef.value?.focus?.({\n        preventScroll: true,\n      })\n    }\n\n    const handlerMainButtonClick = (event: MouseEvent) => {\n      emit('click', event)\n    }\n\n    return {\n      t,\n      ns,\n      scrollbar,\n      wrapStyle,\n      dropdownTriggerKls,\n      dropdownSize,\n      triggerId,\n      currentTabId,\n      handleCurrentTabIdChange,\n      handlerMainButtonClick,\n      handleEntryFocus,\n      handleClose,\n      handleOpen,\n      handleBeforeShowTooltip,\n      handleShowTooltip,\n      handleBeforeHideTooltip,\n      onFocusAfterTrapped,\n      popperRef,\n      contentRef,\n      triggeringElementRef,\n      referenceElementRef,\n    }\n  },\n})\n</script>\n"], "names": ["ElDropdownCollection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ensureArray", "_resolveComponent", "_createSlots", "_withCtx", "_createVNode", "_openBlock", "_createBlock", "_mergeProps", "_createCommentVNode"], "mappings": ";;;;;;;;;;;;;;;;;;AAyHA,MAAM,EAAE,WAAa,EAAA,aAAA,EAAkB,GAAA,QAAA,CAAA;AAEvC,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,YAAA;AAAA,EACN,UAAY,EAAA;AAAA,IACV,QAAA;AAAA,IACA,aAAA;AAAA,IACA,WAAA;AAAA,0BACAA,YAAA;AAAA,IACA,SAAA;AAAA,IACA,kBAAA;AAAA,iBACAC,SAAA;AAAA,IACA,MAAA;AAAA,IACA,SAAA;AAAA,GACF;AAAA,EACA,KAAO,EAAA,aAAA;AAAA,EACP,KAAO,EAAA,CAAC,gBAAkB,EAAA,OAAA,EAAS,SAAS,CAAA;AAAA,EAC5C,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAA,MAAM,YAAY,kBAAmB,EAAA,CAAA;AACrC,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAClC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AAExB,IAAA,MAAM,uBAAuB,GAAI,EAAA,CAAA;AACjC,IAAA,MAAM,sBAAsB,GAAI,EAAA,CAAA;AAChC,IAAA,MAAM,YAAY,GAAqB,EAAA,CAAA;AACvC,IAAA,MAAM,aAAa,GAAiB,EAAA,CAAA;AACpC,IAAM,MAAA,SAAA,GAAY,IAAI,IAAI,CAAA,CAAA;AAC1B,IAAM,MAAA,YAAA,GAAe,IAAmB,IAAI,CAAA,CAAA;AAC5C,IAAM,MAAA,eAAA,GAAkB,IAAI,KAAK,CAAA,CAAA;AAEjC,IAAM,MAAA,SAAA,GAAY,SAAwB,OAAO;AAAA,MAC/C,SAAA,EAAW,OAAQ,CAAA,KAAA,CAAM,SAAS,CAAA;AAAA,KAClC,CAAA,CAAA,CAAA;AACF,IAAM,MAAA,kBAAA,GAAqB,SAAS,MAAM,CAAC,GAAG,CAAE,CAAA,YAAA,CAAa,KAAK,CAAC,CAAC,CAAA,CAAA;AACpE,IAAA,MAAM,UAAU,QAAS,CAAA,MAAMC,SAAY,CAAA,KAAA,CAAM,OAAO,CAAC,CAAA,CAAA;AAEzD,IAAM,MAAA,gBAAA,GAAmB,OAAQ,CAAA,KAAA,CAAA;AACjC,IAAA,MAAM,SAAY,GAAA,QAAA,CAAiB,MAAM,KAAA,CAAM,MAAM,gBAAgB,CAAA,CAAA;AAMrE,IAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,OAAA,CAAA,EAAA,CAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,CAAA,qBAAA,CAAA,KAAA;AAAA,MACE;AAA8B,MAC9B,IAAE,CAAA,EAAA,GAAA,qBAA6B,yCAA4B,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAA,EAAA;AACzD,QAAI,qBAAA,CAAA,GAAA,CAAA,mBAAiD,CAAA,cAAA,EAAA,uBAAA,CAAA,CAAA;AACnD,OAAA;AAA0B,MACxB,IAAA,CAAA,EAAA,GAAA,iBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,iBAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAA,EAAA;AAAA,QACA,iBAAA,CAAA,GAAA,CAAA,mBAAA,CAAA,cAAA,EAAA,uBAAA,CAAA,CAAA;AAAA,OACF;AAAA,MACF,IAAA,CAAA,CAAA,EAAA,GAAA,iBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,iBAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAA,KAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,EAAA;AACA,QAAI,iBAAA,CAAA,GAAA,CAAA,gBAA6C,CAAA,cAAA,EAAA,uBAAA,CAAA,CAAA;AAC/C,OAAA;AAAsB,KACpB,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IACA,eAAA,CAAA,MAAA;AAAA,MACF,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACF,IAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,oBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAA,EAAA;AACA,QAAA,0BAC0B,CAAA,GAAA,CAAA,mBAChB,CAAA,cAAA,yBACR,CAAA,CAAA;AACA,OAAA;AAAsB,KACpB,CAAA,CAAA;AAAA,IACA,SAAA,WAAA,GAAA;AAAA,MACF,WAAA,EAAA,CAAA;AAAA,KACF;AAAA,IACF,SAAA,WAAA,GAAA;AAAA,MACA;AAAkB,MACpB,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAI,SAAA,UAAA,GAAA;AACF,MAAA,IAAA,EAAA,CAAA;AAA+B,MAC7B,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,KACA;AAAA,IACF,MAAA,YAAA,GAAA,WAAA,EAAA,CAAA;AAAA,IACF,SAAA,cAAA,CAAA,GAAA,IAAA,EAAA;AAAA,MACD,IAAA,CAAA,SAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AAED,KAAA;AACE,IAAY,SAAA,uBAAA,GAAA;AAAA,MACd,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAA,CAAA,EAAA,GAAA,CAAS,EAAc,GAAA,oBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AACrB,KAAA;AAAyB,IAC3B,SAAA,WAAA,GAAA;AAEA,KAAA;AACE,IAAA,SAAA,WAAwB,GAAA;AAAA,MAC1B,MAAA,SAAA,GAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAEA,MAAA,sBAAiC,CAAA,OAAA,CAAA,KAAA,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,EAAA,CAAA,CAAA;AAEjC,MAAA,yBAA2B,CAAa;AACtC,KAAK;AAAkB,IACzB,SAAA,wBAAA,CAAA,EAAA,EAAA;AAEA,MAAA,YAAmC,CAAA,KAAA,GAAA,EAAA,CAAA;AACjC,KAAqB;AAAkB,IACzC,SAAA,gBAAA,CAAA,CAAA,EAAA;AAEA,MAAA,IAAA,CAAA,eAAuB,CAAA,KAAA,EAAA;AAAA,QAEvB,CAAA,CAAA,cAAA,EAAA,CAAA;AAEA,QAAA,CAAA,CAAA,wBAAuB,EAAA,CAAA;AACrB,OAAM;AAEN,KAAA;AACA,IAAA,SAAA,uBAAqB,GAAA;AAAA,MACvB,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAA,iBAAqB,CAAA,KAAA,EAAA;AAAA,MACvB,IAAA,EAAA,CAAA;AAEA,MAAA,IAAA,CAAA,qBAAoC,CAAA,GAAA,KAAA,CAAA,IAAA,MAAA,SAAA,EAAA;AAClC,QAAI,CAAA,EAAC,mBAAuB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAC1B,OAAA;AACA,KAAA;AAA2B,IAC7B,SAAA,uBAAA,GAAA;AAAA,MACF,IAAA,CAAA,gBAAA,EAAA,KAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,OAAK,uBAAsB,EAAA;AAAA,MAC7B,UAAA;AAEA,MAAA,IAAA,EAAA,oBAA0C,CAAA,IAAA,CAAA;AACxC,MAAI,SAAA;AACF,MAAA;AAAwB,MAC1B,WAAA;AAAA,MACF,WAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAA,OAAK;AAAuB,MAC9B,QAAA,EAAA,SAAA;AAEA,MAAA,YAAgC;AAAA,MAC9B,WAAA;AAAA,MACA,cAAM;AAAyB,MAC/B,OAAA,EAAA,KAAA,CAAA,KAAA,EAAA,SAAA,CAAA;AAAA,MACA,WAAA,EAAA,KAAA,CAAA,KAAA,EAAA,aAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,mBAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACD,IAAA,EAAA,EAAA,EAAA,CAAA;AAED,MAAA,CAAA,CAAA,cAAsB,EAAA,CAAA;AAAA,MACpB,CAAU,EAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA;AAAA,QACV,aAAA,EAAA,IAAA;AAAA,OACA,CAAA,CAAA;AAAA,KACA,CAAA;AAAA,IACA,MAAA,sBAA+B,GAAA,CAAA,KAAA,KAAA;AAAA,MAC/B,IAAA,CAAA,OAAA,EAAmB,KAAA,CAAA,CAAA;AAAoB,KACxC,CAAA;AAED,IAAM,OAAA;AACJ,MAAA,CAAA;AACA,MAAA,EAAA;AAA0B,MAAA,SACT;AAAA,MACjB,SAAC;AAAA,MACH,kBAAA;AAEA,MAAM,YAAA;AACJ,MAAA;AAAmB,MACrB,YAAA;AAEA,MAAO,wBAAA;AAAA,MACL,sBAAA;AAAA,MACA,gBAAA;AAAA,MACA,WAAA;AAAA,MACA,UAAA;AAAA,MACA,uBAAA;AAAA,MACA,iBAAA;AAAA,MACA,uBAAA;AAAA,MACA,mBAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,oBAAA;AAAA,MACA,mBAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,SACA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,IAAA,EAAA,CAAA;AAAA,EACA,MAAA,iCAAA,GAAAC,gBAAA,CAAA,wBAAA,CAAA,CAAA;AAAA,EACA,MAAA,gCAAA,GAAAA,gBAAA,CAAA,uBAAA,CAAA,CAAA;AAAA,EACA,MAAA,uBAAA,GAAAA,gBAAA,CAAA,cAAA,CAAA,CAAA;AAAA,EACF,MAAA,wBAAA,GAAAA,gBAAA,CAAA,eAAA,CAAA,CAAA;AAAA,EACF,MAAA,qBAAA,GAAAA,gBAAA,CAAA,YAAA,CAAA,CAAA;AACF,EAAC,MAAA,oBAAA,GAAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;;;;;;;;;;;;AAvTC,MAAA,gBAAA,EAAA,IAAA,CAAA,aAAA;AAAA,MAyFM,kBAAA,EAAA,KAAA;AAAA,MAAA,YAAA,EAAA,IAAA,CAAA,OAAA,KAAA,OAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AAAA,MAzFA;AAA0C,MAAA,SAAA,EAAA,IAAA,CAAA,SAAA;;MAC9C,mBA0Da,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA,mBAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA;AAAA,MAAA,OAzDP,EAAA,IAAA,CAAA,OAAA;AAAA,MAAA,cACG,EAAA,IAAA,CAAA,WAAA;AAAA,MAAA,mBACE,EAAA,IAAA,CAAA,UAAA;AAAA,MAAA,YACa,EAAA,IAAA,CAAA,OAAA,KAAA,OAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AAAA,MAAA,yBACL,EAAA,KAAA;AAAA,MAAA,aACE,EAAA,IAAA,CAAA,oBAAA;AAAA,MAClB,oBAAmB,EAAA,IAAA,CAAA,WAAA;AAA0B,MAAA,QAChC,EAAA,IAAA,CAAA,QAAA;AAAA,MAAA,UACF,EAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,MAAA,UACC,EAAA,IAAA,CAAA,UAAM;AAAwB,MAAA;AACF,MAAA,UAC/B,EAAA,IAAA,CAAA,UAAA;AAAA,MAAA,YACK,EAAA,IAAA,CAAA,uBAAA;AAAA,MAAA,MACK,EAAA,IAAA,CAAA,iBAAA;AAAA,MACnB,YAAA,EAAA,IAAmB,CAAA,uBAAA;AAA0B,KAAA,EAAAC,WACpB,CAAA;AAAA,MAAA,OACZ,EAAAC,OAAA,CAAA,MAAA;AAAA,QACbC,WAAoB,CAAA,uBAAA,EAAA;AAAA,UACV,GAAA,EAAA,WAAA;AAAA,UACA,YAAA,EAAQ,IAAA,CAAA,SAAA;AAAe,UACrB,GAAA,EAAA,KAAA;AAAA,UACb,YAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,SACa,EAAA;AAAA,UACC,OAAA,EAAAD,OAAA,CAAA,MAAA;AAAA,YACPC,WAAA,CAAA,gCAAA,EAAA;AAAA,cACO,IAAA,EAAA,IAAA,CAAA,IAAA;AAAA,cAAA,gBAAA,EAAA,IAAA,CAAA,YAAA;AAEH,cAAA,aAkBM,YAAA;AAAA,cAAA,oBAAA,EAAA,IAAA,CAAA,wBAAA;AAAA,cAhBT,YAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,aACS,EAAA;AAAA,cACT,OAAA,EAAAD,OAAA,CAAA,MAAA;AAAA,gBACHC,6CAAgB,EAAA,IAAA,EAAA;AAAA,kBAAA,OAAA,EAAAD,OAAA,CAAA,MAAA;8BAYO,CAAA,IAAA,CAAA,MAAA,EAAA,UAAA,CAAA;AAAA,mBAAA,CAAA;AAAA,kBATf,CAAA,EAAA,CAAA;AAAA,iBACU,CAAA;AAAA,eAAA,CACjB;AAAY,cAAA,CAAA,EACY,CAAA;AAAA,aAAA,EAAA,CACvB,EAAa,CAAA,MAAA,EAAA,gBAAA,EAAA,sBAAA,EAAA,cAAA,CAAA,CAAA;AAAA,WAAA,CAAA;;AAIW,SAAA,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,YAAA,CAAA,CAAA;AAAA,OAAA,CAAA;AADC,MAAA,CAAA,EAAA,CAAA;AAAA,KAAA,EAAA;;;;;;;;;;;;;;;;AAKf,OAAA,GAAA,KAAA,CAAA;AAAc,KAAA,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,WAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,aAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,cAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAAA,WAAA,IAQbE,SAAA,EAAA,EAAAC,WAAA,CAAA,0BAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA;AAAA,MAAA,OAAA,EAAAH,OAAA,CAAA,MAAA;AAAA,QAAAC,WANT,CAAA,oBAAA,EAAAG,UAAA,CAAA,EAAA,GAAA,EAAA,qBAAA,EAAA,EAAA,IAAA,CAAA,WAAA,EAAA;AAAA,UAAA,IACL,EAAI,IAAA,CAAA,YAAA;AAAA,UAAA,IACJ,EAAK,IAAA,CAAA,IAAA;AAAA,UAAA,QACM,EAAA,IAAA,CAAA,QAAA;AAAA,UAAA,QAAA,EAAA,IAAA,CAAA,QAAA;8CAEY;AAAA,SAAA,CAAA,EAAA;AAAA,UAAA,OAAA,EAAAJ,OAAA,CAAA,MAAA;;;;;;;AAIb,UAAA,GAAA,EAAA,sBAAA;AA2BI,SAAA,EAAA,IAAA,CAAA,WAfJ,EAAA;AAAA,UAVZ,cAAA;AAEqB,UAAA,IACZ,EAAA,IAAA,CAAA,YAAA;AAAA,UAAA,IACA,EAAA,IAAA,CAAA,IAAA;AAAA,UAAA,KACI,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,cAAA,CAAA;AAAA,UAAA,QACA,EAAA,IAAA,CAAA,QAAA;AAAA,UAAA,QACH,EAAA,IAAA,CAAA,QAAA;AAAA,UAAA,YAAA,EAAA,IAAA,CAAA,CAAA,CAAA,4BAAA,CAAA;;AAEe,UAAA,OAAA,EAAAA,OAAA,CAAA,MAAA;AAAA,YAAAC,WAAA,CAAA,kBAAA,EAAA;;;;AAEzB,gBAAAA,iCAaY,CAAA;AAAA,eAZL,CAAA;AAAA,cACD,CAAA,EAAA,CAAA;AAAA,aACI,EAAW,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAAA,WAAA,CACnB;AAAK,UAAA,CAAA,EACE,CAAA;AAAA,SAAA,EAAA,EACA,EAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,CAAA,CAAA;AAAA,OACN,CAAA;AAAW,MAAA,CAAA,EAAA,CAAA;AACD,KAAA,CAAA,IAAAI,kBACA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,GAAA,EAAA,CAAA,CAAA,CAAA;AACG,CAAA;AAEyC,eAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,cAAA,CAAA,CAAA,CAAA;;;;"}