{"version": 3, "file": "tokens.mjs", "sources": ["../../../../../../packages/components/dropdown/src/tokens.ts"], "sourcesContent": ["import { PopperProps } from '@element-plus/components/popper'\nimport type { ComputedRef, InjectionKey, Ref } from 'vue'\n\nexport type ElDropdownInjectionContext = {\n  contentRef: Ref<HTMLElement | undefined>\n  role: ComputedRef<PopperProps['role']>\n  triggerId: ComputedRef<string>\n  isUsingKeyboard: Ref<boolean>\n  onItemLeave: (e: PointerEvent) => void\n  onItemEnter: (e: PointerEvent) => void\n}\n\nexport const DROPDOWN_INJECTION_KEY: InjectionKey<ElDropdownInjectionContext> =\n  Symbol('elDropdown')\n"], "names": [], "mappings": "AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,YAAY;;;;"}