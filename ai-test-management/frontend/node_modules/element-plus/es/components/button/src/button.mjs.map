{"version": 3, "file": "button.mjs", "sources": ["../../../../../../packages/components/button/src/button.ts"], "sourcesContent": ["import { useSizeProp } from '@element-plus/hooks'\nimport { buildProps, definePropType, iconPropType } from '@element-plus/utils'\nimport { Loading } from '@element-plus/icons-vue'\nimport type { Component, ExtractPropTypes } from 'vue'\n\nexport const buttonTypes = [\n  'default',\n  'primary',\n  'success',\n  'warning',\n  'info',\n  'danger',\n  /**\n   * @deprecated\n   * Text type will be deprecated in the next major version (3.0.0)\n   */\n  'text',\n  '',\n] as const\nexport const buttonNativeTypes = ['button', 'submit', 'reset'] as const\n\nexport const buttonProps = buildProps({\n  /**\n   * @description button size\n   */\n  size: useSizeProp,\n  /**\n   * @description disable the button\n   */\n  disabled: Boolean,\n  /**\n   * @description button type\n   */\n  type: {\n    type: String,\n    values: buttonTypes,\n    default: '',\n  },\n  /**\n   * @description icon component\n   */\n  icon: {\n    type: iconPropType,\n  },\n  /**\n   * @description native button type\n   */\n  nativeType: {\n    type: String,\n    values: buttonNativeTypes,\n    default: 'button',\n  },\n  /**\n   * @description determine whether it's loading\n   */\n  loading: Boolean,\n  /**\n   * @description customize loading icon component\n   */\n  loadingIcon: {\n    type: iconPropType,\n    default: () => Loading,\n  },\n  /**\n   * @description determine whether it's a plain button\n   */\n  plain: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description determine whether it's a text button\n   */\n  text: Boolean,\n  /**\n   * @description determine whether it's a link button\n   */\n  link: Boolean,\n  /**\n   * @description determine whether the text button background color is always on\n   */\n  bg: Boolean,\n  /**\n   * @description native button autofocus\n   */\n  autofocus: Boolean,\n  /**\n   * @description determine whether it's a round button\n   */\n  round: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description determine whether it's a circle button\n   */\n  circle: Boolean,\n  /**\n   * @description custom button color, automatically calculate `hover` and `active` color\n   */\n  color: String,\n  /**\n   * @description dark mode, which automatically converts `color` to dark mode colors\n   */\n  dark: Boolean,\n  /**\n   * @description automatically insert a space between two chinese characters\n   */\n  autoInsertSpace: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description custom element tag\n   */\n  tag: {\n    type: definePropType<string | Component>([String, Object]),\n    default: 'button',\n  },\n} as const)\nexport const buttonEmits = {\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\n\nexport type ButtonProps = ExtractPropTypes<typeof buttonProps>\nexport type ButtonEmits = typeof buttonEmits\n\nexport type ButtonType = ButtonProps['type']\nexport type ButtonNativeType = ButtonProps['nativeType']\n\nexport interface ButtonConfigContext {\n  type?: string\n  plain?: boolean\n  round?: boolean\n  autoInsertSpace?: boolean\n}\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,WAAW,GAAG;AAC3B,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,EAAE;AACJ,EAAE;AACU,MAAC,iBAAiB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE;AACnD,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,WAAW;AACvB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,iBAAiB;AAC7B,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,OAAO,EAAE,MAAM,OAAO;AAC1B,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,EAAE,EAAE,OAAO;AACb,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;AAC3B,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C;;;;"}