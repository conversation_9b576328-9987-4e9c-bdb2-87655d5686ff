{"version": 3, "file": "grid.mjs", "sources": ["../../../../../../packages/components/table-v2/src/grid.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport {\n  virtualizedGridProps,\n  virtualizedListProps,\n} from '@element-plus/components/virtual-list'\nimport {\n  classType,\n  columns,\n  dataType,\n  fixedDataType,\n  requiredNumber,\n  styleType,\n} from './common'\nimport { tableV2HeaderProps } from './header'\nimport { tableV2RowProps } from './row'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { ItemSize } from '@element-plus/components/virtual-list'\n\nexport type onRowRenderedParams = {\n  rowCacheStart: number\n  rowCacheEnd: number\n  rowVisibleStart: number\n  rowVisibleEnd: number\n}\n\nexport const tableV2GridProps = buildProps({\n  columns,\n  data: dataType,\n  fixedData: fixedDataType,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n\n  /**\n   * Size related attributes\n   */\n  width: requiredNumber,\n  height: requiredNumber,\n\n  headerWidth: requiredNumber,\n  headerHeight: tableV2HeaderProps.headerHeight,\n\n  bodyWidth: requiredNumber,\n  rowHeight: requiredNumber,\n\n  /**\n   * Special attributes\n   */\n  cache: virtualizedListProps.cache,\n  useIsScrolling: Boolean,\n  scrollbarAlwaysOn: virtualizedGridProps.scrollbarAlwaysOn,\n  scrollbarStartGap: virtualizedGridProps.scrollbarStartGap,\n  scrollbarEndGap: virtualizedGridProps.scrollbarEndGap,\n\n  /**\n   * CSS attributes\n   */\n  class: classType,\n  style: styleType,\n  containerStyle: styleType,\n\n  getRowHeight: {\n    type: definePropType<ItemSize>(Function),\n    required: true,\n  },\n  rowKey: tableV2RowProps.rowKey,\n\n  /**\n   * Event handlers\n   */\n  onRowsRendered: {\n    type: definePropType<(params: onRowRenderedParams) => void>(Function),\n  },\n  onScroll: {\n    type: definePropType<(...args: any[]) => void>(Function),\n  },\n} as const)\n\nexport type TableV2GridProps = ExtractPropTypes<typeof tableV2GridProps>\n"], "names": [], "mappings": ";;;;;;AAeY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,OAAO;AACT,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,SAAS,EAAE,aAAa;AAC1B,EAAE,kBAAkB,EAAE,eAAe,CAAC,kBAAkB;AACxD,EAAE,KAAK,EAAE,cAAc;AACvB,EAAE,MAAM,EAAE,cAAc;AACxB,EAAE,WAAW,EAAE,cAAc;AAC7B,EAAE,YAAY,EAAE,kBAAkB,CAAC,YAAY;AAC/C,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,SAAS,EAAE,cAAc;AAC3B,EAAE,KAAK,EAAE,oBAAoB,CAAC,KAAK;AACnC,EAAE,cAAc,EAAE,OAAO;AACzB,EAAE,iBAAiB,EAAE,oBAAoB,CAAC,iBAAiB;AAC3D,EAAE,iBAAiB,EAAE,oBAAoB,CAAC,iBAAiB;AAC3D,EAAE,eAAe,EAAE,oBAAoB,CAAC,eAAe;AACvD,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,MAAM,EAAE,eAAe,CAAC,MAAM;AAChC,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC;;;;"}