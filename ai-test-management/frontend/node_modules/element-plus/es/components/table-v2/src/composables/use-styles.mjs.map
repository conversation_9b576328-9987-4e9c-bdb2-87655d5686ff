{"version": 3, "file": "use-styles.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/composables/use-styles.ts"], "sourcesContent": ["import { ComputedRef, computed, unref } from 'vue'\nimport { addUnit, isNumber } from '@element-plus/utils'\nimport { enforceUnit, sum } from '../utils'\n\nimport type { CSSProperties } from 'vue'\nimport type { TableV2Props } from '../table'\nimport type { UseColumnsReturn } from './use-columns'\n\ntype UseStyleProps = {\n  columnsTotalWidth: UseColumnsReturn['columnsTotalWidth']\n  fixedColumnsOnLeft: UseColumnsReturn['fixedColumnsOnLeft']\n  fixedColumnsOnRight: UseColumnsReturn['fixedColumnsOnRight']\n  rowsHeight: ComputedRef<number>\n}\n\nexport const useStyles = (\n  props: TableV2Props,\n  {\n    columnsTotalWidth,\n    rowsHeight,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n  }: UseStyleProps\n) => {\n  const bodyWidth = computed(() => {\n    const { fixed, width, vScrollbarSize } = props\n    const ret = width - vScrollbarSize\n    return fixed ? Math.max(Math.round(unref(columnsTotalWidth)), ret) : ret\n  })\n\n  const mainTableHeight = computed(() => {\n    const { height = 0, maxHeight = 0, footerHeight, hScrollbarSize } = props\n\n    if (maxHeight > 0) {\n      const _fixedRowsHeight = unref(fixedRowsHeight)\n      const _rowsHeight = unref(rowsHeight)\n      const _headerHeight = unref(headerHeight)\n      const total =\n        _headerHeight + _fixedRowsHeight + _rowsHeight + hScrollbarSize\n\n      return Math.min(total, maxHeight - footerHeight)\n    }\n\n    return height - footerHeight\n  })\n\n  const fixedTableHeight = computed(() => {\n    const { maxHeight } = props\n    const tableHeight = unref(mainTableHeight)\n    if (isNumber(maxHeight) && maxHeight > 0) return tableHeight\n\n    const totalHeight =\n      unref(rowsHeight) + unref(headerHeight) + unref(fixedRowsHeight)\n\n    return Math.min(tableHeight, totalHeight)\n  })\n\n  const mapColumn = (column: TableV2Props['columns'][number]) => column.width\n\n  const leftTableWidth = computed(() =>\n    sum(unref(fixedColumnsOnLeft).map(mapColumn))\n  )\n\n  const rightTableWidth = computed(() =>\n    sum(unref(fixedColumnsOnRight).map(mapColumn))\n  )\n\n  const headerHeight = computed(() => sum(props.headerHeight))\n\n  const fixedRowsHeight = computed(() => {\n    return (props.fixedData?.length || 0) * props.rowHeight\n  })\n\n  const windowHeight = computed(() => {\n    return unref(mainTableHeight) - unref(headerHeight) - unref(fixedRowsHeight)\n  })\n\n  const rootStyle = computed<CSSProperties>(() => {\n    const { style = {}, height, width } = props\n    return enforceUnit({\n      ...style,\n      height,\n      width,\n    })\n  })\n\n  const footerHeight = computed(() =>\n    enforceUnit({ height: props.footerHeight })\n  )\n\n  const emptyStyle = computed<CSSProperties>(() => ({\n    top: addUnit(unref(headerHeight)),\n    bottom: addUnit(props.footerHeight),\n    width: addUnit(props.width),\n  }))\n\n  return {\n    bodyWidth,\n    fixedTableHeight,\n    mainTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    windowHeight,\n    footerHeight,\n    emptyStyle,\n    rootStyle,\n    headerHeight,\n  }\n}\n\nexport type UseStyleReturn = ReturnType<typeof useStyles>\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,SAAS,GAAG,CAAC,KAAK,EAAE;AACjC,EAAE,iBAAiB;AACnB,EAAE,UAAU;AACZ,EAAE,kBAAkB;AACpB,EAAE,mBAAmB;AACrB,CAAC,KAAK;AACN,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;AACnD,IAAI,MAAM,GAAG,GAAG,KAAK,GAAG,cAAc,CAAC;AACvC,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AAC7E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;AAC7F,IAAI,IAAI,SAAS,GAAG,CAAC,EAAE;AACvB,MAAM,MAAM,gBAAgB,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;AACtD,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAC5C,MAAM,MAAM,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAChD,MAAM,MAAM,KAAK,GAAG,aAAa,GAAG,gBAAgB,GAAG,WAAW,GAAG,cAAc,CAAC;AACpF,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,GAAG,aAAa,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,MAAM,GAAG,aAAa,CAAC;AAClC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM;AAC1C,IAAI,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;AAChC,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;AAC/C,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,GAAG,CAAC;AAC5C,MAAM,OAAO,WAAW,CAAC;AACzB,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;AACzF,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC9C,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC;AAC7C,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACvF,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACzF,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AAC/D,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC;AAC1F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;AACjF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AAChD,IAAI,OAAO,WAAW,CAAC;AACvB,MAAM,GAAG,KAAK;AACd,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AACnF,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO;AACrC,IAAI,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACrC,IAAI,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;AACvC,IAAI,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AAC/B,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ;;;;"}