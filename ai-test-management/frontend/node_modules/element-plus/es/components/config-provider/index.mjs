import ConfigProvider from './src/config-provider.mjs';
export { messageConfig } from './src/config-provider.mjs';
export { configProviderProps } from './src/config-provider-props.mjs';
export { configProviderContextKey } from './src/constants.mjs';
export { provideGlobalConfig, useGlobalComponentSettings, useGlobalConfig } from './src/hooks/use-global-config.mjs';
import { withInstall } from '../../utils/vue/install.mjs';

const ElConfigProvider = withInstall(ConfigProvider);

export { ElConfigProvider, ElConfigProvider as default };
//# sourceMappingURL=index.mjs.map
