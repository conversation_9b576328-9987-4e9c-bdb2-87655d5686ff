{"version": 3, "file": "timeline-item2.mjs", "sources": ["../../../../../../packages/components/timeline/src/timeline-item.vue"], "sourcesContent": ["<template>\n  <li :class=\"[ns.b(), { [ns.e('center')]: center }]\">\n    <div :class=\"ns.e('tail')\" />\n    <div\n      v-if=\"!$slots.dot\"\n      :class=\"defaultNodeKls\"\n      :style=\"{\n        backgroundColor: color,\n      }\"\n    >\n      <el-icon v-if=\"icon\" :class=\"ns.e('icon')\">\n        <component :is=\"icon\" />\n      </el-icon>\n    </div>\n    <div v-if=\"$slots.dot\" :class=\"ns.e('dot')\">\n      <slot name=\"dot\" />\n    </div>\n\n    <div :class=\"ns.e('wrapper')\">\n      <div\n        v-if=\"!hideTimestamp && placement === 'top'\"\n        :class=\"[ns.e('timestamp'), ns.is('top')]\"\n      >\n        {{ timestamp }}\n      </div>\n\n      <div :class=\"ns.e('content')\">\n        <slot />\n      </div>\n\n      <div\n        v-if=\"!hideTimestamp && placement === 'bottom'\"\n        :class=\"[ns.e('timestamp'), ns.is('bottom')]\"\n      >\n        {{ timestamp }}\n      </div>\n    </div>\n  </li>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { timelineItemProps } from './timeline-item'\n\ndefineOptions({\n  name: 'ElTimelineItem',\n})\n\nconst props = defineProps(timelineItemProps)\n\nconst ns = useNamespace('timeline-item')\nconst defaultNodeKls = computed(() => [\n  ns.e('node'),\n  ns.em('node', props.size || ''),\n  ns.em('node', props.type || ''),\n  ns.is('hollow', props.hollow),\n])\n</script>\n"], "names": [], "mappings": ";;;;;;mCA8Cc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAK,aAAa,eAAe,CAAA,CAAA;AACvC,IAAM,MAAA,cAAA,GAAiB,SAAS,MAAM;AAAA,MACpC,EAAA,CAAG,EAAE,MAAM,CAAA;AAAA,MACX,EAAG,CAAA,EAAA,CAAG,MAAQ,EAAA,KAAA,CAAM,QAAQ,EAAE,CAAA;AAAA,MAC9B,EAAG,CAAA,EAAA,CAAG,MAAQ,EAAA,KAAA,CAAM,QAAQ,EAAE,CAAA;AAAA,MAC9B,EAAG,CAAA,EAAA,CAAG,QAAU,EAAA,KAAA,CAAM,MAAM,CAAA;AAAA,KAC7B,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}