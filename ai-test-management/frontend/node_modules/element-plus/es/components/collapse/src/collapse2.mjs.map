{"version": 3, "file": "collapse2.mjs", "sources": ["../../../../../../packages/components/collapse/src/collapse.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isNumber,\n  isString,\n  mutable,\n} from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Arrayable, Awaitable } from '@element-plus/utils'\n\nexport type CollapseActiveName = string | number\nexport type CollapseModelValue = Arrayable<CollapseActiveName>\n\nexport type CollapseIconPositionType = 'left' | 'right'\n\nexport const emitChangeFn = (value: CollapseModelValue) =>\n  isNumber(value) || isString(value) || isArray(value)\n\nexport const collapseProps = buildProps({\n  /**\n   * @description whether to activate accordion mode\n   */\n  accordion: Boolean,\n  /**\n   * @description currently active panel, the type is `string` in accordion mode, otherwise it is `array`\n   */\n  modelValue: {\n    type: definePropType<CollapseModelValue>([Array, String, Number]),\n    default: () => mutable([] as const),\n  },\n  /**\n   * @description set expand icon position\n   */\n  expandIconPosition: {\n    type: definePropType<CollapseIconPositionType>([String]),\n    default: 'right',\n  },\n  /**\n   * @description before-collapse hook before the collapse state changes. If `false` is returned or a `Promise` is returned and then is rejected, will stop collapsing\n   */\n  beforeCollapse: {\n    type: definePropType<(name: CollapseActiveName) => Awaitable<boolean>>(\n      Function\n    ),\n  },\n} as const)\nexport type CollapseProps = ExtractPropTypes<typeof collapseProps>\n\nexport const collapseEmits = {\n  [UPDATE_MODEL_EVENT]: emitChangeFn,\n  [CHANGE_EVENT]: emitChangeFn,\n}\nexport type CollapseEmits = typeof collapseEmits\n"], "names": [], "mappings": ";;;;;;AASY,MAAC,YAAY,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE;AAChF,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC;AAClC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,CAAC,kBAAkB,GAAG,YAAY;AACpC,EAAE,CAAC,YAAY,GAAG,YAAY;AAC9B;;;;"}