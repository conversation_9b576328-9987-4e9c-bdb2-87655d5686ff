{"version": 3, "file": "collapse-item.mjs", "sources": ["../../../../../../packages/components/collapse/src/collapse-item.ts"], "sourcesContent": ["import { buildProps, definePropType, iconPropType } from '@element-plus/utils'\nimport { ArrowRight } from '@element-plus/icons-vue'\nimport type { ExtractPropTypes } from 'vue'\nimport type { CollapseActiveName } from './collapse'\n\nexport const collapseItemProps = buildProps({\n  /**\n   * @description title of the panel\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description unique identification of the panel\n   */\n  name: {\n    type: definePropType<CollapseActiveName>([String, Number]),\n    default: undefined,\n  },\n  /**\n   * @description icon of the collapse item\n   */\n  icon: {\n    type: iconPropType,\n    default: ArrowRight,\n  },\n  /**\n   * @description disable the collapse item\n   */\n  disabled: Boolean,\n} as const)\nexport type CollapseItemProps = ExtractPropTypes<typeof collapseItemProps>\n"], "names": [], "mappings": ";;;;AAEY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC;;;;"}