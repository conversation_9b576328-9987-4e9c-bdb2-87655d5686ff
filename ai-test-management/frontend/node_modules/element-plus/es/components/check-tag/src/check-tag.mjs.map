{"version": 3, "file": "check-tag.mjs", "sources": ["../../../../../../packages/components/check-tag/src/check-tag.ts"], "sourcesContent": ["import { buildProps, isBoolean } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\n\nimport type CheckTag from './check-tag.vue'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const checkTagProps = buildProps({\n  /**\n   * @description is checked\n   */\n  checked: Boolean,\n  /**\n   * @description whether the check-tag is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description type of Tag\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'info', 'warning', 'danger'],\n    default: 'primary',\n  },\n} as const)\nexport type CheckTagProps = ExtractPropTypes<typeof checkTagProps>\n\nexport const checkTagEmits = {\n  'update:checked': (value: boolean) => isBoolean(value),\n  [CHANGE_EVENT]: (value: boolean) => isBoolean(value),\n}\nexport type CheckTagEmits = typeof checkTagEmits\n\nexport type CheckTagInstance = InstanceType<typeof CheckTag> & unknown\n"], "names": [], "mappings": ";;;;AAEY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;AAC/D,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,gBAAgB,EAAE,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;AAC/C,EAAE,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;AAC7C;;;;"}