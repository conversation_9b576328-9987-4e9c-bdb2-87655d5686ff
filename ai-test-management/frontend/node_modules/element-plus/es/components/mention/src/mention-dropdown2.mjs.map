{"version": 3, "file": "mention-dropdown2.mjs", "sources": ["../../../../../../packages/components/mention/src/mention-dropdown.vue"], "sourcesContent": ["<template>\n  <div ref=\"dropdownRef\" :class=\"ns.b('dropdown')\">\n    <div v-if=\"$slots.header\" :class=\"ns.be('dropdown', 'header')\">\n      <slot name=\"header\" />\n    </div>\n    <el-scrollbar\n      v-show=\"options.length > 0 && !loading\"\n      :id=\"contentId\"\n      ref=\"scrollbarRef\"\n      tag=\"ul\"\n      :wrap-class=\"ns.be('dropdown', 'wrap')\"\n      :view-class=\"ns.be('dropdown', 'list')\"\n      role=\"listbox\"\n      :aria-label=\"ariaLabel\"\n      aria-orientation=\"vertical\"\n    >\n      <li\n        v-for=\"(item, index) in options\"\n        :id=\"`${contentId}-${index}`\"\n        ref=\"optionRefs\"\n        :key=\"index\"\n        :class=\"optionkls(item, index)\"\n        role=\"option\"\n        :aria-disabled=\"item.disabled || disabled || undefined\"\n        :aria-selected=\"hoveringIndex === index\"\n        @mousemove=\"handleMouseEnter(index)\"\n        @click.stop=\"handleSelect(item)\"\n      >\n        <slot name=\"label\" :item=\"item\" :index=\"index\">\n          <span>{{ item.label ?? item.value }}</span>\n        </slot>\n      </li>\n    </el-scrollbar>\n    <div v-if=\"loading\" :class=\"ns.be('dropdown', 'loading')\">\n      <slot name=\"loading\"> {{ t('el.mention.loading') }} </slot>\n    </div>\n    <div v-if=\"$slots.footer\" :class=\"ns.be('dropdown', 'footer')\">\n      <slot name=\"footer\" />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, nextTick, ref, watch } from 'vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { scrollIntoView } from '@element-plus/utils'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport { mentionDropdownEmits, mentionDropdownProps } from './mention-dropdown'\n\nimport type { MentionOption } from './types'\n\ndefineOptions({\n  name: 'ElMentionDropdown',\n})\n\nconst props = defineProps(mentionDropdownProps)\nconst emit = defineEmits(mentionDropdownEmits)\n\nconst ns = useNamespace('mention')\nconst { t } = useLocale()\nconst hoveringIndex = ref(-1)\n\nconst scrollbarRef = ref<InstanceType<typeof ElScrollbar>>()\nconst optionRefs = ref<HTMLElement[]>()\nconst dropdownRef = ref<HTMLElement>()\n\nconst optionkls = (item: MentionOption, index: number) => [\n  ns.be('dropdown', 'item'),\n  ns.is('hovering', hoveringIndex.value === index),\n  ns.is('disabled', item.disabled || props.disabled),\n]\n\nconst handleSelect = (item: MentionOption) => {\n  if (item.disabled || props.disabled) return\n  emit('select', item)\n}\n\nconst handleMouseEnter = (index: number) => {\n  hoveringIndex.value = index\n}\n\nconst filteredAllDisabled = computed(\n  () => props.disabled || props.options.every((item) => item.disabled)\n)\n\nconst hoverOption = computed(() => props.options[hoveringIndex.value])\n\nconst selectHoverOption = () => {\n  if (!hoverOption.value) return\n  emit('select', hoverOption.value)\n}\n\nconst navigateOptions = (direction: 'next' | 'prev') => {\n  const { options } = props\n  if (options.length === 0 || filteredAllDisabled.value) return\n\n  if (direction === 'next') {\n    hoveringIndex.value++\n    if (hoveringIndex.value === options.length) {\n      hoveringIndex.value = 0\n    }\n  } else if (direction === 'prev') {\n    hoveringIndex.value--\n    if (hoveringIndex.value < 0) {\n      hoveringIndex.value = options.length - 1\n    }\n  }\n  const option = options[hoveringIndex.value]\n  if (option.disabled) {\n    navigateOptions(direction)\n    return\n  }\n  nextTick(() => scrollToOption(option))\n}\n\nconst scrollToOption = (option: MentionOption) => {\n  const { options } = props\n\n  const index = options.findIndex((item) => item.value === option.value)\n  const target = optionRefs.value?.[index]\n\n  if (target) {\n    const menu = dropdownRef.value?.querySelector?.(\n      `.${ns.be('dropdown', 'wrap')}`\n    )\n    if (menu) {\n      scrollIntoView(menu as HTMLElement, target)\n    }\n  }\n  scrollbarRef.value?.handleScroll()\n}\n\nconst resetHoveringIndex = () => {\n  if (filteredAllDisabled.value || props.options.length === 0) {\n    hoveringIndex.value = -1\n  } else {\n    hoveringIndex.value = 0\n  }\n}\n\nwatch(() => props.options, resetHoveringIndex, {\n  immediate: true,\n})\n\ndefineExpose({\n  hoveringIndex,\n  navigateOptions,\n  selectHoverOption,\n  hoverOption,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;mCAmDc,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AACjC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,aAAA,GAAgB,IAAI,CAAE,CAAA,CAAA,CAAA;AAE5B,IAAA,MAAM,eAAe,GAAsC,EAAA,CAAA;AAC3D,IAAA,MAAM,aAAa,GAAmB,EAAA,CAAA;AACtC,IAAA,MAAM,cAAc,GAAiB,EAAA,CAAA;AAErC,IAAM,MAAA,SAAA,GAAY,CAAC,IAAA,EAAqB,KAAkB,KAAA;AAAA,MACxD,EAAA,CAAG,EAAG,CAAA,UAAA,EAAY,MAAM,CAAA;AAAA,MACxB,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,aAAA,CAAc,UAAU,KAAK,CAAA;AAAA,MAC/C,GAAG,EAAG,CAAA,UAAA,EAAY,IAAK,CAAA,QAAA,IAAY,MAAM,QAAQ,CAAA;AAAA,KACnD,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,IAAwB,KAAA;AAC5C,MAAI,IAAA,IAAA,CAAK,QAAY,IAAA,KAAA,CAAM,QAAU;AACrC,QAAA;AAAmB,MACrB,IAAA,CAAA,QAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,gBAAsB,GAAA,CAAA,KAAA,KAAA;AAAA,MACxB,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AAA4B,IAC1B,MAAA,mBAAwB,GAAA,eAAoB,KAAU,CAAA,QAAA,IAAK,KAAQ,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IACrE,MAAA,WAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAEA,IAAA,MAAM,iBAAuB,GAAA,MAAA;AAE7B,MAAA,IAAM;AACJ,QAAI;AACJ,MAAK,IAAA,CAAA,QAAA,EAAU,YAAY,KAAK,CAAA,CAAA;AAAA,KAClC,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,SAA+B,KAAA;AACtD,MAAM,MAAA,EAAE,SAAY,GAAA,KAAA,CAAA;AACpB,MAAA,IAAI,OAAQ,CAAA,MAAA,KAAW,CAAK,IAAA,mBAAA,CAAoB,KAAO;AAEvD,QAAA;AACE,MAAc,IAAA,SAAA,KAAA,MAAA,EAAA;AACd,QAAI,aAAA,CAAA,KAAwB,EAAA,CAAA;AAC1B,QAAA,IAAA,aAAsB,CAAA,KAAA,KAAA,OAAA,CAAA,MAAA,EAAA;AAAA,UACxB,aAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAAA,SACF;AACE,OAAc,MAAA,IAAA,SAAA,KAAA,MAAA,EAAA;AACd,QAAI,aAAA,CAAA;AACF,QAAc,IAAA,aAAA,CAAA,KAAA;AAAyB,UACzC,aAAA,CAAA,KAAA,GAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AAAA,SACF;AACA,OAAM;AACN,MAAA,YAAqB,GAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AACnB,MAAA,IAAA,MAAA,CAAA,QAAyB,EAAA;AACzB,QAAA,eAAA,CAAA,SAAA,CAAA,CAAA;AAAA,QACF,OAAA;AACA,OAAS;AAA4B,MACvC,QAAA,CAAA,MAAA,cAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,cAAc,GAAA,CAAA,MAAA,KAAA;AAEpB,MAAM,IAAA,EAAA,EAAA,EAAA,EAAA,EAAQ;AACd,MAAM,MAAA,EAAA,OAAS,EAAW,GAAA,KAAA,CAAA;AAE1B,MAAA,MAAY,KAAA,GAAA,OAAA,CAAA,SAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,KAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACV,MAAM,MAAA,MAAA,GAAO,gBAAmB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MAAA,IAC9B,MAAI,EAAM;AAAmB,QAC/B,MAAA,IAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAI,IAAM,EAAA;AACR,UAAA,cAAA,CAAe,MAAqB,MAAM,CAAA,CAAA;AAAA,SAC5C;AAAA,OACF;AACA,MAAA,CAAA,EAAA,GAAA,kBAAiC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AAAA,KACnC,CAAA;AAEA,IAAA,MAAM,qBAAqB,MAAM;AAC/B,MAAA,IAAI,mBAAoB,CAAA,KAAA,IAAS,KAAM,CAAA,OAAA,CAAQ,WAAW,CAAG,EAAA;AAC3D,QAAA,aAAA,CAAc,KAAQ,GAAA,CAAA,CAAA,CAAA;AAAA,OACjB,MAAA;AACL,QAAA,aAAA,CAAc,KAAQ,GAAA,CAAA,CAAA;AAAA,OACxB;AAAA,KACF,CAAA;AAEA,IAAM,KAAA,CAAA,MAAM,KAAM,CAAA,OAAA,EAAS,kBAAoB,EAAA;AAAA,MAC7C,SAAW,EAAA,IAAA;AAAA,KACZ,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MACX,aAAA;AAAA,MACA,eAAA;AAAA,MACA,iBAAA;AAAA,MACA,WAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}