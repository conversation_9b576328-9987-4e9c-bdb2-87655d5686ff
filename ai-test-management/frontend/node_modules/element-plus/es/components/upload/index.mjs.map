{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/upload/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Upload from './src/upload.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElUpload: SFCWithInstall<typeof Upload> = withInstall(Upload)\nexport default ElUpload\n\nexport * from './src/upload'\nexport * from './src/upload-content'\nexport * from './src/upload-list'\nexport * from './src/upload-dragger'\nexport * from './src/constants'\n"], "names": [], "mappings": ";;;;;;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}