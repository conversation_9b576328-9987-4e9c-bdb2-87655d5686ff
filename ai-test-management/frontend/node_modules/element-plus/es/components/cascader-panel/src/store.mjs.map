{"version": 3, "file": "store.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/store.ts"], "sourcesContent": ["import { isEqual } from 'lodash-unified'\nimport { type Nullable, isPropAbsent } from '@element-plus/utils'\nimport Node from './node'\n\nimport type {\n  CascaderConfig,\n  CascaderNodePathValue,\n  CascaderNodeValue,\n  CascaderOption,\n} from './node'\n\nconst flatNodes = (nodes: Node[], leafOnly: boolean) => {\n  return nodes.reduce((res, node) => {\n    if (node.isLeaf) {\n      res.push(node)\n    } else {\n      !leafOnly && res.push(node)\n      res = res.concat(flatNodes(node.children, leafOnly))\n    }\n    return res\n  }, [] as Node[])\n}\n\nexport default class Store {\n  readonly nodes: Node[]\n  readonly allNodes: Node[]\n  readonly leafNodes: Node[]\n\n  constructor(data: CascaderOption[], readonly config: CascaderConfig) {\n    const nodes = (data || []).map(\n      (nodeData) => new Node(nodeData, this.config)\n    )\n    this.nodes = nodes\n    this.allNodes = flatNodes(nodes, false)\n    this.leafNodes = flatNodes(nodes, true)\n  }\n\n  getNodes() {\n    return this.nodes\n  }\n\n  getFlattedNodes(leafOnly: boolean) {\n    return leafOnly ? this.leafNodes : this.allNodes\n  }\n\n  appendNode(nodeData: CascaderOption, parentNode?: Node) {\n    const node = parentNode\n      ? parentNode.appendChild(nodeData)\n      : new Node(nodeData, this.config)\n\n    if (!parentNode) this.nodes.push(node)\n\n    this.appendAllNodesAndLeafNodes(node)\n  }\n\n  appendNodes(nodeDataList: CascaderOption[], parentNode: Node) {\n    nodeDataList.forEach((nodeData) => this.appendNode(nodeData, parentNode))\n  }\n\n  appendAllNodesAndLeafNodes(node: Node) {\n    this.allNodes.push(node)\n    node.isLeaf && this.leafNodes.push(node)\n    if (node.children) {\n      node.children.forEach((subNode) => {\n        this.appendAllNodesAndLeafNodes(subNode)\n      })\n    }\n  }\n\n  // when checkStrictly, leaf node first\n  getNodeByValue(\n    value: CascaderNodeValue | CascaderNodePathValue,\n    leafOnly = false\n  ): Nullable<Node> {\n    if (isPropAbsent(value)) return null\n\n    const node = this.getFlattedNodes(leafOnly).find(\n      (node) => isEqual(node.value, value) || isEqual(node.pathValues, value)\n    )\n\n    return node || null\n  }\n\n  getSameNode(node: Node): Nullable<Node> {\n    if (!node) return null\n\n    const node_ = this.getFlattedNodes(false).find(\n      ({ value, level }) => isEqual(node.value, value) && node.level === level\n    )\n\n    return node_ || null\n  }\n}\n"], "names": [], "mappings": ";;;;AAGA,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,QAAQ,KAAK;AACvC,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;AACrC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,CAAC,CAAC;AACa,MAAM,KAAK,CAAC;AAC3B,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE;AAC5B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,QAAQ,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAClF,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,GAAG;AACH,EAAE,eAAe,CAAC,QAAQ,EAAE;AAC5B,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;AACrD,GAAG;AACH,EAAE,UAAU,CAAC,QAAQ,EAAE,UAAU,EAAE;AACnC,IAAI,MAAM,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACjG,IAAI,IAAI,CAAC,UAAU;AACnB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,WAAW,CAAC,YAAY,EAAE,UAAU,EAAE;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;AAC9E,GAAG;AACH,EAAE,0BAA0B,CAAC,IAAI,EAAE;AACnC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACzC,QAAQ,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACjD,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,cAAc,CAAC,KAAK,EAAE,QAAQ,GAAG,KAAK,EAAE;AAC1C,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC;AAC3B,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;AACjI,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC;AACxB,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;AAC7H,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AACzB,GAAG;AACH;;;;"}