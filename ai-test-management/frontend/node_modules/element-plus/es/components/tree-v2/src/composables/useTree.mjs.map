{"version": 3, "file": "useTree.mjs", "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useTree.ts"], "sourcesContent": ["import { computed, ref, shallowRef, watch } from 'vue'\nimport { isObject } from '@element-plus/utils'\nimport {\n  CURRENT_CHANGE,\n  NODE_CLICK,\n  NODE_COLLAPSE,\n  NODE_DROP,\n  NODE_EXPAND,\n  TreeOptionsEnum,\n} from '../virtual-tree'\nimport { useCheck } from './useCheck'\nimport { useFilter } from './useFilter'\nimport type {\n  FixedSizeList,\n  Alignment as ScrollStrategy,\n} from '@element-plus/components/virtual-list'\nimport type { SetupContext } from 'vue'\nimport type { treeEmits } from '../virtual-tree'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type {\n  Tree,\n  TreeData,\n  TreeKey,\n  TreeNode,\n  TreeNodeData,\n  TreeProps,\n} from '../types'\n\nexport function useTree(\n  props: TreeProps,\n  emit: SetupContext<typeof treeEmits>['emit']\n) {\n  const expandedKeySet = ref<Set<TreeKey>>(new Set(props.defaultExpandedKeys))\n  const currentKey = ref<TreeKey | undefined>()\n  const tree = shallowRef<Tree | undefined>()\n  const listRef = ref<typeof FixedSizeList | undefined>()\n\n  const {\n    isIndeterminate,\n    isChecked,\n    toggleCheckbox,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n  } = useCheck(props, tree)\n\n  const { doFilter, hiddenNodeKeySet, isForceHiddenExpandIcon } = useFilter(\n    props,\n    tree\n  )\n\n  const valueKey = computed(() => {\n    return props.props?.value || TreeOptionsEnum.KEY\n  })\n  const childrenKey = computed(() => {\n    return props.props?.children || TreeOptionsEnum.CHILDREN\n  })\n  const disabledKey = computed(() => {\n    return props.props?.disabled || TreeOptionsEnum.DISABLED\n  })\n  const labelKey = computed(() => {\n    return props.props?.label || TreeOptionsEnum.LABEL\n  })\n\n  const flattenTree = computed(() => {\n    const expandedKeys = expandedKeySet.value\n    const hiddenKeys = hiddenNodeKeySet.value\n    const flattenNodes: TreeNode[] = []\n    const nodes = tree.value?.treeNodes || []\n\n    const stack: TreeNode[] = []\n    for (let i = nodes.length - 1; i >= 0; --i) {\n      stack.push(nodes[i])\n    }\n    while (stack.length) {\n      const node = stack.pop()!\n      if (hiddenKeys.has(node.key)) continue\n\n      flattenNodes.push(node)\n      if (node.children && expandedKeys.has(node.key)) {\n        for (let i = node.children.length - 1; i >= 0; --i) {\n          stack.push(node.children[i])\n        }\n      }\n    }\n\n    return flattenNodes\n  })\n\n  const isNotEmpty = computed(() => {\n    return flattenTree.value.length > 0\n  })\n\n  function createTree(data: TreeData): Tree {\n    const treeNodeMap: Map<TreeKey, TreeNode> = new Map()\n    const levelTreeNodeMap: Map<number, TreeNode[]> = new Map()\n    let maxLevel = 1\n    function traverse(\n      nodes: TreeData,\n      level = 1,\n      parent: TreeNode | undefined = undefined\n    ) {\n      const siblings: TreeNode[] = []\n      for (const rawNode of nodes) {\n        const value = getKey(rawNode)\n        const node: TreeNode = {\n          level,\n          key: value,\n          data: rawNode,\n        }\n        node.label = getLabel(rawNode)\n        node.parent = parent\n        const children = getChildren(rawNode)\n        node.disabled = getDisabled(rawNode)\n        node.isLeaf = !children || children.length === 0\n        if (children && children.length) {\n          node.children = traverse(children, level + 1, node)\n        }\n        siblings.push(node)\n        treeNodeMap.set(value, node)\n        if (!levelTreeNodeMap.has(level)) {\n          levelTreeNodeMap.set(level, [])\n        }\n        levelTreeNodeMap.get(level)?.push(node)\n      }\n      if (level > maxLevel) {\n        maxLevel = level\n      }\n      return siblings\n    }\n    const treeNodes: TreeNode[] = traverse(data)\n    return {\n      treeNodeMap,\n      levelTreeNodeMap,\n      maxLevel,\n      treeNodes,\n    }\n  }\n\n  function filter(query: string) {\n    const keys = doFilter(query)\n    if (keys) {\n      expandedKeySet.value = keys\n    }\n  }\n\n  function getChildren(node: TreeNodeData): TreeNodeData[] {\n    return node[childrenKey.value]\n  }\n\n  function getKey(node: TreeNodeData): TreeKey {\n    if (!node) {\n      return ''\n    }\n    return node[valueKey.value]\n  }\n\n  function getDisabled(node: TreeNodeData): boolean {\n    return node[disabledKey.value]\n  }\n\n  function getLabel(node: TreeNodeData): string {\n    return node[labelKey.value]\n  }\n\n  function toggleExpand(node: TreeNode) {\n    const expandedKeys = expandedKeySet.value\n    if (expandedKeys.has(node.key)) {\n      collapseNode(node)\n    } else {\n      expandNode(node)\n    }\n  }\n\n  function setExpandedKeys(keys: TreeKey[]) {\n    const expandedKeys = new Set<TreeKey>()\n    const nodeMap = tree.value!.treeNodeMap\n\n    keys.forEach((k) => {\n      let node = nodeMap.get(k)\n      while (node && !expandedKeys.has(node.key)) {\n        expandedKeys.add(node.key)\n        node = node.parent\n      }\n    })\n\n    expandedKeySet.value = expandedKeys\n  }\n\n  function handleNodeClick(node: TreeNode, e: MouseEvent) {\n    emit(NODE_CLICK, node.data, node, e)\n    handleCurrentChange(node)\n    if (props.expandOnClickNode) {\n      toggleExpand(node)\n    }\n    if (\n      props.showCheckbox &&\n      (props.checkOnClickNode || (node.isLeaf && props.checkOnClickLeaf)) &&\n      !node.disabled\n    ) {\n      toggleCheckbox(node, !isChecked(node), true)\n    }\n  }\n\n  function handleNodeDrop(node: TreeNode, e: DragEvent) {\n    emit(NODE_DROP, node.data, node, e)\n  }\n\n  function handleCurrentChange(node: TreeNode) {\n    if (!isCurrent(node)) {\n      currentKey.value = node.key\n      emit(CURRENT_CHANGE, node.data, node)\n    }\n  }\n\n  function handleNodeCheck(node: TreeNode, checked: CheckboxValueType) {\n    toggleCheckbox(node, checked)\n  }\n\n  function expandNode(node: TreeNode) {\n    const keySet = expandedKeySet.value\n    if (tree.value && props.accordion) {\n      // whether only one node among the same level can be expanded at one time\n      const { treeNodeMap } = tree.value\n      keySet.forEach((key) => {\n        const treeNode = treeNodeMap.get(key)\n        if (node && node.level === treeNode?.level) {\n          keySet.delete(key)\n        }\n      })\n    }\n    keySet.add(node.key)\n    emit(NODE_EXPAND, node.data, node)\n  }\n\n  function collapseNode(node: TreeNode) {\n    expandedKeySet.value.delete(node.key)\n    emit(NODE_COLLAPSE, node.data, node)\n  }\n\n  function isExpanded(node: TreeNode): boolean {\n    return expandedKeySet.value.has(node.key)\n  }\n\n  function isDisabled(node: TreeNode): boolean {\n    return !!node.disabled\n  }\n\n  function isCurrent(node: TreeNode): boolean {\n    const current = currentKey.value\n    return current !== undefined && current === node.key\n  }\n\n  function getCurrentNode(): TreeNodeData | undefined {\n    if (!currentKey.value) return undefined\n    return tree.value?.treeNodeMap.get(currentKey.value)?.data\n  }\n\n  function getCurrentKey(): TreeKey | undefined {\n    return currentKey.value\n  }\n\n  function setCurrentKey(key: TreeKey): void {\n    currentKey.value = key\n  }\n\n  function setData(data: TreeData) {\n    tree.value = createTree(data)\n  }\n\n  function getNode(data: TreeKey | TreeNodeData) {\n    const key = isObject(data) ? getKey(data) : data\n    return tree.value?.treeNodeMap.get(key)\n  }\n\n  function scrollToNode(key: TreeKey, strategy: ScrollStrategy = 'auto') {\n    const node = getNode(key)\n    if (node && listRef.value) {\n      listRef.value.scrollToItem(flattenTree.value.indexOf(node), strategy)\n    }\n  }\n\n  function scrollTo(offset: number) {\n    listRef.value?.scrollTo(offset)\n  }\n\n  watch(\n    () => props.currentNodeKey,\n    (key) => {\n      currentKey.value = key\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  watch(\n    () => props.data,\n    (data: TreeData) => {\n      setData(data)\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  return {\n    tree,\n    flattenTree,\n    isNotEmpty,\n    listRef,\n    getKey,\n    getChildren,\n    toggleExpand,\n    toggleCheckbox,\n    isExpanded,\n    isChecked,\n    isIndeterminate,\n    isDisabled,\n    isCurrent,\n    isForceHiddenExpandIcon,\n    handleNodeClick,\n    handleNodeDrop,\n    handleNodeCheck,\n    // expose\n    getCurrentNode,\n    getCurrentKey,\n    setCurrentKey,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n    filter,\n    setData,\n    getNode,\n    expandNode,\n    collapseNode,\n    setExpandedKeys,\n    scrollToNode,\n    scrollTo,\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAYO,SAAS,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE;AACrC,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;AACjE,EAAE,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,IAAI,GAAG,UAAU,EAAE,CAAC;AAC5B,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM;AACR,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,GAAG,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5B,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,GAAG,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACzF,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,eAAe,CAAC,GAAG,CAAC;AACnF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,CAAC;AACrF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,YAAY,GAAG,EAAE,CAAC;AAC5B,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,EAAE,CAAC;AAC5E,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;AACrB,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAChD,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,MAAM,EAAE;AACzB,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAC/B,MAAM,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;AAClC,QAAQ,SAAS;AACjB,MAAM,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACvD,QAAQ,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC5D,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,WAAW,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAClD,IAAI,MAAM,gBAAgB,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACvD,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAC;AACrB,IAAI,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,EAAE;AACzD,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC1B,MAAM,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;AACnC,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AACtC,QAAQ,MAAM,IAAI,GAAG;AACrB,UAAU,KAAK;AACf,UAAU,GAAG,EAAE,KAAK;AACpB,UAAU,IAAI,EAAE,OAAO;AACvB,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC7B,QAAQ,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9C,QAAQ,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;AACzD,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;AACzC,UAAU,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9D,SAAS;AACT,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,QAAQ,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1C,UAAU,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC1C,SAAS;AACT,QAAQ,CAAC,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5E,OAAO;AACP,MAAM,IAAI,KAAK,GAAG,QAAQ,EAAE;AAC5B,QAAQ,QAAQ,GAAG,KAAK,CAAC;AACzB,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,OAAO;AACX,MAAM,WAAW;AACjB,MAAM,gBAAgB;AACtB,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,MAAM,CAAC,KAAK,EAAE;AACzB,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;AAClC,KAAK;AACL,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,SAAS,MAAM,CAAC,IAAI,EAAE;AACxB,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,IAAI,EAAE;AAC9B,IAAI,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC;AAC9C,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACpC,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC;AACvB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,IAAI,EAAE;AACjC,IAAI,MAAM,YAAY,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACnD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AACxB,MAAM,IAAI,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAClD,QAAQ,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,QAAQ,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC;AACxC,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE;AACpC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACzC,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,KAAK,CAAC,iBAAiB,EAAE;AACjC,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnH,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,KAAK;AACL,GAAG;AACH,EAAE,SAAS,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE;AACnC,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,SAAS,mBAAmB,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;AAC1B,MAAM,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AAClC,MAAM,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;AAC1C,IAAI,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC;AACxC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE;AACvC,MAAM,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACzC,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC9B,QAAQ,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,MAAM,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE;AACjF,UAAU,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,IAAI,EAAE;AAC9B,IAAI,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC3B,GAAG;AACH,EAAE,SAAS,SAAS,CAAC,IAAI,EAAE;AAC3B,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC;AACrC,IAAI,OAAO,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC,GAAG,CAAC;AACtD,GAAG;AACH,EAAE,SAAS,cAAc,GAAG;AAC5B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK;AACzB,MAAM,OAAO,KAAK,CAAC,CAAC;AACpB,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;AACvH,GAAG;AACH,EAAE,SAAS,aAAa,GAAG;AAC3B,IAAI,OAAO,UAAU,CAAC,KAAK,CAAC;AAC5B,GAAG;AACH,EAAE,SAAS,aAAa,CAAC,GAAG,EAAE;AAC9B,IAAI,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,GAAG;AACH,EAAE,SAAS,OAAO,CAAC,IAAI,EAAE;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,SAAS,OAAO,CAAC,IAAI,EAAE;AACzB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACrD,IAAI,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxE,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE;AAChD,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AAC/B,MAAM,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5E,KAAK;AACL,GAAG;AACH,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC5B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK;AAC7C,IAAI,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK;AACpC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;AAClB,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,uBAAuB;AAC3B,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,GAAG,CAAC;AACJ;;;;"}