{"version": 3, "file": "carousel-item.mjs", "sources": ["../../../../../../packages/components/carousel/src/carousel-item.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const carouselItemProps = buildProps({\n  /**\n   * @description name of the item, can be used in `setActiveItem`\n   */\n  name: { type: String, default: '' },\n  /**\n   * @description text content for the corresponding indicator\n   */\n  label: {\n    type: [String, Number],\n    default: '',\n  },\n} as const)\n\nexport type CarouselItemProps = ExtractPropTypes<typeof carouselItemProps>\n"], "names": [], "mappings": ";;AACY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE;AACrC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}