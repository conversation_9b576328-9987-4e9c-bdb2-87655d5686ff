{"version": 3, "file": "use-carousel-item.mjs", "sources": ["../../../../../../packages/components/carousel/src/use-carousel-item.ts"], "sourcesContent": ["import {\n  getCurrentInstance,\n  inject,\n  onMounted,\n  onUnmounted,\n  reactive,\n  ref,\n  unref,\n} from 'vue'\nimport { debugWarn, isUndefined } from '@element-plus/utils'\nimport { CAROUSEL_ITEM_NAME, carouselContextKey } from './constants'\n\nimport type { CarouselItemProps } from './carousel-item'\n\nexport const useCarouselItem = (props: CarouselItemProps) => {\n  const carouselContext = inject(carouselContextKey)!\n  // instance\n  const instance = getCurrentInstance()!\n  if (!carouselContext) {\n    debugWarn(\n      CAROUSEL_ITEM_NAME,\n      'usage: <el-carousel></el-carousel-item></el-carousel>'\n    )\n  }\n\n  if (!instance) {\n    debugWarn(\n      CAROUSEL_ITEM_NAME,\n      'compositional hook can only be invoked inside setups'\n    )\n  }\n\n  const carouselItemRef = ref<HTMLElement>()\n  const hover = ref(false)\n  const translate = ref(0)\n  const scale = ref(1)\n  const active = ref(false)\n  const ready = ref(false)\n  const inStage = ref(false)\n  const animating = ref(false)\n\n  // computed\n  const { isCardType, isVertical, cardScale } = carouselContext\n\n  // methods\n\n  function processIndex(index: number, activeIndex: number, length: number) {\n    const lastItemIndex = length - 1\n    const prevItemIndex = activeIndex - 1\n    const nextItemIndex = activeIndex + 1\n    const halfItemIndex = length / 2\n\n    if (activeIndex === 0 && index === lastItemIndex) {\n      return -1\n    } else if (activeIndex === lastItemIndex && index === 0) {\n      return length\n    } else if (index < prevItemIndex && activeIndex - index >= halfItemIndex) {\n      return length + 1\n    } else if (index > nextItemIndex && index - activeIndex >= halfItemIndex) {\n      return -2\n    }\n    return index\n  }\n\n  function calcCardTranslate(index: number, activeIndex: number) {\n    const parentWidth = unref(isVertical)\n      ? carouselContext.root.value?.offsetHeight || 0\n      : carouselContext.root.value?.offsetWidth || 0\n\n    if (inStage.value) {\n      return (parentWidth * ((2 - cardScale) * (index - activeIndex) + 1)) / 4\n    } else if (index < activeIndex) {\n      return (-(1 + cardScale) * parentWidth) / 4\n    } else {\n      return ((3 + cardScale) * parentWidth) / 4\n    }\n  }\n\n  function calcTranslate(\n    index: number,\n    activeIndex: number,\n    isVertical: boolean\n  ) {\n    const rootEl = carouselContext.root.value\n    if (!rootEl) return 0\n\n    const distance =\n      (isVertical ? rootEl.offsetHeight : rootEl.offsetWidth) || 0\n    return distance * (index - activeIndex)\n  }\n\n  const translateItem = (\n    index: number,\n    activeIndex: number,\n    oldIndex?: number\n  ) => {\n    const _isCardType = unref(isCardType)\n    const carouselItemLength = carouselContext.items.value.length ?? Number.NaN\n\n    const isActive = index === activeIndex\n    if (!_isCardType && !isUndefined(oldIndex)) {\n      animating.value = isActive || index === oldIndex\n    }\n\n    if (!isActive && carouselItemLength > 2 && carouselContext.loop) {\n      index = processIndex(index, activeIndex, carouselItemLength)\n    }\n\n    const _isVertical = unref(isVertical)\n    active.value = isActive\n\n    if (_isCardType) {\n      inStage.value = Math.round(Math.abs(index - activeIndex)) <= 1\n      translate.value = calcCardTranslate(index, activeIndex)\n      scale.value = unref(active) ? 1 : cardScale\n    } else {\n      translate.value = calcTranslate(index, activeIndex, _isVertical)\n    }\n\n    ready.value = true\n\n    if (isActive && carouselItemRef.value) {\n      carouselContext.setContainerHeight(carouselItemRef.value.offsetHeight)\n    }\n  }\n\n  function handleItemClick() {\n    if (carouselContext && unref(isCardType)) {\n      const index = carouselContext.items.value.findIndex(\n        ({ uid }) => uid === instance.uid\n      )\n      carouselContext.setActiveItem(index)\n    }\n  }\n\n  // lifecycle\n  onMounted(() => {\n    carouselContext.addItem({\n      props,\n      states: reactive({\n        hover,\n        translate,\n        scale,\n        active,\n        ready,\n        inStage,\n        animating,\n      }),\n      uid: instance.uid,\n      translateItem,\n    })\n  })\n\n  onUnmounted(() => {\n    carouselContext.removeItem(instance.uid)\n  })\n\n  return {\n    carouselItemRef,\n    active,\n    animating,\n    hover,\n    inStage,\n    isVertical,\n    translate,\n    isCardType,\n    scale,\n    ready,\n    handleItemClick,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAWY,MAAC,eAAe,GAAG,CAAC,KAAK,KAAK;AAC1C,EAAE,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;AACrD,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,IAAI,CAAC,eAAe,EAAE;AACxB,IAAI,SAAS,CAAC,kBAAkB,EAAE,uDAAuD,CAAC,CAAC;AAC3F,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,SAAS,CAAC,kBAAkB,EAAE,sDAAsD,CAAC,CAAC;AAC1F,GAAG;AACH,EAAE,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC;AAChC,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,eAAe,CAAC;AAChE,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE;AACpD,IAAI,MAAM,aAAa,GAAG,MAAM,GAAG,CAAC,CAAC;AACrC,IAAI,MAAM,aAAa,GAAG,WAAW,GAAG,CAAC,CAAC;AAC1C,IAAI,MAAM,aAAa,GAAG,WAAW,GAAG,CAAC,CAAC;AAC1C,IAAI,MAAM,aAAa,GAAG,MAAM,GAAG,CAAC,CAAC;AACrC,IAAI,IAAI,WAAW,KAAK,CAAC,IAAI,KAAK,KAAK,aAAa,EAAE;AACtD,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,KAAK,MAAM,IAAI,WAAW,KAAK,aAAa,IAAI,KAAK,KAAK,CAAC,EAAE;AAC7D,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK,MAAM,IAAI,KAAK,GAAG,aAAa,IAAI,WAAW,GAAG,KAAK,IAAI,aAAa,EAAE;AAC9E,MAAM,OAAO,MAAM,GAAG,CAAC,CAAC;AACxB,KAAK,MAAM,IAAI,KAAK,GAAG,aAAa,IAAI,KAAK,GAAG,WAAW,IAAI,aAAa,EAAE;AAC9E,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,iBAAiB,CAAC,KAAK,EAAE,WAAW,EAAE;AACjD,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,KAAK,CAAC,CAAC;AACrM,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AACvB,MAAM,OAAO,WAAW,IAAI,CAAC,CAAC,GAAG,SAAS,KAAK,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7E,KAAK,MAAM,IAAI,KAAK,GAAG,WAAW,EAAE;AACpC,MAAM,OAAO,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;AAChD,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,CAAC,GAAG,SAAS,IAAI,WAAW,GAAG,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG;AACH,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE;AAC1D,IAAI,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,QAAQ,GAAG,CAAC,WAAW,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,KAAK,CAAC,CAAC;AACnF,IAAI,OAAO,QAAQ,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,KAAK;AAC1D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAI,MAAM,kBAAkB,GAAG,CAAC,EAAE,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC;AACnG,IAAI,MAAM,QAAQ,GAAG,KAAK,KAAK,WAAW,CAAC;AAC3C,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;AAChD,MAAM,SAAS,CAAC,KAAK,GAAG,QAAQ,IAAI,KAAK,KAAK,QAAQ,CAAC;AACvD,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,IAAI,kBAAkB,GAAG,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE;AACrE,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;AACnE,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAI,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC5B,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC;AACrE,MAAM,SAAS,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC9D,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;AAClD,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,QAAQ,IAAI,eAAe,CAAC,KAAK,EAAE;AAC3C,MAAM,eAAe,CAAC,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7E,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,IAAI,eAAe,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE;AAC9C,MAAM,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC7F,MAAM,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3C,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,eAAe,CAAC,OAAO,CAAC;AAC5B,MAAM,KAAK;AACX,MAAM,MAAM,EAAE,QAAQ,CAAC;AACvB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,OAAO;AACf,QAAQ,SAAS;AACjB,OAAO,CAAC;AACR,MAAM,GAAG,EAAE,QAAQ,CAAC,GAAG;AACvB,MAAM,aAAa;AACnB,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,eAAe;AACnB,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,KAAK;AACT,IAAI,KAAK;AACT,IAAI,eAAe;AACnB,GAAG,CAAC;AACJ;;;;"}