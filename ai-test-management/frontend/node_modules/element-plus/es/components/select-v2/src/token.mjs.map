{"version": 3, "file": "token.mjs", "sources": ["../../../../../../packages/components/select-v2/src/token.ts"], "sourcesContent": ["import type { IOptionV2Props, ISelectV2Props } from './defaults'\nimport type { InjectionKey, Ref } from 'vue'\nimport type { Option } from './select.types'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\nexport interface SelectV2Context {\n  props: ISelectV2Props\n  expanded: Ref<boolean>\n  tooltipRef: Ref<TooltipInstance | undefined>\n  onSelect: (option: Option) => void\n  onHover: (idx?: number) => void\n  onKeyboardNavigate: (direction: 'forward' | 'backward') => void\n  onKeyboardSelect: () => void\n}\n\nexport const selectV2InjectionKey: InjectionKey<SelectV2Context> = Symbol(\n  'ElSelectV2Injection'\n)\nexport type { ISelectV2Props, IOptionV2Props }\n"], "names": [], "mappings": "AAAY,MAAC,oBAAoB,GAAG,MAAM,CAAC,qBAAqB;;;;"}