{"version": 3, "file": "select.mjs", "sources": ["../../../../../../packages/components/select-v2/src/select.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"selectRef\"\n    v-click-outside:[popperRef]=\"handleClickOutside\"\n    :class=\"[nsSelect.b(), nsSelect.m(selectSize)]\"\n    @mouseenter=\"states.inputHovering = true\"\n    @mouseleave=\"states.inputHovering = false\"\n  >\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownMenuVisible\"\n      :teleported=\"teleported\"\n      :popper-class=\"[nsSelect.e('popper'), popperClass]\"\n      :gpu-acceleration=\"false\"\n      :stop-popper-mouse-event=\"false\"\n      :popper-options=\"popperOptions\"\n      :fallback-placements=\"fallbackPlacements\"\n      :effect=\"effect\"\n      :placement=\"placement\"\n      pure\n      :transition=\"`${nsSelect.namespace.value}-zoom-in-top`\"\n      trigger=\"click\"\n      :persistent=\"persistent\"\n      :append-to=\"appendTo\"\n      :show-arrow=\"showArrow\"\n      :offset=\"offset\"\n      @before-show=\"handleMenuEnter\"\n      @hide=\"states.isBeforeHide = false\"\n    >\n      <template #default>\n        <div\n          ref=\"wrapperRef\"\n          :class=\"[\n            nsSelect.e('wrapper'),\n            nsSelect.is('focused', isFocused),\n            nsSelect.is('hovering', states.inputHovering),\n            nsSelect.is('filterable', filterable),\n            nsSelect.is('disabled', selectDisabled),\n          ]\"\n          @click.prevent=\"toggleMenu\"\n        >\n          <div\n            v-if=\"$slots.prefix\"\n            ref=\"prefixRef\"\n            :class=\"nsSelect.e('prefix')\"\n          >\n            <slot name=\"prefix\" />\n          </div>\n          <div\n            ref=\"selectionRef\"\n            :class=\"[\n              nsSelect.e('selection'),\n              nsSelect.is(\n                'near',\n                multiple && !$slots.prefix && !!modelValue.length\n              ),\n            ]\"\n          >\n            <slot v-if=\"multiple\" name=\"tag\">\n              <div\n                v-for=\"item in showTagList\"\n                :key=\"getValueKey(getValue(item))\"\n                :class=\"nsSelect.e('selected-item')\"\n              >\n                <el-tag\n                  :closable=\"!selectDisabled && !getDisabled(item)\"\n                  :size=\"collapseTagSize\"\n                  :type=\"tagType\"\n                  :effect=\"tagEffect\"\n                  disable-transitions\n                  :style=\"tagStyle\"\n                  @close=\"deleteTag($event, item)\"\n                >\n                  <span :class=\"nsSelect.e('tags-text')\">\n                    <slot\n                      name=\"label\"\n                      :label=\"getLabel(item)\"\n                      :value=\"getValue(item)\"\n                    >\n                      {{ getLabel(item) }}\n                    </slot>\n                  </span>\n                </el-tag>\n              </div>\n\n              <el-tooltip\n                v-if=\"collapseTags && modelValue.length > maxCollapseTags\"\n                ref=\"tagTooltipRef\"\n                :disabled=\"dropdownMenuVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                :effect=\"effect\"\n                placement=\"bottom\"\n                :teleported=\"teleported\"\n              >\n                <template #default>\n                  <div\n                    ref=\"collapseItemRef\"\n                    :class=\"nsSelect.e('selected-item')\"\n                  >\n                    <el-tag\n                      :closable=\"false\"\n                      :size=\"collapseTagSize\"\n                      :type=\"tagType\"\n                      :effect=\"tagEffect\"\n                      :style=\"collapseTagStyle\"\n                      disable-transitions\n                    >\n                      <span :class=\"nsSelect.e('tags-text')\">\n                        + {{ modelValue.length - maxCollapseTags }}\n                      </span>\n                    </el-tag>\n                  </div>\n                </template>\n                <template #content>\n                  <div ref=\"tagMenuRef\" :class=\"nsSelect.e('selection')\">\n                    <div\n                      v-for=\"selected in collapseTagList\"\n                      :key=\"getValueKey(getValue(selected))\"\n                      :class=\"nsSelect.e('selected-item')\"\n                    >\n                      <el-tag\n                        class=\"in-tooltip\"\n                        :closable=\"!selectDisabled && !getDisabled(selected)\"\n                        :size=\"collapseTagSize\"\n                        :type=\"tagType\"\n                        :effect=\"tagEffect\"\n                        disable-transitions\n                        @close=\"deleteTag($event, selected)\"\n                      >\n                        <span :class=\"nsSelect.e('tags-text')\">\n                          <slot\n                            name=\"label\"\n                            :label=\"getLabel(selected)\"\n                            :value=\"getValue(selected)\"\n                          >\n                            {{ getLabel(selected) }}\n                          </slot>\n                        </span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </slot>\n            <div\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('input-wrapper'),\n                nsSelect.is('hidden', !filterable),\n              ]\"\n            >\n              <input\n                :id=\"inputId\"\n                ref=\"inputRef\"\n                v-model=\"states.inputValue\"\n                :style=\"inputStyle\"\n                :autocomplete=\"autocomplete\"\n                :tabindex=\"tabindex\"\n                aria-autocomplete=\"list\"\n                aria-haspopup=\"listbox\"\n                autocapitalize=\"off\"\n                :aria-expanded=\"expanded\"\n                :aria-label=\"ariaLabel\"\n                :class=\"[nsSelect.e('input'), nsSelect.is(selectSize)]\"\n                :disabled=\"selectDisabled\"\n                role=\"combobox\"\n                :readonly=\"!filterable\"\n                spellcheck=\"false\"\n                type=\"text\"\n                :name=\"name\"\n                @input=\"onInput\"\n                @compositionstart=\"handleCompositionStart\"\n                @compositionupdate=\"handleCompositionUpdate\"\n                @compositionend=\"handleCompositionEnd\"\n                @keydown.up.stop.prevent=\"onKeyboardNavigate('backward')\"\n                @keydown.down.stop.prevent=\"onKeyboardNavigate('forward')\"\n                @keydown.enter.stop.prevent=\"onKeyboardSelect\"\n                @keydown.esc.stop.prevent=\"handleEsc\"\n                @keydown.delete.stop=\"handleDel\"\n                @click.stop=\"toggleMenu\"\n              />\n              <span\n                v-if=\"filterable\"\n                ref=\"calculatorRef\"\n                aria-hidden=\"true\"\n                :class=\"nsSelect.e('input-calculator')\"\n                v-text=\"states.inputValue\"\n              />\n            </div>\n            <div\n              v-if=\"shouldShowPlaceholder\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('placeholder'),\n                nsSelect.is(\n                  'transparent',\n                  !hasModelValue || (expanded && !states.inputValue)\n                ),\n              ]\"\n            >\n              <slot\n                v-if=\"hasModelValue\"\n                name=\"label\"\n                :label=\"currentPlaceholder\"\n                :value=\"modelValue\"\n              >\n                <span>{{ currentPlaceholder }}</span>\n              </slot>\n              <span v-else>{{ currentPlaceholder }}</span>\n            </div>\n          </div>\n          <div ref=\"suffixRef\" :class=\"nsSelect.e('suffix')\">\n            <el-icon\n              v-if=\"iconComponent\"\n              v-show=\"!showClearBtn\"\n              :class=\"[nsSelect.e('caret'), nsInput.e('icon'), iconReverse]\"\n            >\n              <component :is=\"iconComponent\" />\n            </el-icon>\n            <el-icon\n              v-if=\"showClearBtn && clearIcon\"\n              :class=\"[\n                nsSelect.e('caret'),\n                nsInput.e('icon'),\n                nsSelect.e('clear'),\n              ]\"\n              @click.prevent.stop=\"handleClear\"\n            >\n              <component :is=\"clearIcon\" />\n            </el-icon>\n            <el-icon\n              v-if=\"validateState && validateIcon && needStatusIcon\"\n              :class=\"[\n                nsInput.e('icon'),\n                nsInput.e('validateIcon'),\n                nsInput.is('loading', validateState === 'validating'),\n              ]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </div>\n        </div>\n      </template>\n      <template #content>\n        <el-select-menu\n          ref=\"menuRef\"\n          :data=\"filteredOptions\"\n          :width=\"popperSize\"\n          :hovering-index=\"states.hoveringIndex\"\n          :scrollbar-always-on=\"scrollbarAlwaysOn\"\n        >\n          <template v-if=\"$slots.header\" #header>\n            <div :class=\"nsSelect.be('dropdown', 'header')\">\n              <slot name=\"header\" />\n            </div>\n          </template>\n          <template #default=\"scope\">\n            <slot v-bind=\"scope\" />\n          </template>\n          <template v-if=\"$slots.loading && loading\" #loading>\n            <div :class=\"nsSelect.be('dropdown', 'loading')\">\n              <slot name=\"loading\" />\n            </div>\n          </template>\n          <template v-else-if=\"loading || filteredOptions.length === 0\" #empty>\n            <div :class=\"nsSelect.be('dropdown', 'empty')\">\n              <slot name=\"empty\">\n                <span>{{ emptyText }}</span>\n              </slot>\n            </div>\n          </template>\n          <template v-if=\"$slots.footer\" #footer>\n            <div :class=\"nsSelect.be('dropdown', 'footer')\">\n              <slot name=\"footer\" />\n            </div>\n          </template>\n        </el-select-menu>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, provide, reactive, toRefs } from 'vue'\nimport { isArray } from '@element-plus/utils'\nimport { ClickOutside } from '@element-plus/directives'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { useCalcInputWidth } from '@element-plus/hooks'\nimport ElSelectMenu from './select-dropdown'\nimport useSelect from './useSelect'\nimport { SelectProps, selectEmits } from './defaults'\nimport { selectV2InjectionKey } from './token'\n\nexport default defineComponent({\n  name: 'ElSelectV2',\n  components: {\n    ElSelectMenu,\n    ElTag,\n    ElTooltip,\n    ElIcon,\n  },\n  directives: { ClickOutside },\n  props: SelectProps,\n  emits: selectEmits,\n  setup(props, { emit }) {\n    const modelValue = computed(() => {\n      const { modelValue: rawModelValue, multiple } = props\n      const fallback = multiple ? [] : undefined\n      // When it is array, we check if this is multi-select.\n      // Based on the result we get\n      if (isArray(rawModelValue)) {\n        return multiple ? rawModelValue : fallback\n      }\n      return multiple ? fallback : rawModelValue\n    })\n\n    const API = useSelect(\n      reactive({\n        ...toRefs(props),\n        modelValue,\n      }),\n      emit\n    )\n    const { calculatorRef, inputStyle } = useCalcInputWidth()\n\n    provide(selectV2InjectionKey, {\n      props: reactive({\n        ...toRefs(props),\n        height: API.popupHeight,\n        modelValue,\n      }),\n      expanded: API.expanded,\n      tooltipRef: API.tooltipRef,\n      onSelect: API.onSelect,\n      onHover: API.onHover,\n      onKeyboardNavigate: API.onKeyboardNavigate,\n      onKeyboardSelect: API.onKeyboardSelect,\n    })\n\n    const selectedLabel = computed(() => {\n      if (!props.multiple) {\n        return API.states.selectedLabel\n      }\n      return API.states.cachedOptions.map((i) => i.label as string)\n    })\n\n    return {\n      ...API,\n      modelValue,\n      selectedLabel,\n      calculatorRef,\n      inputStyle,\n    }\n  },\n})\n</script>\n"], "names": ["_resolveComponent", "_createVNode", "_withCtx", "_createElementVNode", "_withModifiers", "_openBlock", "_createElementBlock", "_normalizeClass", "_renderSlot", "_createCommentVNode", "_createTextVNode", "_toDisplayString", "_createBlock", "_normalizeStyle", "_with<PERSON><PERSON><PERSON>", "_resolveDynamicComponent", "_createSlots", "_normalizeProps", "_guardReactiveProps"], "mappings": ";;;;;;;;;;;;;AAuSA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,YAAA;AAAA,EACN,UAAY,EAAA;AAAA,IACV,YAAA;AAAA,IACA,KAAA;AAAA,IACA,SAAA;AAAA,IACA,MAAA;AAAA,GACF;AAAA,EACA,UAAA,EAAY,EAAE,YAAa,EAAA;AAAA,EAC3B,KAAO,EAAA,WAAA;AAAA,EACP,KAAO,EAAA,WAAA;AAAA,EACP,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,MAAM,EAAE,UAAA,EAAY,aAAe,EAAA,QAAA,EAAa,GAAA,KAAA,CAAA;AAChD,MAAM,MAAA,QAAA,GAAW,QAAW,GAAA,EAAK,GAAA,KAAA,CAAA,CAAA;AAGjC,MAAI,IAAA,OAAA,CAAQ,aAAa,CAAG,EAAA;AAC1B,QAAA,OAAO,WAAW,aAAgB,GAAA,QAAA,CAAA;AAAA,OACpC;AACA,MAAA,OAAO,WAAW,QAAW,GAAA,aAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAA,MAAM,GAAM,GAAA,SAAA,CAAA,QAAA,CAAA;AAAA,MACV,GAAS,MAAA,CAAA,KAAA,CAAA;AAAA,MACP;AAAe,KACf,CAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IAAA,MACD,EAAA,aAAA,EAAA,UAAA,EAAA,GAAA,iBAAA,EAAA,CAAA;AAAA,IACD,OAAA,CAAA,oBAAA,EAAA;AAAA,MACF,KAAA,EAAA,QAAA,CAAA;AACA,QAAA,GAAQ,MAAA,CAAA,KAAA,CAAA;AAER,QAAA,MAA8B,EAAA,GAAA,CAAA,WAAA;AAAA,kBACZ;AAAA,OACd,CAAA;AAAe,MAAA,UACP,GAAI,CAAA,QAAA;AAAA,MACZ,UAAA,EAAA,GAAA,CAAA,UAAA;AAAA,MACF,QAAC,EAAA,GAAA,CAAA,QAAA;AAAA,MACD,YAAc,CAAA,OAAA;AAAA,MACd,kBAAgB,EAAA,GAAA,CAAA,kBAAA;AAAA,MAChB,gBAAc,EAAA,GAAA,CAAA,gBAAA;AAAA,KAAA,CACd;AAAa,IAAA,sBACO,QAAI,CAAA,MAAA;AAAA,MACxB,mBAAsB,EAAA;AAAA,QACvB,OAAA,GAAA,CAAA,MAAA,CAAA,aAAA,CAAA;AAED,OAAM;AACJ,MAAI,WAAO,MAAU,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,CAAA;AACnB,KAAA,CAAA,CAAA;AAAkB,IACpB,OAAA;AACA,MAAA,GAAA,GAAA;AAA4D,MAC7D,UAAA;AAED,MAAO,aAAA;AAAA,MACL,aAAG;AAAA,MACH,UAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACF,SACF,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACF,EAAC,MAAA,iBAAA,GAAAA,gBAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;yCA7EO,CAAA,aAAA,GAAA,IAAA;AAAA,IArRJ,YAAI,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AAAA,GAAA;AAEwC,IAC3CC,WAAA,CAAA,qBAAY,EAAA;AAAoB,MAChC,GAAA,EAAA,YAAY;AAAoB,MAAA,OAAA,EAAA,IAAA,CAAA,mBAAA;MAgRpB,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,MA7QX,cAAI,EAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,MACH,kBAAS,EAAA,KAAA;AAAA,MACT,yBAAY,EAAA,KAAA;AAAA,MACZ,gBAAY,EAAY,IAAA,CAAA;AAAwB,MAChD,qBAAkB,EAAA,IAAA,CAAA,kBAAA;AAAA,MAClB,MAAyB,EAAA,IAAA,CAAA,MAAA;AAAA,MACzB,SAAgB,EAAA,IAAA,CAAA,SAAA;AAAA,MAChB,IAAqB,EAAA,EAAA;AAAA,MACrB,UAAQ,EAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,MACR,OAAW,EAAA,OAAA;AAAA,MACZ,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,MACC,WAAU,EAAA,IAAc,CAAA,QAAA;AAAe,MACxC,YAAQ,EAAA,IAAA,CAAA,SAAA;AAAA,MACP,MAAY,EAAA,IAAA,CAAA,MAAA;AAAA,MACZ,YAAW,EAAA,IAAA,CAAA,eAAA;AAAA,MACX,MAAY,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AAAA,KAAA,EACJ;AAAA,MACR,OAAa,EAAAC,OAAA,CAAA,MAAA;AAAA,QACbC,kBAAM,CAAA,KAAA,EAAA;AAAmB,UAAA,GAAA,EAAA,YAAA;AAEf,UAAA,KAAO,gBAoNV,CAAA;AAAA,YAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AAAA,YAlNA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,SAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,YACE,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,aAAA,CAAA;AAAA,YAAgB,cAAS,EAAC,CAAA,YAAA,EAAA,IAAA,CAAA,UAAA,CAAA;AAAA,YAAyB,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,UAAA,EAAqB,IAAA,CAAA,cAAA,CAAA;AAAA,WAAA,CAAe;AAA4C,UAAe,OAAA,EAAAC,aAAW,CAAA,IAAA,CAAA,UAAyB,EAAA,CAAA,SAAA,CAAA,CAAA;AAAA,SAAe,EAAA;AAAsC,UAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAO3P,YAAA,GAAA,EAAA,CAAA;AAAyB,YAAA,GAAA,EAAA,WAAA;AAGlB,YAAA,KAAA,EAAAC,cADR,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;AAAA,WAMM,EAAA;AAAA,YAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,WAAA,EAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;4BAJA,CAAA,KAAA,EAAA;AAAA,YACH,GAAA,EAAA,cAAO;AAAU,YAAA,KAAA,EAAAF,cAAA,CAAA;;cAElB,IAAsB,CAAA,QAAA,CAAA,EAAA,CAAA,MAAA,EAAA,IAAA,CAAA,QAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA;AAAA,aAAA,CAAA;;;;AAExB,gBAAA,OAAAF,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,kBAkKM,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAAA,kBAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,iBAjKA,EAAA;AAAA,kBACEN,WAAA,CAAA,iBAAA,EAAA;AAAA,8BAA2B,CAAC,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,oBAAsC,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,oBAAA,IAAA,EAAA,IAAA,CAAA,OAAA;AAA4C,oBAAA,MAAA,EAAA,IAAA,CAAA,SAAa;AAA8B,oBAAA,qBAAA,EAAA,EAAA;;;;oBAQ/J,OAAA,EAAAC,OAAA,CAAA;AAqFO,sBApFLC,kBAAA,CAAA,MAAA,EAAA;AAAA,wBAwBM,KAAA,EAAAI,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;AAAA,uBAAA,EAAA;AAAA,wBAvBWC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAR,OAAI,EAAA;AADb,0BAAA,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AAAA,0BAwBM,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AAAA,yBAAA,EAAA,MAAA;AAAA,0BAtBHE,eAAiB,CAAAC,eAAA,CAAS,IAAI,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,yBAC9B,CAAA;AAAiB,uBAAA,EAAA,CAAA,CAAA;;wBAElB;AAkBS,mBAAA,EAAA,IAAA,EAjBE,CAAA,UAAiB,EAAA,MAAA,EAAA,MAAA,EAAA,4BAAqB,CAAA,CAAA;AAAA,iBAAA,EAAA,CAAA,CAAA,CAAA;AACxC,eAAA,CAAA,EAAA,GAAA,CAAA;AACA,cAAA,IAAA,CAAA,YACE,IAAA,IAAA,CAAA,UAAA,CAAA,MAAA,GAAA,IAAA,CAAA,eAAA,IAAAN,SAAA,EAAA,EAAAO,WAAA,CAAA,qBAAA,EAAA;AAAA,gBACT,GAAA,EAAA,CAAA;AAAA,gBACC,GAAA,EAAA;AAAe,gBAAA,QAAA,EACf,IAAK,CAAA,mBAAY,IAAA,CAAA,IAAA,CAAA,mBAAY;AAAA,gBAAA,qBAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;;AAUvB,gBARP,SAAA,EAAA,QAAA;AAAA,gBAQO,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,eAAA,EAAA;AAAA,gBARA,OAAA,EAAAV,OAAA,CAAA,MAAO;AAAU,kBAAAC,kBAAA,CAAA,KAAA,EAAA;;yCAOf,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,mBAJJ,EAAA;AAAoB,oBACpBF,WAAA,CAAA;AAAoB,sBAAA,QAAA,EAAA,KAGhB;AAAA,sBAAA,IAAA,EAAA,IAAA,CAAA,eAAA;AADF,sBAAA,IAAA,EAAA,IAAA,CAAA,OAAA;AAAa,sBAAA,MAAA,EAAA,IAAA,CAAA,SAAA;AAAA,sBAAA,KAAA,EAAAY,cAAA,CAAA,IAAA,CAAA,gBAAA,CAAA;AAAA,sBAAA,qBAAA,EAAA,EAAA;AAAA,qBAAA,EAAA;;;;;;;;;;;;;;;;;wBAOA,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,QAAoB,CAAA,QAAA,CAAA,CAAA;AAwD/B,wBAAA,KAAA,EAAAN,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;uBAvDP,EAAA;AAAA,wBACHN,+BAA6B;AAAK,0BACb,KAAA,EAAA,YAAA;AAAA,0BACb,QAAA,EAAA,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,QAAA,CAAA;AAAA,0BACC,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,0BACG,IAAA,EAAA,IAAA,CAAA,OAAA;AAAA,0BAAA,MAAA,EAAA,IAAA,CAAA,SAAA;AAEF,0BAAA,qBAiBH,EAAA,EAAA;AAAA,0BAhBN,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,yBAgBM,EAAA;AAAA,0BAAA,OAAA,EAAAC,OAAA,CAAA,MAAA;AAAA,4BAfAC,kBAAA,CAAA,MAAA,EAAA;AAAA,8BACH,KAAO,EAAAI,cAAA,CAAA,IAAA,CAAA,QAAU,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;AAAA,6BAAA,EAAA;;gCAaT,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,QAAA,CAAA;AAAA,gCAVI,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,QAAA,CAAA;AAAA,+BACJ,EAAA,MAAA;AAAA,gCACAG,eAAA,CAAAC,eAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,+BACE,CAAA;AAAA,6BACR,EAAA,CAAA,CAAK;AAAkB,2BACxB,CAAA;AAAA,0BAAA,CAAA,EAAA,CAAA;4CAEA,EAEO,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,CAAA,CAAA;AAAA,uBAFP,EAAA,CAAA,CAAA,CAAA;AAAA,qBAEO,CAAA,EAAA,GAAA,CAAA;AAAA,mBAAA,EAAA,CAAA,CAAA;AAAA,iBAFA,CAAA;AAAiB,gBAAA,CAAA,EAAA,CAAA;+BACpB,EAAA,QAAA,EAAA,YAAG,CAAW,CAAA,IAAAF,kBAAS,CAAe,MAAA,EAAA,IAAA,CAAA;AAAA,aAAA,CAAA,GAAAA,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,YAAAN,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,KAAA,EAAAI,cAAA,CAAA;AAAA,gBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA;;;;;;;;;AAKrC,gBAAA,KAAA,EAAAM,cA2BH,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,gBA1BN,YAAA,EAAA,IAAA,CAAA,YAAA;AAAA,gBA0BM,QAAA,EAAA,IAAA,CAAA,QAAA;AAAA,gBAAA,mBAAA,EAAA,MAAA;AAAA,gBAAA,eA1BG,EAAA,SAAA;AAAA,gBAAc,cAAA,EAAO,KAAA;AAAU,gBAAA,eAAA,EAAA,IAAA,CAAA,QAAA;;AACtC,gBAAA,KAAA,EAAAN,cAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,gBAwBM,QAAA,EAAA,IAAA,CAAA,cAAA;AAAA,gBAAA,IAAA,EAAA,UAAA;AAAA,gBAvBe,QAAA,EAAA,CAAA,IAAA,CAAA,UAAA;AADrB,gBAAA,UAAA,EAAA,OAAA;AAAA,gBAwBM,IAAA,EAAA,MAAA;AAAA,gBAAA,IAAA,EAAA,IAAA,CAAA,IAAA;AAAA,gBAtBH,OAAA,EAAA,IAAA,CAAA,OAAK;AAA6B,gBAClC,kBAAA,EAAA,IAAO,CAAA,sBAAA;AAAU,gBAAA,mBAAA,EAAA,IAAA,CAAA,uBAAA;;;AAoBT,kBAAAO,QAAA,CAAAV,aAjBD,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA;AAAA,kBAAAU,QAAA,CAAAV,aACG,CAAA,CAAA,MAAiB,KAAA,IAAA,CAAA,4BAAyB,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,kBAAAU,QAAA,CAAAV,aAC5C,CAAA,IAAA,CAAA,gBAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAAA,kBAAAU,QAAA,CAAAV,aACA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,kBAAAU,QAAA,CAAAV,aACE,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA;AAAA,iBACT;AAAA,gBAAA,OAAA,EAAAA,aACM,CAAA,IAAA,CAAA,UAAE,EAAU,CAAA,MAAA,CAAA,CAAA;AAAgB,eAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,IAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,YAAA,EAAA,UAAA,EAAA,UAAA,EAAA,MAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,SAAA,CAAA,CAAA,EAAA;mDAElC,CAQO;AAAA,eARP,CAAA;AAAA,cAQO,IAAA,CAAA,UAAA,IAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,MAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;AAAA,gBARA,GAAA,EAAA,eAAA;AAAiB,gBAAA,aAAA,EAAA,MAAA;;4CAOf,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,eAJJ,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,aAAA,CAAA,CAAA,IAAAG,kBAAwB,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aACxB,EAAA,CAAA,CAAA;AAAwB,YAAA,IAAA,CAAA,qBAAA,IAAAJ,SAGpB,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AADF,cAAA,KAAA,EAAAC,cAAA,CAAA;AAAiB,gBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA;AAAA,gBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,aAAA,CAAA;AAAA,gBAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,aAAA,EAAA,CAAA,IAAA,CAAA,aAAA,IAAA,IAAA,CAAA,QAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC,YAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,SAAA,IAAAF,SAAA,EAAA,EAAAO,WAAA,CAAA,kBAAA,EAAA;AAAA,cA4CM,GAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAL,cAAA,CAAA;AAAA,gBAAA,IA3CE,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA;AAAA,gBAAA,IAAoB,iBAAU,CAAA;AAAA,gBAAA,IAAmC,mBAAU,CAAA;AAAA,eAAmC,CAAA;AAAiC,cAAA,OAAA,EAAAH,aAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,SAAA,EAAA,MAAA,CAAA,CAAA;;;yCAmCnJ,CAAAW,uBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AAAA,eAAA,CAAA;AA5BK,cAAA,CAAA,EAAA,CAAA;AACD,aAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,CAAA,IAAAN,yBACsB,EAAA,IAAA,CAAA;AAAA,YACzB,IAAA,CAAA,kCAAiB,IAAA,IAAA,CAAA,cAAA,IAAAJ,SAAA,EAAA,EAAAO,WAAA,CAAA,kBAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CACjB;AAAc,cAAA,KAAA,EACJL,cAAA,CAAA;AAAA,gBAAA,IACX,CAAkB,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,gBAAA,IAClB,CAAc,OAAA,CAAA,CAAA,CAAA,cAAA,CAAA;AAAA,gBAAA,IACd,CAAe,OAAA,CAAA,EAAA,CAAA,SAAA,EAAA,IAAA,CAAA,aAAA,KAAA,YAAA,CAAA;AAAA,eAAA,CAAA;AACC,aAAA,EAAA;AACH,cAAA,gBACJ,CAAA,MAAA;AAA2C,iBAAAF,SACzC,EAAA,EAAAO,WAAA,CAAAG,uBAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AAAA,eAAA,CAAA;AACN,cAAA,CAAA,EAAA,CAAA;AACO,aAAA,EAAA,CAAA,EAAA,CAAA,OACD,CAAA,CAAA,IAAAN,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,WAAA,EAAA,CAAA,CAAA;AACN,SAAA,EAAA,EAAA,EAAA,CAAA,SACE,CAAA,CAAA;AAAA,OAAA,CAAA;AACC,MAAA,OAAA,EAAAP,OACW,CAAA,MAAA;AAAA,QAAAD,WAClB,CAAmB,yBAAA,EAAA;AAAA,UAAA,GAAA,EAAA,SACH;AAAA,UAAA,IAAA,EAAA,IAChB,CAAO,eAAA;AAAA,UAAA,KAAA,EAAA,IAAA,CAAA,UAAA;AAAoC,UAAA,gBAAA,EAAA,IAAA,CAAA,MAAA,CAAA,aAAA;AACE,UAAA,qBAAA,EAAA,IAAA,CAAA,iBACD;AAAA,SAAA,EAAAe,WAAA,CAAA;AACT,UAAA,OAAA,EAAAd,OAAA,CAAA,CAAA,KAAA,KAAA;AACL,YAAAM,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAAS,cAAA,CAAAC,kBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAC9B,WAAA,CAAA;AAAsB,UAAA,CAAA,EAAA,CAAA;AAzBd,SAAA,EAAA;AAAiB,UAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA;AA4BpB,YAAA,IAAA,EAAA,QAAA;AAKN,YAAA,EAAA,EAAAhB,OAAA,CAAA,MAAA;gCAJI,CAAA,KAAA,EAAA;AAAA,gBAAA,KACQ,EAAAK,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,eACX,EAAA;AAAiB,gBAClBC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAQ;AAAiB,eAAA,EAAA,CAAA,CAAA;;;;;cAIrB,EADRN,OAAA,CAAA,MAAA;AAAA,cAoBMC,kBAAA,CAAA,KAAA,EAAA;AAAA,gBAAA,KAAA,EAAAI,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA;AAAA,eAAA,EAAA;0BAlBE,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,eAAA,EAAA,CAAA,CAAA;AAA8B,aAAA,CAAA;AAA6C,WAAA,GAAA,IAAA,CAAA,OAA0C,IAAA,IAAA,CAAA,eAAA,CAAA,MAAA,KAAA,CAAA,GAAA;AAAA,YAAA,IAAA,EAAA,OAAA;uBAA0E,CAAA,MAAA;AAAoB,cAAAJ,kBAAA,CAAA,KAAA,EAAA;;;;AAUjN,kBAAAA,kBAAA,CAAA,MAMD,EAAA,IAAA,EAAAQ,eAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;;AAJG,aAAA,CAAA;AACA,WAAA,GAAA,KAAA,CAAA;AAGH,UADL,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA;AAAA,YAAqC,IAAA,EAAA,QAAA;AAAA,YAAA,EAAA,EAAAT,OAAA,CAAA,MAAA;AAAA,cAAAC,kBAAA,CAAA,KAA5B,EAAkB;AAAA,gBAAA,KAAA,EAAAI,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,eAAA,EAAA;AAAA,gBAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,eAE7B,EAAA,CAAA,CAAA;AAAA,aAA4C,CAAA;AAAA,WAAA,GAAA,KAAA,CAAA;AAAA,SAAA,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,gBAAV,EAAA,qBAAA,CAAA,CAAA;AAAA,OAAA,CAAA;AAAA,MAAA,CAAA,EAAA,CAAA;AAAA,KAAA,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,WAAA,EAAA,YAAA,EAAA,YAAA,EAAA,WAAA,EAAA,YAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,CAAA,CAAA;AAAA,GAAA,EAAA,EAAA,EAAA,CAAA,cAAA,EAAA,cAAA,CAAA,CAAA,GAAA;;;;;;;;"}