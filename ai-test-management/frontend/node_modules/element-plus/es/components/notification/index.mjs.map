{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/notification/index.ts"], "sourcesContent": ["import { withInstallFunction } from '@element-plus/utils'\n\nimport Notify from './src/notify'\n\nexport const ElNotification = withInstallFunction(Notify, '$notify')\nexport default ElNotification\n\nexport * from './src/notification'\n"], "names": ["Notify"], "mappings": ";;;;AAEY,MAAC,cAAc,GAAG,mBAAmB,CAACA,MAAM,EAAE,SAAS;;;;"}