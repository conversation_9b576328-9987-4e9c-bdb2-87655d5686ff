{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/date-picker/src/utils.ts"], "sourcesContent": ["import dayjs from 'dayjs'\nimport { isArray, isString } from '@element-plus/utils'\nimport { rangeArr } from '@element-plus/components/time-picker'\n\nimport type { ComputedRef } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { DateCell } from './date-picker.type'\nimport type { DisabledDateType } from './props/shared'\n\ntype DayRange = [Dayjs | undefined, Dayjs | undefined]\n\nexport const isValidRange = (range: DayRange): boolean => {\n  if (!isArray(range)) return false\n\n  const [left, right] = range\n\n  return (\n    dayjs.isDayjs(left) &&\n    dayjs.isDayjs(right) &&\n    dayjs(left).isValid() &&\n    dayjs(right).isValid() &&\n    left.isSameOrBefore(right)\n  )\n}\n\ntype GetDefaultValueParams = {\n  lang: string\n  unit: 'month' | 'year'\n  unlinkPanels: boolean\n}\n\nexport type DefaultValue = [Date, Date] | Date | undefined\n\nexport const getDefaultValue = (\n  defaultValue: DefaultValue,\n  { lang, unit, unlinkPanels }: GetDefaultValueParams\n) => {\n  let start: Dayjs\n\n  if (isArray(defaultValue)) {\n    let [left, right] = defaultValue.map((d) => dayjs(d).locale(lang))\n    if (!unlinkPanels) {\n      right = left.add(1, unit)\n    }\n    return [left, right]\n  } else if (defaultValue) {\n    start = dayjs(defaultValue)\n  } else {\n    start = dayjs()\n  }\n  start = start.locale(lang)\n  return [start, start.add(1, unit)]\n}\n\ntype Dimension = {\n  row: number\n  column: number\n}\n\ntype BuildPickerTableMetadata = {\n  startDate?: Dayjs | null\n  unit: 'month' | 'day'\n  columnIndexOffset: number\n  now: Dayjs\n  nextEndDate: Dayjs | null\n  relativeDateGetter: (index: number) => Dayjs\n  setCellMetadata?: (\n    cell: DateCell,\n    dimension: { rowIndex: number; columnIndex: number }\n  ) => void\n  setRowMetadata?: (row: DateCell[]) => void\n}\n\nexport const buildPickerTable = (\n  dimension: Dimension,\n  rows: DateCell[][],\n  {\n    columnIndexOffset,\n    startDate,\n    nextEndDate,\n    now,\n    unit,\n    relativeDateGetter,\n    setCellMetadata,\n    setRowMetadata,\n  }: BuildPickerTableMetadata\n) => {\n  for (let rowIndex = 0; rowIndex < dimension.row; rowIndex++) {\n    const row = rows[rowIndex]\n    for (let columnIndex = 0; columnIndex < dimension.column; columnIndex++) {\n      let cell = row[columnIndex + columnIndexOffset]\n      if (!cell) {\n        cell = {\n          row: rowIndex,\n          column: columnIndex,\n          type: 'normal',\n          inRange: false,\n          start: false,\n          end: false,\n        }\n      }\n      const index = rowIndex * dimension.column + columnIndex\n      const nextStartDate = relativeDateGetter(index)\n      cell.dayjs = nextStartDate\n      cell.date = nextStartDate.toDate()\n      cell.timestamp = nextStartDate.valueOf()\n      cell.type = 'normal'\n\n      cell.inRange =\n        !!(\n          startDate &&\n          nextStartDate.isSameOrAfter(startDate, unit) &&\n          nextEndDate &&\n          nextStartDate.isSameOrBefore(nextEndDate, unit)\n        ) ||\n        !!(\n          startDate &&\n          nextStartDate.isSameOrBefore(startDate, unit) &&\n          nextEndDate &&\n          nextStartDate.isSameOrAfter(nextEndDate, unit)\n        )\n\n      if (startDate?.isSameOrAfter(nextEndDate)) {\n        cell.start = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit)\n        cell.end = startDate && nextStartDate.isSame(startDate, unit)\n      } else {\n        cell.start = !!startDate && nextStartDate.isSame(startDate, unit)\n        cell.end = !!nextEndDate && nextStartDate.isSame(nextEndDate, unit)\n      }\n\n      const isToday = nextStartDate.isSame(now, unit)\n\n      if (isToday) {\n        cell.type = 'today'\n      }\n      setCellMetadata?.(cell, { rowIndex, columnIndex })\n      row[columnIndex + columnIndexOffset] = cell\n    }\n    setRowMetadata?.(row)\n  }\n}\n\nexport const datesInMonth = (\n  date: Dayjs,\n  year: number,\n  month: number,\n  lang: string\n) => {\n  const firstDay = dayjs(date).locale(lang).month(month).year(year)\n  const numOfDays = firstDay.daysInMonth()\n  return rangeArr(numOfDays).map((n) => firstDay.add(n, 'day').toDate())\n}\n\nexport const getValidDateOfMonth = (\n  date: Dayjs,\n  year: number,\n  month: number,\n  lang: string,\n  disabledDate?: DisabledDateType\n) => {\n  const _value = dayjs(date).year(year).month(month)\n  const _date = datesInMonth(date, year, month, lang).find((date) => {\n    return !disabledDate?.(date)\n  })\n  if (_date) {\n    return dayjs(_date).locale(lang)\n  }\n  return _value.locale(lang)\n}\n\nexport const getValidDateOfYear = (\n  value: Dayjs,\n  lang: string,\n  disabledDate?: DisabledDateType\n) => {\n  const year = value.year()\n  if (!disabledDate?.(value.toDate())) {\n    return value.locale(lang)\n  }\n  const month = value.month()\n  if (!datesInMonth(value, year, month, lang).every(disabledDate)) {\n    return getValidDateOfMonth(value, year, month, lang, disabledDate)\n  }\n  for (let i = 0; i < 12; i++) {\n    if (!datesInMonth(value, year, i, lang).every(disabledDate)) {\n      return getValidDateOfMonth(value, year, i, lang, disabledDate)\n    }\n  }\n  return value\n}\n\nexport const correctlyParseUserInput = (\n  value: string | Dayjs | Dayjs[],\n  format: string,\n  lang: string,\n  defaultFormat: ComputedRef<boolean>\n): Dayjs | Dayjs[] => {\n  if (isArray(value)) {\n    return value.map(\n      (v) => correctlyParseUserInput(v, format, lang, defaultFormat) as Dayjs\n    )\n  }\n  if (isString(value)) {\n    const dayjsValue = defaultFormat.value ? dayjs(value) : dayjs(value, format)\n    if (!dayjsValue.isValid()) {\n      // return directly if not valid\n      return dayjsValue\n    }\n  }\n  return dayjs(value, format).locale(lang)\n}\n"], "names": [], "mappings": ";;;;AAGY,MAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACvC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AACrB,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;AAC9B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACtI,EAAE;AACU,MAAC,eAAe,GAAG,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK;AAC/E,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE;AAC7B,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACvE,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACzB,GAAG,MAAM,IAAI,YAAY,EAAE;AAC3B,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAChC,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,KAAK,EAAE,CAAC;AACpB,GAAG;AACH,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACrC,EAAE;AACU,MAAC,gBAAgB,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE;AAClD,EAAE,iBAAiB;AACnB,EAAE,SAAS;AACX,EAAE,WAAW;AACb,EAAE,GAAG;AACL,EAAE,IAAI;AACN,EAAE,kBAAkB;AACpB,EAAE,eAAe;AACjB,EAAE,cAAc;AAChB,CAAC,KAAK;AACN,EAAE,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;AAC/D,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/B,IAAI,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;AAC7E,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,WAAW,GAAG,iBAAiB,CAAC,CAAC;AACtD,MAAM,IAAI,CAAC,IAAI,EAAE;AACjB,QAAQ,IAAI,GAAG;AACf,UAAU,GAAG,EAAE,QAAQ;AACvB,UAAU,MAAM,EAAE,WAAW;AAC7B,UAAU,IAAI,EAAE,QAAQ;AACxB,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,KAAK,EAAE,KAAK;AACtB,UAAU,GAAG,EAAE,KAAK;AACpB,SAAS,CAAC;AACV,OAAO;AACP,MAAM,MAAM,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;AAC9D,MAAM,MAAM,aAAa,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACtD,MAAM,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AACjC,MAAM,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;AACzC,MAAM,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,OAAO,EAAE,CAAC;AAC/C,MAAM,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AAC3B,MAAM,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,SAAS,IAAI,aAAa,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,WAAW,IAAI,aAAa,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,IAAI,aAAa,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,WAAW,IAAI,aAAa,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;AACxR,MAAM,IAAI,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE;AAC7E,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,WAAW,IAAI,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC9E,QAAQ,IAAI,CAAC,GAAG,GAAG,SAAS,IAAI,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACtE,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC1E,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,WAAW,IAAI,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC5E,OAAO;AACP,MAAM,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACtD,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;AAC5B,OAAO;AACP,MAAM,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC;AAC1F,MAAM,GAAG,CAAC,WAAW,GAAG,iBAAiB,CAAC,GAAG,IAAI,CAAC;AAClD,KAAK;AACL,IAAI,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AAC1D,GAAG;AACH,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,KAAK;AACzD,EAAE,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpE,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC3C,EAAE,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AACzE,EAAE;AACU,MAAC,mBAAmB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,KAAK;AAC9E,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrD,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK;AACtE,IAAI,OAAO,EAAE,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AAClE,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE;AACU,MAAC,kBAAkB,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,YAAY,KAAK;AACjE,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AAC5B,EAAE,IAAI,EAAE,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;AACvE,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC9B,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACnE,IAAI,OAAO,mBAAmB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AACvE,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AAC/B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACjE,MAAM,OAAO,mBAAmB,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AACrE,KAAK;AACL,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACU,MAAC,uBAAuB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,KAAK;AAC/E,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AACtB,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,uBAAuB,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;AACrF,GAAG;AACH,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACjF,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;AAC/B,MAAM,OAAO,UAAU,CAAC;AACxB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3C;;;;"}