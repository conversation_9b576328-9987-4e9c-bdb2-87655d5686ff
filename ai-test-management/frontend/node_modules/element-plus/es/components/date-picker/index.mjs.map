{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/date-picker/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport DatePicker from './src/date-picker'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDatePicker: SFCWithInstall<typeof DatePicker> =\n  withInstall(DatePicker)\n\nexport default ElDatePicker\nexport * from './src/constants'\nexport * from './src/props/date-picker'\nexport type { DatePickerInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU;;;;"}