{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/breadcrumb/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Breadcrumb from './src/breadcrumb.vue'\nimport BreadcrumbItem from './src/breadcrumb-item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElBreadcrumb: SFCWithInstall<typeof Breadcrumb> & {\n  BreadcrumbItem: typeof BreadcrumbItem\n} = withInstall(Breadcrumb, {\n  BreadcrumbItem,\n})\nexport const ElBreadcrumbItem: SFCWithInstall<typeof BreadcrumbItem> =\n  withNoopInstall(BreadcrumbItem)\nexport default ElBreadcrumb\n\nexport * from './src/breadcrumb'\nexport * from './src/breadcrumb-item'\nexport * from './src/constants'\nexport type {\n  BreadcrumbInstance,\n  BreadcrumbItemInstance,\n} from './src/instances'\n"], "names": [], "mappings": ";;;;;;;AAGY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU,EAAE;AACpD,EAAE,cAAc;AAChB,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG,eAAe,CAAC,cAAc;;;;"}