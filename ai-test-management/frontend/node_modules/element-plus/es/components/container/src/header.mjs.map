{"version": 3, "file": "header.mjs", "sources": ["../../../../../../packages/components/container/src/header.vue"], "sourcesContent": ["<template>\n  <header :class=\"ns.b()\" :style=\"style\">\n    <slot />\n  </header>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: '<PERSON>Header',\n})\n\nconst props = defineProps({\n  /**\n   * @description height of the header\n   */\n  height: {\n    type: String,\n    default: null,\n  },\n})\n\nconst ns = useNamespace('header')\nconst style = computed(() => {\n  return props.height\n    ? (ns.cssVarBlock({\n        height: props.height,\n      }) as CSSProperties)\n    : {}\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;mCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;AAYA,QAAM,MAAA,cAAkB;AACxB,OAAM,CAAA,GAAA,EAAA,CAAA;AACJ,KAAO,CAAA,CAAA;AACa,IAAA,YACN,EAAM,MAAA,KAAA;AAAA,MAChB,OACCA,SAAA,EAAA,EAAAC,kBAAA,CAAA,QAAA,EAAA;AAAA,QACN,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;;;;;;;;;;;;"}