import { inject, provide } from 'vue';

function useNodeExpandEventBroadcast(props) {
  const parentNodeMap = inject("TreeNodeMap", null);
  const currentNodeMap = {
    treeNodeExpand: (node) => {
      if (props.node !== node) {
        props.node.collapse();
      }
    },
    children: []
  };
  if (parentNodeMap) {
    parentNodeMap.children.push(currentNodeMap);
  }
  provide("TreeNodeMap", currentNodeMap);
  return {
    broadcastExpanded: (node) => {
      if (!props.accordion)
        return;
      for (const childNode of currentNodeMap.children) {
        childNode.treeNodeExpand(node);
      }
    }
  };
}

export { useNodeExpandEventBroadcast };
//# sourceMappingURL=useNodeExpandEventBroadcast.mjs.map
