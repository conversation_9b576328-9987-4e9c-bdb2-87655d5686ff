{"version": 3, "file": "helper.mjs", "sources": ["../../../../../../../packages/components/table/src/store/helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { watch } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { isObject } from '@element-plus/utils'\nimport useStore from '.'\n\nimport type { Store } from '.'\nimport type { Table, TableProps } from '../table/defaults'\n\nconst InitialStateMap = {\n  rowKey: 'rowKey',\n  defaultExpandAll: 'defaultExpandAll',\n  selectOnIndeterminate: 'selectOnIndeterminate',\n  indent: 'indent',\n  lazy: 'lazy',\n  data: 'data',\n  ['treeProps.hasChildren']: {\n    key: 'lazyColumnIdentifier',\n    default: 'hasChildren',\n  },\n  ['treeProps.children']: {\n    key: 'childrenColumnName',\n    default: 'children',\n  },\n  ['treeProps.checkStrictly']: {\n    key: 'checkStrictly',\n    default: false,\n  },\n}\n\nexport function createStore<T>(table: Table<T>, props: TableProps<T>) {\n  if (!table) {\n    throw new Error('Table is required.')\n  }\n\n  const store = useStore<T>()\n  // fix https://github.com/ElemeFE/element/issues/14075\n  // related pr https://github.com/ElemeFE/element/pull/14146\n  store.toggleAllSelection = debounce(store._toggleAllSelection, 10)\n  Object.keys(InitialStateMap).forEach((key) => {\n    handleValue(getArrKeysValue(props, key), key, store)\n  })\n  proxyTableProps(store, props)\n  return store\n}\n\nfunction proxyTableProps<T>(store: Store<T>, props: TableProps<T>) {\n  Object.keys(InitialStateMap).forEach((key) => {\n    watch(\n      () => getArrKeysValue(props, key),\n      (value) => {\n        handleValue(value, key, store)\n      }\n    )\n  })\n}\n\nfunction handleValue<T>(value, propsKey: string, store: Store<T>) {\n  let newVal = value\n  let storeKey = InitialStateMap[propsKey]\n  if (isObject(InitialStateMap[propsKey])) {\n    storeKey = storeKey.key\n    newVal = newVal || InitialStateMap[propsKey].default\n  }\n  store.states[storeKey].value = newVal\n}\n\nfunction getArrKeysValue<T>(props: TableProps<T>, keys: string) {\n  if (keys.includes('.')) {\n    const keyList = keys.split('.')\n    let value = props\n    keyList.forEach((key) => {\n      value = value[key]\n    })\n    return value\n  } else {\n    return props[keys]\n  }\n}\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,eAAe,GAAG;AACxB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,gBAAgB,EAAE,kBAAkB;AACtC,EAAE,qBAAqB,EAAE,uBAAuB;AAChD,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,CAAC,uBAAuB,GAAG;AAC7B,IAAI,GAAG,EAAE,sBAAsB;AAC/B,IAAI,OAAO,EAAE,aAAa;AAC1B,GAAG;AACH,EAAE,CAAC,oBAAoB,GAAG;AAC1B,IAAI,GAAG,EAAE,oBAAoB;AAC7B,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,CAAC,yBAAyB,GAAG;AAC/B,IAAI,GAAG,EAAE,eAAe;AACxB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC,CAAC;AACK,SAAS,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;AAC1C,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;AAC3B,EAAE,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;AACrE,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAChD,IAAI,WAAW,CAAC,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACzD,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAChC,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD,SAAS,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE;AACvC,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAChD,IAAI,KAAK,CAAC,MAAM,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK;AACxD,MAAM,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACrC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE;AAC7C,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;AAC3C,EAAE,IAAI,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE;AAC3C,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;AAC5B,IAAI,MAAM,GAAG,MAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;AACzD,GAAG;AACH,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC;AACxC,CAAC;AACD,SAAS,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE;AACtC,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC1B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC;AACtB,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,MAAM;AACT,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC;AACvB,GAAG;AACH;;;;"}