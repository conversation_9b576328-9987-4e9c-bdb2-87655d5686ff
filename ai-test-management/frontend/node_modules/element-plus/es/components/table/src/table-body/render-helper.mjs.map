{"version": 3, "file": "render-helper.mjs", "sources": ["../../../../../../../packages/components/table/src/table-body/render-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, h, inject } from 'vue'\nimport { merge } from 'lodash-unified'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isBoolean, isPropAbsent } from '@element-plus/utils'\nimport { getRowIdentity } from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport useEvents from './events-helper'\nimport useStyles from './styles-helper'\nimport TdWrapper from './td-wrapper.vue'\nimport type { TableBodyProps } from './defaults'\nimport type { RenderRowData, TableProps, TreeNode } from '../table/defaults'\n\nfunction useRender<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n  const {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  } = useEvents(props)\n  const {\n    getRowStyle,\n    getRowClass,\n    getCellStyle,\n    getCellClass,\n    getSpan,\n    getColspanRealWidth,\n  } = useStyles(props)\n  const firstDefaultColumnIndex = computed(() => {\n    return props.store.states.columns.value.findIndex(\n      ({ type }) => type === 'default'\n    )\n  })\n  const getKeyOfRow = (row: T, index: number) => {\n    const rowKey = (parent.props as Partial<TableProps<T>>).rowKey\n    if (rowKey) {\n      return getRowIdentity(row, rowKey)\n    }\n    return index\n  }\n  const rowRender = (\n    row: T,\n    $index: number,\n    treeRowData?: TreeNode,\n    expanded = false\n  ) => {\n    const { tooltipEffect, tooltipOptions, store } = props\n    const { indent, columns } = store.states\n    const rowClasses = getRowClass(row, $index)\n    let display = true\n    if (treeRowData) {\n      rowClasses.push(ns.em('row', `level-${treeRowData.level}`))\n      display = treeRowData.display\n    }\n    const displayStyle = display ? null : { display: 'none' }\n    return h(\n      'tr',\n      {\n        style: [displayStyle, getRowStyle(row, $index)],\n        class: rowClasses,\n        key: getKeyOfRow(row, $index),\n        onDblclick: ($event) => handleDoubleClick($event, row),\n        onClick: ($event) => handleClick($event, row),\n        onContextmenu: ($event) => handleContextMenu($event, row),\n        onMouseenter: () => handleMouseEnter($index),\n        onMouseleave: handleMouseLeave,\n      },\n      columns.value.map((column, cellIndex) => {\n        const { rowspan, colspan } = getSpan(row, column, $index, cellIndex)\n        if (!rowspan || !colspan) {\n          return null\n        }\n        const columnData = Object.assign({}, column)\n        columnData.realWidth = getColspanRealWidth(\n          columns.value,\n          colspan,\n          cellIndex\n        )\n        const data: RenderRowData<T> = {\n          store: props.store,\n          _self: props.context || parent,\n          column: columnData,\n          row,\n          $index,\n          cellIndex,\n          expanded,\n        }\n        if (cellIndex === firstDefaultColumnIndex.value && treeRowData) {\n          data.treeNode = {\n            indent: treeRowData.level * indent.value,\n            level: treeRowData.level,\n          }\n          if (isBoolean(treeRowData.expanded)) {\n            data.treeNode.expanded = treeRowData.expanded\n            // 表明是懒加载\n            if ('loading' in treeRowData) {\n              data.treeNode.loading = treeRowData.loading\n            }\n            if ('noLazyChildren' in treeRowData) {\n              data.treeNode.noLazyChildren = treeRowData.noLazyChildren\n            }\n          }\n        }\n        const baseKey = `${getKeyOfRow(row, $index)},${cellIndex}`\n        const patchKey = columnData.columnKey || columnData.rawColumnKey || ''\n        const mergedTooltipOptions =\n          column.showOverflowTooltip &&\n          merge(\n            {\n              effect: tooltipEffect,\n            },\n            tooltipOptions,\n            column.showOverflowTooltip\n          )\n        return h(\n          TdWrapper,\n          {\n            style: getCellStyle($index, cellIndex, row, column),\n            class: getCellClass($index, cellIndex, row, column, colspan - 1),\n            key: `${patchKey}${baseKey}`,\n            rowspan,\n            colspan,\n            onMouseenter: ($event) =>\n              handleCellMouseEnter($event, row, mergedTooltipOptions),\n            onMouseleave: handleCellMouseLeave,\n          },\n          {\n            default: () => cellChildren(cellIndex, column, data),\n          }\n        )\n      })\n    )\n  }\n  const cellChildren = (cellIndex, column, data) => {\n    return column.renderCell(data)\n  }\n\n  const wrappedRowRender = (row: T, $index: number) => {\n    const store = props.store\n    const { isRowExpanded, assertRowKey } = store\n    const { treeData, lazyTreeNodeMap, childrenColumnName, rowKey } =\n      store.states\n    const columns = store.states.columns.value\n    const hasExpandColumn = columns.some(({ type }) => type === 'expand')\n    if (hasExpandColumn) {\n      const expanded = isRowExpanded(row)\n      const tr = rowRender(row, $index, undefined, expanded)\n      const renderExpanded = parent.renderExpanded\n      if (!renderExpanded) {\n        console.error('[Element Error]renderExpanded is required.')\n        return tr\n      }\n\n      // 在没设置时候避免 h 执行\n      // 非保留模式且未展开时，直接返回\n      // 使用二维数组包装，避免修改 $index\n      const rows = [[tr]]\n\n      // 仅在需要时创建展开行（保留模式或展开状态）\n      if (parent.props.preserveExpandedContent || expanded) {\n        rows[0].push(\n          h(\n            'tr',\n            {\n              key: `expanded-row__${tr.key as string}`,\n              style: { display: expanded ? '' : 'none' },\n            },\n            [\n              h(\n                'td',\n                {\n                  colspan: columns.length,\n                  class: `${ns.e('cell')} ${ns.e('expanded-cell')}`,\n                },\n                [renderExpanded({ row, $index, store, expanded })]\n              ),\n            ]\n          )\n        )\n      }\n\n      return rows\n    } else if (Object.keys(treeData.value).length) {\n      assertRowKey()\n      // TreeTable 时，rowKey 必须由用户设定，不使用 getKeyOfRow 计算\n      // 在调用 rowRender 函数时，仍然会计算 rowKey，不太好的操作\n      const key = getRowIdentity(row, rowKey.value)\n      let cur = treeData.value[key]\n      let treeRowData = null\n      if (cur) {\n        treeRowData = {\n          expanded: cur.expanded,\n          level: cur.level,\n          display: true,\n        }\n        if (isBoolean(cur.lazy)) {\n          if (isBoolean(cur.loaded) && cur.loaded) {\n            treeRowData.noLazyChildren = !(cur.children && cur.children.length)\n          }\n          treeRowData.loading = cur.loading\n        }\n      }\n      const tmp = [rowRender(row, $index, treeRowData)]\n      // 渲染嵌套数据\n      if (cur) {\n        // currentRow 记录的是 index，所以还需主动增加 TreeTable 的 index\n        let i = 0\n        const traverse = (children, parent) => {\n          if (!(children && children.length && parent)) return\n          children.forEach((node) => {\n            // 父节点的 display 状态影响子节点的显示状态\n            const innerTreeRowData = {\n              display: parent.display && parent.expanded,\n              level: parent.level + 1,\n              expanded: false,\n              noLazyChildren: false,\n              loading: false,\n            }\n            const childKey = getRowIdentity(node, rowKey.value)\n            if (isPropAbsent(childKey)) {\n              throw new Error('For nested data item, row-key is required.')\n            }\n            cur = { ...treeData.value[childKey] }\n            // 对于当前节点，分成有无子节点两种情况。\n            // 如果包含子节点的，设置 expanded 属性。\n            // 对于它子节点的 display 属性由它本身的 expanded 与 display 共同决定。\n            if (cur) {\n              innerTreeRowData.expanded = cur.expanded\n              // 懒加载的某些节点，level 未知\n              cur.level = cur.level || innerTreeRowData.level\n              cur.display = !!(cur.expanded && innerTreeRowData.display)\n              if (isBoolean(cur.lazy)) {\n                if (isBoolean(cur.loaded) && cur.loaded) {\n                  innerTreeRowData.noLazyChildren = !(\n                    cur.children && cur.children.length\n                  )\n                }\n                innerTreeRowData.loading = cur.loading\n              }\n            }\n            i++\n            tmp.push(rowRender(node, $index + i, innerTreeRowData))\n            if (cur) {\n              const nodes =\n                lazyTreeNodeMap.value[childKey] ||\n                node[childrenColumnName.value]\n              traverse(nodes, cur)\n            }\n          })\n        }\n        // 对于 root 节点，display 一定为 true\n        cur.display = true\n        const nodes =\n          lazyTreeNodeMap.value[key] || row[childrenColumnName.value]\n        traverse(nodes, cur)\n      }\n      return tmp\n    } else {\n      return rowRender(row, $index, undefined)\n    }\n  }\n\n  return {\n    wrappedRowRender,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useRender\n"], "names": [], "mappings": ";;;;;;;;;;AASA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACnC,EAAE,MAAM;AACR,IAAI,iBAAiB;AACrB,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AACvB,EAAE,MAAM;AACR,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,OAAO;AACX,IAAI,mBAAmB;AACvB,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AACvB,EAAE,MAAM,uBAAuB,GAAG,QAAQ,CAAC,MAAM;AACjD,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,KAAK,SAAS,CAAC,CAAC;AACxF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,KAAK,KAAK;AACtC,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AACvC,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,GAAG,KAAK,KAAK;AACpE,IAAI,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AAC3D,IAAI,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;AAC7C,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAChD,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACpC,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AAC9D,IAAI,OAAO,CAAC,CAAC,IAAI,EAAE;AACnB,MAAM,KAAK,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACrD,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,GAAG,EAAE,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC;AACnC,MAAM,UAAU,EAAE,CAAC,MAAM,KAAK,iBAAiB,CAAC,MAAM,EAAE,GAAG,CAAC;AAC5D,MAAM,OAAO,EAAE,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC;AACnD,MAAM,aAAa,EAAE,CAAC,MAAM,KAAK,iBAAiB,CAAC,MAAM,EAAE,GAAG,CAAC;AAC/D,MAAM,YAAY,EAAE,MAAM,gBAAgB,CAAC,MAAM,CAAC;AAClD,MAAM,YAAY,EAAE,gBAAgB;AACpC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,KAAK;AAChD,MAAM,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;AAC3E,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE;AAChC,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACnD,MAAM,UAAU,CAAC,SAAS,GAAG,mBAAmB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AACpF,MAAM,MAAM,IAAI,GAAG;AACnB,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM;AACtC,QAAQ,MAAM,EAAE,UAAU;AAC1B,QAAQ,GAAG;AACX,QAAQ,MAAM;AACd,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,OAAO,CAAC;AACR,MAAM,IAAI,SAAS,KAAK,uBAAuB,CAAC,KAAK,IAAI,WAAW,EAAE;AACtE,QAAQ,IAAI,CAAC,QAAQ,GAAG;AACxB,UAAU,MAAM,EAAE,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;AAClD,UAAU,KAAK,EAAE,WAAW,CAAC,KAAK;AAClC,SAAS,CAAC;AACV,QAAQ,IAAI,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;AAC7C,UAAU,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;AACxD,UAAU,IAAI,SAAS,IAAI,WAAW,EAAE;AACxC,YAAY,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACxD,WAAW;AACX,UAAU,IAAI,gBAAgB,IAAI,WAAW,EAAE;AAC/C,YAAY,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AACtE,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACjE,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,YAAY,IAAI,EAAE,CAAC;AAC7E,MAAM,MAAM,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,IAAI,KAAK,CAAC;AACvE,QAAQ,MAAM,EAAE,aAAa;AAC7B,OAAO,EAAE,cAAc,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACrD,MAAM,OAAO,CAAC,CAAC,SAAS,EAAE;AAC1B,QAAQ,KAAK,EAAE,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC;AAC3D,QAAQ,KAAK,EAAE,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,GAAG,CAAC,CAAC;AACxE,QAAQ,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;AACpC,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,YAAY,EAAE,CAAC,MAAM,KAAK,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE,oBAAoB,CAAC;AACzF,QAAQ,YAAY,EAAE,oBAAoB;AAC1C,OAAO,EAAE;AACT,QAAQ,OAAO,EAAE,MAAM,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC;AAC5D,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,KAAK;AACpD,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACnC,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK;AAC5C,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC9B,IAAI,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC;AAClD,IAAI,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;AACnF,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,KAAK,QAAQ,CAAC,CAAC;AAC1E,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAC1C,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC1D,MAAM,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AACnD,MAAM,IAAI,CAAC,cAAc,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;AACpE,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP,MAAM,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,uBAAuB,IAAI,QAAQ,EAAE;AAC5D,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;AAC7B,UAAU,GAAG,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AACxC,UAAU,KAAK,EAAE,EAAE,OAAO,EAAE,QAAQ,GAAG,EAAE,GAAG,MAAM,EAAE;AACpD,SAAS,EAAE;AACX,UAAU,CAAC,CAAC,IAAI,EAAE;AAClB,YAAY,OAAO,EAAE,OAAO,CAAC,MAAM;AACnC,YAAY,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;AAC7D,WAAW,EAAE,CAAC,cAAc,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;AAChE,SAAS,CAAC,CAAC,CAAC;AACZ,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;AACnD,MAAM,YAAY,EAAE,CAAC;AACrB,MAAM,MAAM,GAAG,GAAG,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AACpD,MAAM,IAAI,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpC,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC;AAC7B,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,WAAW,GAAG;AACtB,UAAU,QAAQ,EAAE,GAAG,CAAC,QAAQ;AAChC,UAAU,KAAK,EAAE,GAAG,CAAC,KAAK;AAC1B,UAAU,OAAO,EAAE,IAAI;AACvB,SAAS,CAAC;AACV,QAAQ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACjC,UAAU,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;AACnD,YAAY,WAAW,CAAC,cAAc,GAAG,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAChF,WAAW;AACX,UAAU,WAAW,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AAC5C,SAAS;AACT,OAAO;AACP,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;AACxD,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,QAAQ,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,OAAO,KAAK;AAChD,UAAU,IAAI,EAAE,QAAQ,IAAI,QAAQ,CAAC,MAAM,IAAI,OAAO,CAAC;AACvD,YAAY,OAAO;AACnB,UAAU,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACrC,YAAY,MAAM,gBAAgB,GAAG;AACrC,cAAc,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ;AAC1D,cAAc,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC;AACtC,cAAc,QAAQ,EAAE,KAAK;AAC7B,cAAc,cAAc,EAAE,KAAK;AACnC,cAAc,OAAO,EAAE,KAAK;AAC5B,aAAa,CAAC;AACd,YAAY,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AAChE,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE;AACxC,cAAc,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;AAC5E,aAAa;AACb,YAAY,GAAG,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;AAClD,YAAY,IAAI,GAAG,EAAE;AACrB,cAAc,gBAAgB,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AACvD,cAAc,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC;AAC9D,cAAc,GAAG,CAAC,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC,QAAQ,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACzE,cAAc,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACvC,gBAAgB,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;AACzD,kBAAkB,gBAAgB,CAAC,cAAc,GAAG,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3F,iBAAiB;AACjB,gBAAgB,gBAAgB,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AACvD,eAAe;AACf,aAAa;AACb,YAAY,CAAC,EAAE,CAAC;AAChB,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;AACpE,YAAY,IAAI,GAAG,EAAE;AACrB,cAAc,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC/F,cAAc,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACpC,aAAa;AACb,WAAW,CAAC,CAAC;AACb,SAAS,CAAC;AACV,QAAQ,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,QAAQ,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAClF,QAAQ,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC7B,OAAO;AACP,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK,MAAM;AACX,MAAM,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}