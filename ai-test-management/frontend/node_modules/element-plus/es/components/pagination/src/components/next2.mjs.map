{"version": 3, "file": "next2.mjs", "sources": ["../../../../../../../packages/components/pagination/src/components/next.vue"], "sourcesContent": ["<template>\n  <button\n    type=\"button\"\n    class=\"btn-next\"\n    :disabled=\"internalDisabled\"\n    :aria-label=\"nextText || t('el.pagination.next')\"\n    :aria-disabled=\"internalDisabled\"\n    @click=\"$emit('click', $event)\"\n  >\n    <span v-if=\"nextText\">{{ nextText }}</span>\n    <el-icon v-else>\n      <component :is=\"nextIcon\" />\n    </el-icon>\n  </button>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { paginationNextProps } from './next'\n\ndefineOptions({\n  name: 'ElPaginationNext',\n})\n\nconst props = defineProps(paginationNextProps)\n\ndefineEmits(['click'])\n\nconst { t } = useLocale()\n\nconst internalDisabled = computed(\n  () =>\n    props.disabled ||\n    props.currentPage === props.pageCount ||\n    props.pageCount === 0\n)\n</script>\n"], "names": ["_openBlock", "_createElementBlock"], "mappings": ";;;;;;mCAsBc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAMA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AAExB,IAAA,MAAM,gBAAmB,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,WAAA,KAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,SAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACvB,OAAA,CACE,MAAM,MACN,KAAA;AACoB,MACxB,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,QAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;"}