{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/dialog/src/constants.ts"], "sourcesContent": ["import type { CSSProperties, ComputedRef, InjectionKey, Ref } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\n\nexport type DialogContext = {\n  dialogRef: Ref<HTMLElement | undefined>\n  headerRef: Ref<HTMLElement | undefined>\n  bodyId: Ref<string>\n  ns: UseNamespaceReturn\n  rendered: Ref<boolean>\n  style: ComputedRef<CSSProperties>\n}\n\nexport const dialogInjectionKey: InjectionKey<DialogContext> =\n  Symbol('dialogInjectionKey')\n"], "names": [], "mappings": "AAAY,MAAC,kBAAkB,GAAG,MAAM,CAAC,oBAAoB;;;;"}