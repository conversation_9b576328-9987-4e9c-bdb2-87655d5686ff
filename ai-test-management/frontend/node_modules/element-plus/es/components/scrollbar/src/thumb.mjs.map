{"version": 3, "file": "thumb.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/thumb.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Thumb from './thumb.vue'\n\nexport const thumbProps = buildProps({\n  vertical: Boolean,\n  size: String,\n  move: Number,\n  ratio: {\n    type: Number,\n    required: true,\n  },\n  always: Boolean,\n} as const)\nexport type ThumbProps = ExtractPropTypes<typeof thumbProps>\n\nexport type ThumbInstance = InstanceType<typeof Thumb> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,UAAU,GAAG,UAAU,CAAC;AACrC,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,CAAC;;;;"}