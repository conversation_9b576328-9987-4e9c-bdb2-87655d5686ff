{"version": 3, "file": "input-number.mjs", "sources": ["../../../../../../packages/components/input-number/src/input-number.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport { buildProps, isNumber } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type InputNumber from './input-number.vue'\n\nexport const inputNumberProps = buildProps({\n  /**\n   * @description same as `id` in native input\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description incremental step\n   */\n  step: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description whether input value can only be multiple of step\n   */\n  stepStrictly: Boolean,\n  /**\n   * @description the maximum allowed value\n   */\n  max: {\n    type: Number,\n    default: Number.POSITIVE_INFINITY,\n  },\n  /**\n   * @description the minimum allowed value\n   */\n  min: {\n    type: Number,\n    default: Number.NEGATIVE_INFINITY,\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [Number, null],\n  },\n  /**\n   * @description same as `readonly` in native input\n   */\n  readonly: Boolean,\n  /**\n   * @description whether the component is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description size of the component\n   */\n  size: useSizeProp,\n  /**\n   * @description whether to enable the control buttons\n   */\n  controls: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description position of the control buttons\n   */\n  controlsPosition: {\n    type: String,\n    default: '',\n    values: ['', 'right'],\n  },\n  /**\n   * @description value should be set when input box is cleared\n   */\n  valueOnClear: {\n    type: [String, Number, null],\n    validator: (val: 'min' | 'max' | number | null) =>\n      val === null || isNumber(val) || ['min', 'max'].includes(val),\n    default: null,\n  },\n  /**\n   * @description same as `name` in native input\n   */\n  name: String,\n  /**\n   * @description same as `placeholder` in native input\n   */\n  placeholder: String,\n  /**\n   * @description precision of input value\n   */\n  precision: {\n    type: Number,\n    validator: (val: number) =>\n      val >= 0 && val === Number.parseInt(`${val}`, 10),\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useAriaProps(['ariaLabel']),\n} as const)\nexport type InputNumberProps = ExtractPropTypes<typeof inputNumberProps>\n\nexport const inputNumberEmits = {\n  [CHANGE_EVENT]: (cur: number | undefined, prev: number | undefined) =>\n    prev !== cur,\n  blur: (e: FocusEvent) => e instanceof FocusEvent,\n  focus: (e: FocusEvent) => e instanceof FocusEvent,\n  [INPUT_EVENT]: (val: number | null | undefined) =>\n    isNumber(val) || isNil(val),\n  [UPDATE_MODEL_EVENT]: (val: number | undefined) =>\n    isNumber(val) || isNil(val),\n}\nexport type InputNumberEmits = typeof inputNumberEmits\n\nexport type InputNumberInstance = InstanceType<typeof InputNumber> & unknown\n"], "names": [], "mappings": ";;;;;;;AAQY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM,CAAC,iBAAiB;AACrC,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM,CAAC,iBAAiB;AACrC,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;AACxB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC;AACzB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;AAChC,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AACrF,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AACzE,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAG,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG;AAChC,EAAE,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,KAAK,GAAG;AAC7C,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU;AACtC,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU;AACvC,EAAE,CAAC,WAAW,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC;AACrD,EAAE,CAAC,kBAAkB,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC;AAC5D;;;;"}