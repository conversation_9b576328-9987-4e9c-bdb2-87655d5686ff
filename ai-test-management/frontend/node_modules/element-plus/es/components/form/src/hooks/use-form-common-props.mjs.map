{"version": 3, "file": "use-form-common-props.mjs", "sources": ["../../../../../../../packages/components/form/src/hooks/use-form-common-props.ts"], "sourcesContent": ["import { computed, inject, ref, unref } from 'vue'\nimport { useGlobalSize, useProp } from '@element-plus/hooks'\nimport { formContextKey, formItemContextKey } from '../constants'\n\nimport type { ComponentSize } from '@element-plus/constants'\nimport type { MaybeRef } from '@vueuse/core'\n\nexport const useFormSize = (\n  fallback?: MaybeRef<ComponentSize | undefined>,\n  ignore: Partial<Record<'prop' | 'form' | 'formItem' | 'global', boolean>> = {}\n) => {\n  const emptyRef = ref(undefined)\n\n  const size = ignore.prop ? emptyRef : useProp<ComponentSize>('size')\n  const globalConfig = ignore.global ? emptyRef : useGlobalSize()\n  const form = ignore.form\n    ? { size: undefined }\n    : inject(formContextKey, undefined)\n  const formItem = ignore.formItem\n    ? { size: undefined }\n    : inject(formItemContextKey, undefined)\n\n  return computed(\n    (): ComponentSize =>\n      size.value ||\n      unref(fallback) ||\n      formItem?.size ||\n      form?.size ||\n      globalConfig.value ||\n      ''\n  )\n}\n\nexport const useFormDisabled = (fallback?: MaybeRef<boolean | undefined>) => {\n  const disabled = useProp<boolean>('disabled')\n  const form = inject(formContextKey, undefined)\n  return computed(\n    () => disabled.value || unref(fallback) || form?.disabled || false\n  )\n}\n\n// These exports are used for preventing breaking changes\nexport const useSize = useFormSize\nexport const useDisabled = useFormDisabled\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,WAAW,GAAG,CAAC,QAAQ,EAAE,MAAM,GAAG,EAAE,KAAK;AACtD,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AACxD,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,aAAa,EAAE,CAAC;AAClE,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;AAC/E,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3F,EAAE,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACzK,EAAE;AACU,MAAC,eAAe,GAAG,CAAC,QAAQ,KAAK;AAC7C,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AACvC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9C,EAAE,OAAO,QAAQ,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC;AAC/G,EAAE;AACU,MAAC,OAAO,GAAG,YAAY;AACvB,MAAC,WAAW,GAAG;;;;"}