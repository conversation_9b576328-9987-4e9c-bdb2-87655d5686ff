{"version": 3, "file": "form.mjs", "sources": ["../../../../../../packages/components/form/src/form.ts"], "sourcesContent": ["import { componentSizes } from '@element-plus/constants'\nimport {\n  buildProps,\n  definePropType,\n  isArray,\n  isBoolean,\n  isString,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { FormItemProp } from './form-item'\nimport type { FormRules } from './types'\n\nexport const formMetaProps = buildProps({\n  /**\n   * @description Control the size of components in this form.\n   */\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n  /**\n   * @description Whether to disable all components in this form. If set to `true`, it will override the `disabled` prop of the inner component.\n   */\n  disabled: Boolean,\n} as const)\n\nexport const formProps = buildProps({\n  ...formMetaProps,\n  /**\n   * @description Data of form component.\n   */\n  model: Object,\n  /**\n   * @description Validation rules of form.\n   */\n  rules: {\n    type: definePropType<FormRules>(Object),\n  },\n  /**\n   * @description Position of label. If set to `'left'` or `'right'`, `label-width` prop is also required.\n   */\n  labelPosition: {\n    type: String,\n    values: ['left', 'right', 'top'],\n    default: 'right',\n  },\n  /**\n   * @description Position of asterisk.\n   */\n  requireAsteriskPosition: {\n    type: String,\n    values: ['left', 'right'],\n    default: 'left',\n  },\n  /**\n   * @description Width of label, e.g. `'50px'`. All its direct child form items will inherit this value. `auto` is supported.\n   */\n  labelWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description Suffix of the label.\n   */\n  labelSuffix: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description Whether the form is inline.\n   */\n  inline: Boolean,\n  /**\n   * @description Whether to display the error message inline with the form item.\n   */\n  inlineMessage: Boolean,\n  /**\n   * @description Whether to display an icon indicating the validation result.\n   */\n  statusIcon: Boolean,\n  /**\n   * @description Whether to show the error message.\n   */\n  showMessage: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Whether to trigger validation when the `rules` prop is changed.\n   */\n  validateOnRuleChange: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Whether to hide required fields should have a red asterisk (star) beside their labels.\n   */\n  hideRequiredAsterisk: Boolean,\n  /**\n   * @description When validation fails, scroll to the first error form entry.\n   */\n  scrollToError: Boolean,\n  /**\n   * @description When validation fails, it scrolls to the first error item based on the scrollIntoView option.\n   */\n  scrollIntoViewOptions: {\n    type: [Object, Boolean],\n    default: true,\n  },\n} as const)\nexport type FormProps = ExtractPropTypes<typeof formProps>\nexport type FormMetaProps = ExtractPropTypes<typeof formMetaProps>\n\nexport const formEmits = {\n  validate: (prop: FormItemProp, isValid: boolean, message: string) =>\n    (isArray(prop) || isString(prop)) &&\n    isBoolean(isValid) &&\n    isString(message),\n}\nexport type FormEmits = typeof formEmits\n"], "names": [], "mappings": ";;;;;AAQY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,cAAc;AAC1B,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC,EAAE;AACS,MAAC,SAAS,GAAG,UAAU,CAAC;AACpC,EAAE,GAAG,aAAa;AAClB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;AACpC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,uBAAuB,EAAE;AAC3B,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;AAC7B,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,oBAAoB,EAAE;AACxB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,oBAAoB,EAAE,OAAO;AAC/B,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,qBAAqB,EAAE;AACzB,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;AAC3B,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,SAAS,GAAG;AACzB,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC;AACpH;;;;"}