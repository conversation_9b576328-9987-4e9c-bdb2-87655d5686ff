{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/form/src/utils.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { debugWarn, ensureArray } from '@element-plus/utils'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { FormItemContext } from './types'\nimport type { FormItemProp } from './form-item'\n\nconst SCOPE = 'ElForm'\n\nexport function useFormLabelWidth() {\n  const potentialLabelWidthArr = ref<number[]>([])\n\n  const autoLabelWidth = computed(() => {\n    if (!potentialLabelWidthArr.value.length) return '0'\n    const max = Math.max(...potentialLabelWidthArr.value)\n    return max ? `${max}px` : ''\n  })\n\n  function getLabelWidthIndex(width: number) {\n    const index = potentialLabelWidthArr.value.indexOf(width)\n    if (index === -1 && autoLabelWidth.value === '0') {\n      debugWarn(SCOPE, `unexpected width ${width}`)\n    }\n    return index\n  }\n\n  function registerLabelWidth(val: number, oldVal: number) {\n    if (val && oldVal) {\n      const index = getLabelWidthIndex(oldVal)\n      potentialLabelWidthArr.value.splice(index, 1, val)\n    } else if (val) {\n      potentialLabelWidthArr.value.push(val)\n    }\n  }\n\n  function deregisterLabelWidth(val: number) {\n    const index = getLabelWidthIndex(val)\n    if (index > -1) {\n      potentialLabelWidthArr.value.splice(index, 1)\n    }\n  }\n\n  return {\n    autoLabelWidth,\n    registerLabelWidth,\n    deregisterLabelWidth,\n  }\n}\n\nexport const filterFields = (\n  fields: FormItemContext[],\n  props: Arrayable<FormItemProp>\n) => {\n  const normalized = ensureArray(props)\n  return normalized.length > 0\n    ? fields.filter((field) => field.prop && normalized.includes(field.prop))\n    : fields\n}\n"], "names": ["ensureArray"], "mappings": ";;;;AAEA,MAAM,KAAK,GAAG,QAAQ,CAAC;AAChB,SAAS,iBAAiB,GAAG;AACpC,EAAE,MAAM,sBAAsB,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACzC,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,MAAM;AAC5C,MAAM,OAAO,GAAG,CAAC;AACjB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAC1D,IAAI,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AACjC,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACrC,IAAI,MAAM,KAAK,GAAG,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9D,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,EAAE;AACtD,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE;AAC3C,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE;AACvB,MAAM,MAAM,KAAK,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAC/C,MAAM,sBAAsB,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AACzD,KAAK,MAAM,IAAI,GAAG,EAAE;AACpB,MAAM,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,KAAK;AACL,GAAG;AACH,EAAE,SAAS,oBAAoB,CAAC,GAAG,EAAE;AACrC,IAAI,MAAM,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC1C,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACpB,MAAM,sBAAsB,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,GAAG;AACH,EAAE,OAAO;AACT,IAAI,cAAc;AAClB,IAAI,kBAAkB;AACtB,IAAI,oBAAoB;AACxB,GAAG,CAAC;AACJ,CAAC;AACW,MAAC,YAAY,GAAG,CAAC,MAAM,EAAE,KAAK,KAAK;AAC/C,EAAE,MAAM,UAAU,GAAGA,SAAW,CAAC,KAAK,CAAC,CAAC;AACxC,EAAE,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;AAClH;;;;"}