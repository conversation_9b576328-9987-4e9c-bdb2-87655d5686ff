{"version": 3, "file": "radio-button2.mjs", "sources": ["../../../../../../packages/components/radio/src/radio-button.vue"], "sourcesContent": ["<template>\n  <label\n    :class=\"[\n      ns.b('button'),\n      ns.is('active', modelValue === actualValue),\n      ns.is('disabled', disabled),\n      ns.is('focus', focus),\n      ns.bm('button', size),\n    ]\"\n  >\n    <input\n      ref=\"radioRef\"\n      v-model=\"modelValue\"\n      :class=\"ns.be('button', 'original-radio')\"\n      :value=\"actualValue\"\n      type=\"radio\"\n      :name=\"name || radioGroup?.name\"\n      :disabled=\"disabled\"\n      @focus=\"focus = true\"\n      @blur=\"focus = false\"\n      @click.stop\n    />\n    <span\n      :class=\"ns.be('button', 'inner')\"\n      :style=\"modelValue === actualValue ? activeStyle : {}\"\n      @keydown.stop\n    >\n      <slot>\n        {{ label }}\n      </slot>\n    </span>\n  </label>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useRadio } from './use-radio'\nimport { radioButtonProps } from './radio-button'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElRadioButton',\n})\n\nconst props = defineProps(radioButtonProps)\n\nconst ns = useNamespace('radio')\nconst { radioRef, focus, size, disabled, modelValue, radioGroup, actualValue } =\n  useRadio(props)\n\nconst activeStyle = computed<CSSProperties>(() => {\n  return {\n    backgroundColor: radioGroup?.fill || '',\n    borderColor: radioGroup?.fill || '',\n    boxShadow: radioGroup?.fill ? `-1px 0 0 0 ${radioGroup.fill}` : '',\n    color: radioGroup?.textColor || '',\n  }\n})\n</script>\n"], "names": [], "mappings": ";;;;;;mCAyCc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAM,MAAA,EAAE,QAAU,EAAA,KAAA,EAAO,IAAM,EAAA,QAAA,EAAU,YAAY,UAAY,EAAA,WAAA,EAC/D,GAAA,QAAA,CAAS,KAAK,CAAA,CAAA;AAEhB,IAAM,MAAA,WAAA,GAAc,SAAwB,MAAM;AAChD,MAAO,OAAA;AAAA,QACL,eAAA,EAAiB,eAAoB,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,IAAA,KAAA,EAAA;AAAA,QACrC,WAAA,EAAa,eAAoB,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,IAAA,KAAA,EAAA;AAAA,QACjC,WAAW,CAAY,UAAA,IAAA,IAAO,GAAc,KAAA,CAAA,GAAA,UAAA,CAAA,QAAoB,CAAA,WAAA,EAAA,UAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA;AAAA,QAChE,KAAA,EAAO,eAAyB,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,SAAA,KAAA,EAAA;AAAA,OAClC,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}