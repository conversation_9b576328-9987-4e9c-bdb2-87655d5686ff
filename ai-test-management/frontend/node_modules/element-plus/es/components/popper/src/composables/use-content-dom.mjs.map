{"version": 3, "file": "use-content-dom.mjs", "sources": ["../../../../../../../packages/components/popper/src/composables/use-content-dom.ts"], "sourcesContent": ["import { computed, ref, unref } from 'vue'\nimport { useNamespace, useZIndex } from '@element-plus/hooks'\n\nimport { isNumber } from '@element-plus/utils'\nimport type { CSSProperties, StyleValue } from 'vue'\nimport type { UsePopperReturn } from '@element-plus/hooks'\nimport type { UsePopperContentReturn } from './use-content'\nimport type { PopperContentProps } from '../content'\n\nexport const usePopperContentDOM = (\n  props: PopperContentProps,\n  {\n    attributes,\n    styles,\n    role,\n  }: Pick<UsePopperReturn, 'attributes' | 'styles'> &\n    Pick<UsePopperContentReturn, 'role'>\n) => {\n  const { nextZIndex } = useZIndex()\n  const ns = useNamespace('popper')\n\n  const contentAttrs = computed(() => unref(attributes).popper)\n  const contentZIndex = ref<number>(\n    isNumber(props.zIndex) ? props.zIndex : nextZIndex()\n  )\n  const contentClass = computed(() => [\n    ns.b(),\n    ns.is('pure', props.pure),\n    ns.is(props.effect),\n    props.popperClass,\n  ])\n  const contentStyle = computed<StyleValue[]>(() => {\n    return [\n      { zIndex: unref(contentZIndex) } as CSSProperties,\n      unref(styles).popper as CSSProperties,\n      props.popperStyle || {},\n    ]\n  })\n  const ariaModal = computed<string | undefined>(() =>\n    role.value === 'dialog' ? 'false' : undefined\n  )\n  const arrowStyle = computed(\n    () => (unref(styles).arrow || {}) as CSSProperties\n  )\n\n  const updateZIndex = () => {\n    contentZIndex.value = isNumber(props.zIndex) ? props.zIndex : nextZIndex()\n  }\n\n  return {\n    ariaModal,\n    arrowStyle,\n    contentAttrs,\n    contentClass,\n    contentStyle,\n    contentZIndex,\n\n    updateZIndex,\n  }\n}\n\nexport type UsePopperContentDOMReturn = ReturnType<typeof usePopperContentDOM>\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,mBAAmB,GAAG,CAAC,KAAK,EAAE;AAC3C,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,IAAI;AACN,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,SAAS,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACpC,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC;AAChE,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC,CAAC;AAClF,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,EAAE,CAAC,CAAC,EAAE;AACV,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC;AAC7B,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;AACvB,IAAI,KAAK,CAAC,WAAW;AACrB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO;AACX,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE;AACtC,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM;AAC1B,MAAM,KAAK,CAAC,WAAW,IAAI,EAAE;AAC7B,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;AAC/E,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AAC/D,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;AAC/E,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ;;;;"}