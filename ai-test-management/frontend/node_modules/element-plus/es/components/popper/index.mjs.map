{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/popper/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Popper from './src/popper.vue'\n\nimport ElPopperArrow from './src/arrow.vue'\nimport ElPopperTrigger from './src/trigger.vue'\nimport ElPopperContent from './src/content.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport { ElPopperArrow, ElPopperTrigger, ElPopperContent }\n\nexport const ElPopper: SFCWithInstall<typeof Popper> = withInstall(Popper)\nexport default ElPopper\n\nexport * from './src/popper'\nexport * from './src/trigger'\nexport * from './src/content'\nexport * from './src/arrow'\nexport * from './src/constants'\n\nexport type { Placement, Options } from '@popperjs/core'\n"], "names": [], "mappings": ";;;;;;;;;;;AAMY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}