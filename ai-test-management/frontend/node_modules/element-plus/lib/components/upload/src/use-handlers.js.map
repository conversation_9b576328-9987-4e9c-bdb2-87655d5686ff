{"version": 3, "file": "use-handlers.js", "sources": ["../../../../../../packages/components/upload/src/use-handlers.ts"], "sourcesContent": ["import { watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { useVModel } from '@vueuse/core'\nimport { debugWarn, throwError } from '@element-plus/utils'\nimport { genFileId } from './upload'\nimport type { ShallowRef } from 'vue'\nimport type {\n  UploadContentInstance,\n  UploadContentProps,\n} from './upload-content'\nimport type {\n  UploadFile,\n  UploadFiles,\n  UploadProps,\n  UploadRawFile,\n  UploadStatus,\n} from './upload'\n\nconst SCOPE = 'ElUpload'\n\nconst revokeFileObjectURL = (file: UploadFile) => {\n  if (file.url?.startsWith('blob:')) {\n    URL.revokeObjectURL(file.url)\n  }\n}\n\nexport const useHandlers = (\n  props: UploadProps,\n  uploadRef: ShallowRef<UploadContentInstance | undefined>\n) => {\n  const uploadFiles = useVModel(\n    props as Omit<UploadProps, 'fileList'> & { fileList: UploadFiles },\n    'fileList',\n    undefined,\n    { passive: true }\n  )\n\n  const getFile = (rawFile: UploadRawFile) =>\n    uploadFiles.value.find((file) => file.uid === rawFile.uid)\n\n  function abort(file: UploadFile) {\n    uploadRef.value?.abort(file)\n  }\n\n  function clearFiles(\n    /** @default ['ready', 'uploading', 'success', 'fail'] */\n    states: UploadStatus[] = ['ready', 'uploading', 'success', 'fail']\n  ) {\n    uploadFiles.value = uploadFiles.value.filter(\n      (row) => !states.includes(row.status)\n    )\n  }\n\n  function removeFile(file: UploadFile) {\n    uploadFiles.value = uploadFiles.value.filter(\n      (uploadFile) => uploadFile.uid !== file.uid\n    )\n  }\n\n  const handleError: UploadContentProps['onError'] = (err, rawFile) => {\n    const file = getFile(rawFile)\n    if (!file) return\n\n    console.error(err)\n    file.status = 'fail'\n    removeFile(file)\n    props.onError(err, file, uploadFiles.value)\n    props.onChange(file, uploadFiles.value)\n  }\n\n  const handleProgress: UploadContentProps['onProgress'] = (evt, rawFile) => {\n    const file = getFile(rawFile)\n    if (!file) return\n\n    props.onProgress(evt, file, uploadFiles.value)\n    file.status = 'uploading'\n    file.percentage = Math.round(evt.percent)\n  }\n\n  const handleSuccess: UploadContentProps['onSuccess'] = (\n    response,\n    rawFile\n  ) => {\n    const file = getFile(rawFile)\n    if (!file) return\n\n    file.status = 'success'\n    file.response = response\n    props.onSuccess(response, file, uploadFiles.value)\n    props.onChange(file, uploadFiles.value)\n  }\n\n  const handleStart: UploadContentProps['onStart'] = (file) => {\n    if (isNil(file.uid)) file.uid = genFileId()\n    const uploadFile: UploadFile = {\n      name: file.name,\n      percentage: 0,\n      status: 'ready',\n      size: file.size,\n      raw: file,\n      uid: file.uid,\n    }\n    if (props.listType === 'picture-card' || props.listType === 'picture') {\n      try {\n        uploadFile.url = URL.createObjectURL(file)\n      } catch (err: unknown) {\n        debugWarn(SCOPE, (err as Error).message)\n        props.onError(err as Error, uploadFile, uploadFiles.value)\n      }\n    }\n    uploadFiles.value = [...uploadFiles.value, uploadFile]\n    props.onChange(uploadFile, uploadFiles.value)\n  }\n\n  const handleRemove: UploadContentProps['onRemove'] = async (\n    file\n  ): Promise<void> => {\n    const uploadFile = file instanceof File ? getFile(file) : file\n    if (!uploadFile) throwError(SCOPE, 'file to be removed not found')\n\n    const doRemove = (file: UploadFile) => {\n      abort(file)\n      removeFile(file)\n      props.onRemove(file, uploadFiles.value)\n      revokeFileObjectURL(file)\n    }\n\n    if (props.beforeRemove) {\n      const before = await props.beforeRemove(uploadFile, uploadFiles.value)\n      if (before !== false) doRemove(uploadFile)\n    } else {\n      doRemove(uploadFile)\n    }\n  }\n\n  function submit() {\n    uploadFiles.value\n      .filter(({ status }) => status === 'ready')\n      .forEach(({ raw }) => raw && uploadRef.value?.upload(raw))\n  }\n\n  watch(\n    () => props.listType,\n    (val) => {\n      if (val !== 'picture-card' && val !== 'picture') {\n        return\n      }\n\n      uploadFiles.value = uploadFiles.value.map((file) => {\n        const { raw, url } = file\n        if (!url && raw) {\n          try {\n            file.url = URL.createObjectURL(raw)\n          } catch (err: unknown) {\n            props.onError(err as Error, file, uploadFiles.value)\n          }\n        }\n        return file\n      })\n    }\n  )\n\n  watch(\n    uploadFiles,\n    (files) => {\n      for (const file of files) {\n        file.uid ||= genFileId()\n        file.status ||= 'success'\n      }\n    },\n    { immediate: true, deep: true }\n  )\n\n  return {\n    /** @description two-way binding ref from props `fileList` */\n    uploadFiles,\n    abort,\n    clearFiles,\n    handleError,\n    handleProgress,\n    handleStart,\n    handleSuccess,\n    handleRemove,\n    submit,\n    revokeFileObjectURL,\n  }\n}\n"], "names": ["useVModel", "isNil", "genFileId", "debugWarn", "throwError", "watch"], "mappings": ";;;;;;;;;;AAKA,MAAM,KAAK,GAAG,UAAU,CAAC;AACzB,MAAM,mBAAmB,GAAG,CAAC,IAAI,KAAK;AACtC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACjE,IAAI,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,GAAG;AACH,CAAC,CAAC;AACU,MAAC,WAAW,GAAG,CAAC,KAAK,EAAE,SAAS,KAAK;AACjD,EAAE,MAAM,WAAW,GAAGA,cAAS,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9E,EAAE,MAAM,OAAO,GAAG,CAAC,OAAO,KAAK,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;AAC1F,EAAE,SAAS,KAAK,CAAC,IAAI,EAAE;AACvB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;AAC1E,IAAI,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AACxF,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,KAAK,UAAU,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9F,GAAG;AACH,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,OAAO,KAAK;AACxC,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO;AACb,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACrB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAChD,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5C,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,OAAO,KAAK;AAC3C,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO;AACb,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;AAC9B,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC9C,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,QAAQ,EAAE,OAAO,KAAK;AAC/C,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AACvD,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK;AAChC,IAAI,IAAIC,mBAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AACvB,MAAM,IAAI,CAAC,GAAG,GAAGC,gBAAS,EAAE,CAAC;AAC7B,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,UAAU,EAAE,CAAC;AACnB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,GAAG,EAAE,IAAI;AACf,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG;AACnB,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,cAAc,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC3E,MAAM,IAAI;AACV,QAAQ,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AACnD,OAAO,CAAC,OAAO,GAAG,EAAE;AACpB,QAAQC,eAAS,CAAC,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AACtC,QAAQ,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1D,OAAO;AACP,KAAK;AACL,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AAC3D,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAClD,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,OAAO,IAAI,KAAK;AACvC,IAAI,MAAM,UAAU,GAAG,IAAI,YAAY,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACnE,IAAI,IAAI,CAAC,UAAU;AACnB,MAAMC,gBAAU,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;AACxD,IAAI,MAAM,QAAQ,GAAG,CAAC,KAAK,KAAK;AAChC,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC;AACnB,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC;AACxB,MAAM,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/C,MAAM,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,YAAY,EAAE;AAC5B,MAAM,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,YAAY,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AAC7E,MAAM,IAAI,MAAM,KAAK,KAAK;AAC1B,QAAQ,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC7B,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,SAAS,MAAM,GAAG;AACpB,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,MAAM,KAAK,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK;AACtF,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,GAAG,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/E,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAEC,SAAK,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK;AACvC,IAAI,IAAI,GAAG,KAAK,cAAc,IAAI,GAAG,KAAK,SAAS,EAAE;AACrD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AACxD,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAChC,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE;AACvB,QAAQ,IAAI;AACZ,UAAU,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC9C,SAAS,CAAC,OAAO,GAAG,EAAE;AACtB,UAAU,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;AACtD,SAAS;AACT,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAEA,SAAK,CAAC,WAAW,EAAE,CAAC,KAAK,KAAK;AAChC,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAGH,gBAAS,EAAE,CAAC,CAAC;AAC3C,MAAM,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACtC,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,MAAM;AACV,IAAI,mBAAmB;AACvB,GAAG,CAAC;AACJ;;;;"}