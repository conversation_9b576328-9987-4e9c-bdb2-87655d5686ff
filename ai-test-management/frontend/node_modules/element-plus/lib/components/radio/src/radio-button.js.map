{"version": 3, "file": "radio-button.js", "sources": ["../../../../../../packages/components/radio/src/radio-button.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { radioPropsBase } from './radio'\nimport type { ExtractPropTypes } from 'vue'\nimport type RadioButton from './radio-button.vue'\n\nexport const radioButtonProps = buildProps({\n  ...radioPropsBase,\n} as const)\n\nexport type RadioButtonProps = ExtractPropTypes<typeof radioButtonProps>\nexport type RadioButtonInstance = InstanceType<typeof RadioButton> & unknown\n"], "names": ["buildProps", "radioPropsBase"], "mappings": ";;;;;;;AAEY,MAAC,gBAAgB,GAAGA,kBAAU,CAAC;AAC3C,EAAE,GAAGC,oBAAc;AACnB,CAAC;;;;"}