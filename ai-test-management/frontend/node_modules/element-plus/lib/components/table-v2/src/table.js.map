{"version": 3, "file": "table.js", "sources": ["../../../../../../packages/components/table-v2/src/table.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport {\n  virtualizedGridProps,\n  virtualizedScrollbarProps,\n} from '@element-plus/components/virtual-list'\nimport {\n  classType,\n  columns,\n  dataType,\n  expandKeys,\n  fixedDataType,\n  requiredNumber,\n  rowKey,\n} from './common'\nimport { tableV2RowProps } from './row'\nimport { tableV2HeaderProps } from './header'\nimport { tableV2GridProps } from './grid'\n\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\nimport type { SortOrder } from './constants'\nimport type {\n  Column,\n  ColumnCommonParams,\n  DataGetter,\n  KeyType,\n  RowCommonParams,\n  SortBy,\n  SortState,\n} from './types'\n\n/**\n * Param types\n */\nexport type ColumnSortParams<T> = {\n  column: Column<T>\n  key: KeyType\n  order: SortOrder\n}\n\n/**\n * Renderer/Getter types\n */\n\nexport type ExtraCellPropGetter<T> = (\n  params: ColumnCommonParams<T> &\n    RowCommonParams & { cellData: T; rowData: any }\n) => any\n\nexport type ExtractHeaderPropGetter<T> = (params: {\n  columns: Column<T>[]\n  headerIndex: number\n}) => any\n\nexport type ExtractHeaderCellPropGetter<T> = (\n  params: ColumnCommonParams<T> & { headerIndex: number }\n) => any\n\nexport type ExtractRowPropGetter<T> = (\n  params: { columns: Column<T>[] } & RowCommonParams\n) => any\n\nexport type HeaderClassNameGetter<T> = (params: {\n  columns: Column<T>[]\n  headerIndex: number\n}) => string\n\nexport type RowClassNameGetter<T> = (\n  params: { columns: Column<T>[] } & RowCommonParams\n) => string\n\n/**\n * Handler types\n */\nexport type ColumnSortHandler<T> = (params: ColumnSortParams<T>) => void\nexport type ColumnResizeHandler<T> = (column: Column<T>, width: number) => void\nexport type ExpandedRowsChangeHandler = (expandedRowKeys: KeyType[]) => void\n\nexport const tableV2Props = buildProps({\n  cache: tableV2GridProps.cache,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n  rowKey,\n  // Header attributes\n  headerClass: {\n    type: definePropType<string | HeaderClassNameGetter<any>>([\n      String,\n      Function,\n    ]),\n  },\n  headerProps: {\n    type: definePropType<any | ExtractHeaderPropGetter<any>>([\n      Object,\n      Function,\n    ]),\n  },\n  headerCellProps: {\n    type: definePropType<any | ExtractHeaderCellPropGetter<any>>([\n      Object,\n      Function,\n    ]),\n  },\n  headerHeight: tableV2HeaderProps.headerHeight,\n  /**\n   * Footer attributes\n   */\n  footerHeight: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * Row attributes\n   */\n  rowClass: {\n    type: definePropType<string | RowClassNameGetter<any>>([String, Function]),\n  },\n  rowProps: {\n    type: definePropType<ExtractRowPropGetter<any> | any>([Object, Function]),\n  },\n  rowHeight: {\n    type: Number,\n    default: 50,\n  },\n\n  /**\n   * Cell attributes\n   */\n  cellProps: {\n    type: definePropType<Record<string, any> | ExtraCellPropGetter<any>>([\n      Object,\n      Function,\n    ]),\n  },\n  /**\n   * Data models\n   */\n  columns,\n  data: dataType,\n  dataGetter: {\n    type: definePropType<DataGetter<any>>(Function),\n  },\n  fixedData: fixedDataType,\n  /**\n   * Expanded keys\n   */\n  expandColumnKey: tableV2RowProps.expandColumnKey,\n  expandedRowKeys: expandKeys,\n  defaultExpandedRowKeys: expandKeys,\n\n  /**\n   * Attributes\n   */\n  class: classType,\n  // disabled: Boolean,\n  fixed: Boolean,\n  style: {\n    type: definePropType<CSSProperties>(Object),\n  },\n  width: requiredNumber,\n  height: requiredNumber,\n  maxHeight: Number,\n  useIsScrolling: Boolean,\n  indentSize: {\n    type: Number,\n    default: 12,\n  },\n  iconSize: {\n    type: Number,\n    default: 12,\n  },\n  hScrollbarSize: virtualizedGridProps.hScrollbarSize,\n  vScrollbarSize: virtualizedGridProps.vScrollbarSize,\n  scrollbarAlwaysOn: virtualizedScrollbarProps.alwaysOn,\n\n  /**\n   * Sorting\n   */\n  sortBy: {\n    type: definePropType<SortBy>(Object),\n    default: () => ({} as { key: KeyType; order: SortOrder }),\n  },\n  sortState: {\n    type: definePropType<SortState>(Object),\n    default: undefined,\n  },\n\n  /**\n   * Handlers\n   */\n  onColumnSort: {\n    type: definePropType<ColumnSortHandler<any>>(Function),\n  },\n  onExpandedRowsChange: {\n    type: definePropType<ExpandedRowsChangeHandler>(Function),\n  },\n  onEndReached: {\n    type: definePropType<(remainDistance: number) => void>(Function),\n  },\n  onRowExpand: tableV2RowProps.onRowExpand,\n  onScroll: tableV2GridProps.onScroll,\n  onRowsRendered: tableV2GridProps.onRowsRendered,\n  rowEventHandlers: tableV2RowProps.rowEventHandlers,\n} as const)\n\nexport type TableV2Props = ExtractPropTypes<typeof tableV2Props>\n"], "names": ["buildProps", "tableV2GridProps", "tableV2RowProps", "<PERSON><PERSON><PERSON>", "definePropType", "tableV2HeaderProps", "columns", "dataType", "fixedDataType", "expandKeys", "classType", "requiredNumber", "virtualizedGridProps", "virtualizedScrollbarProps"], "mappings": ";;;;;;;;;;;AAiBY,MAAC,YAAY,GAAGA,kBAAU,CAAC;AACvC,EAAE,KAAK,EAAEC,qBAAgB,CAAC,KAAK;AAC/B,EAAE,kBAAkB,EAAEC,mBAAe,CAAC,kBAAkB;AACxD,UAAEC,aAAM;AACR,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEC,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEA,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEA,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,GAAG;AACH,EAAE,YAAY,EAAEC,yBAAkB,CAAC,YAAY;AAC/C,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEA,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,GAAG;AACH,WAAEE,cAAO;AACT,EAAE,IAAI,EAAEC,eAAQ;AAChB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEH,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,SAAS,EAAEI,oBAAa;AAC1B,EAAE,eAAe,EAAEN,mBAAe,CAAC,eAAe;AAClD,EAAE,eAAe,EAAEO,iBAAU;AAC7B,EAAE,sBAAsB,EAAEA,iBAAU;AACpC,EAAE,KAAK,EAAEC,gBAAS;AAClB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEN,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,KAAK,EAAEO,qBAAc;AACvB,EAAE,MAAM,EAAEA,qBAAc;AACxB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,cAAc,EAAE,OAAO;AACzB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,cAAc,EAAEC,0BAAoB,CAAC,cAAc;AACrD,EAAE,cAAc,EAAEA,0BAAoB,CAAC,cAAc;AACrD,EAAE,iBAAiB,EAAEC,+BAAyB,CAAC,QAAQ;AACvD,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAET,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,oBAAoB,EAAE;AACxB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,WAAW,EAAEF,mBAAe,CAAC,WAAW;AAC1C,EAAE,QAAQ,EAAED,qBAAgB,CAAC,QAAQ;AACrC,EAAE,cAAc,EAAEA,qBAAgB,CAAC,cAAc;AACjD,EAAE,gBAAgB,EAAEC,mBAAe,CAAC,gBAAgB;AACpD,CAAC;;;;"}