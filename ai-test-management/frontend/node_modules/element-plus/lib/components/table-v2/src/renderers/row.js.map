{"version": 3, "file": "row.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/row.tsx"], "sourcesContent": ["import { Row } from '../components'\nimport { tryCall } from '../utils'\n\nimport type {\n  ComponentInternalInstance,\n  FunctionalComponent,\n  UnwrapNestedRefs,\n} from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { UseTableReturn } from '../use-table'\nimport type { TableV2Props } from '../table'\nimport type { TableGridRowSlotParams } from '../table-grid'\n\ntype RowRendererProps = TableGridRowSlotParams &\n  Pick<\n    TableV2Props,\n    | 'expandColumnKey'\n    | 'estimatedRowHeight'\n    | 'rowProps'\n    | 'rowClass'\n    | 'rowKey'\n    | 'rowEventHandlers'\n  > &\n  UnwrapNestedRefs<\n    Pick<\n      UseTableReturn,\n      | 'depthMap'\n      | 'expandedRowKeys'\n      | 'hasFixedColumns'\n      | 'onRowHovered'\n      | 'onRowExpanded'\n      | 'columnsStyles'\n    >\n  > & {\n    ns: UseNamespaceReturn\n    tableInstance?: ComponentInternalInstance\n  }\n\nconst RowRenderer: FunctionalComponent<RowRendererProps> = (\n  props,\n  { slots }\n) => {\n  const {\n    columns,\n    columnsStyles,\n    depthMap,\n    expandColumnKey,\n    expandedRowKeys,\n    estimatedRowHeight,\n    hasFixedColumns,\n    rowData,\n    rowIndex,\n    style,\n    isScrolling,\n    rowProps,\n    rowClass,\n    rowKey,\n    rowEventHandlers,\n    ns,\n    onRowHovered,\n    onRowExpanded,\n  } = props\n\n  const rowKls = tryCall(rowClass, { columns, rowData, rowIndex }, '')\n  const additionalProps = tryCall(rowProps, {\n    columns,\n    rowData,\n    rowIndex,\n  })\n  const _rowKey = rowData[rowKey]\n  const depth = depthMap[_rowKey] || 0\n  const canExpand = Boolean(expandColumnKey)\n  const isFixedRow = rowIndex < 0\n  const kls = [\n    ns.e('row'),\n    rowKls,\n    {\n      [ns.e(`row-depth-${depth}`)]: canExpand && rowIndex >= 0,\n      [ns.is('expanded')]: canExpand && expandedRowKeys.includes(_rowKey),\n      [ns.is('fixed')]: !depth && isFixedRow,\n      [ns.is('customized')]: Boolean(slots.row),\n    },\n  ]\n\n  const onRowHover = hasFixedColumns ? onRowHovered : undefined\n\n  const _rowProps = {\n    ...additionalProps,\n    columns,\n    columnsStyles,\n    class: kls,\n    depth,\n    expandColumnKey,\n    estimatedRowHeight: isFixedRow ? undefined : estimatedRowHeight,\n    isScrolling,\n    rowIndex,\n    rowData,\n    rowKey: _rowKey,\n    rowEventHandlers,\n    style,\n  }\n\n  const handlerMosueEnter = (e: MouseEvent) => {\n    onRowHover?.({\n      hovered: true,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex,\n    })\n  }\n\n  const handlerMouseLeave = (e: MouseEvent) => {\n    onRowHover?.({\n      hovered: false,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex,\n    })\n  }\n\n  return (\n    <Row\n      {..._rowProps}\n      onRowExpand={onRowExpanded}\n      onMouseenter={handlerMosueEnter}\n      onMouseleave={handlerMouseLeave}\n      rowkey={_rowKey}\n    >\n      {slots}\n    </Row>\n  )\n}\n\nexport default RowRenderer\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "slots", "columns", "columnsStyles", "depthMap", "expandColumnKey", "expandedRowKeys", "estimatedRowHeight", "hasFixedColumns", "rowData", "rowIndex", "style", "isScrolling", "rowProps", "rowClass", "<PERSON><PERSON><PERSON>", "rowEventHandlers", "ns", "onRowExpanded", "_row<PERSON>ey", "depth", "canExpand", "Boolean", "isFixedRow", "kls", "e", "is", "class", "handler<PERSON><PERSON><PERSON><PERSON><PERSON>", "onRowHover", "hovered", "event", "handlerMouseLeave"], "mappings": ";;;;;;;;;;;;;AAsCA,CAAA,KAAMA;AAEFC,EAAAA,MAAAA;AAAF,IACG,OAAA;IACG,aAAA;IACJC,QADI;IAEJC,eAFI;IAGJC,eAHI;IAIJC,kBAJI;IAKJC,eALI;IAMJC,OANI;IAOJC,QAPI;IAQJC,KARI;IASJC,WATI;IAUJC,QAVI;IAWJC,QAXI;IAYJC,MAZI;IAaJC,gBAbI;IAcJC,EAdI;IAeJC,YAfI;IAgBJC,aAhBI;MAAA,KAAA,CAAA;AAkBJC,EAAAA,MAAAA,MAAAA,GAAAA,aAAAA,CAAAA,QAAAA,EAAAA;AAlBI,IAAA,OAAN;AAqBA,IAAA,OAAY;IAAuBhB,QAAF;KAAA,EAAA,CAAA,CAAA;AAAoBQ,EAAAA,MAAAA,eAAAA,GAAAA,aAAAA,CAAAA,QAAAA,EAAAA;IAA/B,OAAtB;AACA,IAAA,OAAqB;IACnBR,QADwC;IAExCO,CAFwC;AAGxCC,EAAAA,MAAAA,OAAAA,GAAAA,OAAAA,CAAAA,MAAAA,CAAAA,CAAAA;AAHwC,EAAA,MAA1C,KAAA,GAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAKA,EAAA,MAAMS,SAAO,GAAU,uBAAvB,CAAA,CAAA;AACA,EAAA,MAAMC,UAAQhB,GAAAA,QAAQ,GAAA,CAAR;AACd,EAAA,MAAMiB,GAAS,GAAA,CAAA,EAAA,CAAA,CAAA,CAAGC,KAAO,CAAA,EAACjB;AAC1B,IAAA,CAAA,EAAA,CAAMkB,CAAU,CAAA,CAAA,UAAGb,EAAQ,KAAA,CAAA,CAAA,CAAA,GAA3B,SAAA,IAAA,QAAA,IAAA,CAAA;IACMc,CAAAA,EAAAA,CAAAA,EAAAA,CAAG,UACP,CAAA,GAAA,SADU,IAGV,eAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AACE,IAAA,CAACP,EAAE,CAACQ,EAAG,CAAYL,OAAAA,CAAAA,GAAAA,CAAAA,KAAM,IAAKC,UAAAA;AAC9B,IAAA,CAACJ,EAAE,CAACS,EAAH,CAAM,YAAP,CAAA,GAA8B,OAAA,CAAA,KAAmB,CAAA,GAAA,CAAA;IACjD,CAACT;QACGS,UAAG,GAAA,eAA6B,GAAA,YAAN,GAAA,KAAA,CAAA,CAAA;AAJhC,EAAA,MAHF,SAAA,GAAA;AAWA,IAAA,GAAA,eAAmBlB;AAEnB,IAAA,OAAe;IAEbN,aAFgB;IAGhBC,KAHgB,EAAA,GAAA;AAIhBwB,IAAAA,KAAK;IACLP,eALgB;IAMhBf,kBANgB,EAAA,UAAA,GAAA,KAAA,CAAA,GAAA,kBAAA;AAOhBE,IAAAA,WAAAA;IACAK,QARgB;IAShBF,OATgB;IAUhBD,MAVgB,EAAA,OAAA;AAWhBM,IAAAA,gBAXgB;IAYhBC,KAZgB;AAahBL,GAAAA,CAAAA;EAbgB,MAAlB,iBAAA,GAAA,CAAA,CAAA,KAAA;;MAgBMiB,OAAAA,EAAAA,IAAAA;AACJC,MAAAA,MAAAA,EAAU,OAAG;AACXC,MAAAA,KAAAA,EAAO;AACPf,MAAAA;AACAgB,MAAAA,QAHW;MAIXtB,CAJW;AAKXC,GAAAA,CAAAA;AALW,EAAA,MAAb,iBAAA,GAAA,CAAA,CAAA,KAAA;IADF,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA;;MAUMsB,MAAAA,EAAAA,OAAAA;AACJH,MAAAA,KAAAA,EAAAA,CAAU;AACRC,MAAAA,OAAO;AACPf,MAAAA,QAAQI;AACRY,KAAAA,CAAAA,CAAAA;;AAEArB,EAAAA,OAAAA,eAAAA,CAAAA,cAAAA,EAAAA,cAAAA,CAAAA,SAAAA,EAAAA;AALW,IAAA,aAAb,EAAA,aAAA;IADF,cAAA,EAAA,iBAAA;;AAUA,IAAA,QAAA,EAAA,OAAA;AAAA,GAAA,CAAA,EAAA,OAAA,CAAA,KAGiBQ,CAHjB,GAAA,KAAA,GAAA;AAAA,IAAA,OAAA,EAAA,MAAA,CAIkBU,KAJlB,CAAA;AAAA,GAAA,CAAA,CAAA;;UAQK3B,WARL;;;;"}