{"version": 3, "file": "auto-resizer.js", "sources": ["../../../../../../../packages/components/table-v2/src/components/auto-resizer.tsx"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { autoResizerProps } from '../auto-resizer'\nimport { useAutoResize } from '../composables'\n\nconst AutoResizer = defineComponent({\n  name: 'ElA<PERSON>Resizer',\n  props: autoResizerProps,\n  setup(props, { slots }) {\n    const ns = useNamespace('auto-resizer')\n    const { height, width, sizer } = useAutoResize(props)\n    const style = {\n      width: '100%',\n      height: '100%',\n    }\n\n    return () => {\n      return (\n        <div ref={sizer} class={ns.b()} style={style}>\n          {slots.default?.({\n            height: height.value,\n            width: width.value,\n          })}\n        </div>\n      )\n    }\n  },\n})\n\nexport default AutoResizer\n"], "names": ["AutoResizer", "defineComponent", "name", "props", "autoResizerProps", "slots", "useNamespace", "ns", "height", "width", "sizer", "useAutoResize", "_createVNode", "b", "style", "default", "value"], "mappings": ";;;;;;;;;AAKA,MAAMA,WAAW,GAAGC,mBAAe,CAAC;AAClCC,EAAAA,IAAI,EAAE,eAD4B;AAElCC,EAAAA,KAAK,EAAEC,4BAF2B;;IAG7B;AAAUC,GAAAA,EAAAA;AAAF,IAAW,MAAA,EAAA,GAAAC,kBAAA,CAAA,cAAA,CAAA,CAAA;AACtB,IAAA,MAAMC;MACA,MAAA;MAAEC,KAAF;MAAUC,KAAV;AAAiBC,KAAAA,GAAAA,2BAAAA,CAAAA,KAAAA,CAAAA,CAAAA;IAAjB,MAA2BC,KAAAA,GAAAA;AACjC,MAAA,aAAc;AACZF,MAAAA,QADY,MAAA;AAEZD,KAAAA,CAAAA;IAFY,OAAd,MAAA;AAKA,MAAA,IAAA,EAAa,CAAA;AACX,MAAA,OAAAI,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EACYF,KADZ;QAAA,OAC0BH,EAAAA,EAAE,CAACM,CAAH,EAD1B;QAAA,OACyCC,EAAAA,KAAAA;OACpCT,EAAAA,CAAAA,CAAAA,EAAAA,GAAMU,KAAAA,CAAAA,OAAU,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA;QACfP,MAAM,EAAEA,MAAM,CAACQ,KADA;QAEfP,KAAK,EAAEA,KAAK,CAACO,KAAAA;AAFE,OAAhB,CAFL,CAAA,CAAA,CAAA;KADF,CAAA;AAUD,GAAA;;AArBiC,oBAApC,WAAA;;;;"}