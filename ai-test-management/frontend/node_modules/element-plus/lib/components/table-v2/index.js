'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tableV2 = require('./src/table-v2.js');
var autoResizer$1 = require('./src/components/auto-resizer.js');
var constants = require('./src/constants.js');
var autoResizer = require('./src/auto-resizer.js');
var _private = require('./src/private.js');
var table = require('./src/table.js');
var row = require('./src/row.js');
var install = require('../../utils/vue/install.js');

const ElTableV2 = install.withInstall(tableV2["default"]);
const ElAutoResizer = install.withInstall(autoResizer$1["default"]);

exports.TableV2 = tableV2["default"];
exports.TableV2Alignment = constants.Alignment;
exports.TableV2FixedDir = constants.FixedDir;
exports.TableV2SortOrder = constants.SortOrder;
exports.autoResizerProps = autoResizer.autoResizerProps;
exports.TableV2Placeholder = _private.placeholderSign;
exports.tableV2Props = table.tableV2Props;
exports.tableV2RowProps = row.tableV2RowProps;
exports.ElAutoResizer = ElAutoResizer;
exports.ElTableV2 = ElTableV2;
//# sourceMappingURL=index.js.map
