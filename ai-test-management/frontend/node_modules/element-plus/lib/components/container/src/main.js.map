{"version": 3, "file": "main.js", "sources": ["../../../../../../packages/components/container/src/main.vue"], "sourcesContent": ["<template>\n  <main :class=\"ns.b()\">\n    <slot />\n  </main>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\n\ndefineOptions({\n  name: 'El<PERSON>ain',\n})\n\nconst ns = useNamespace('main')\n</script>\n"], "names": ["useNamespace"], "mappings": ";;;;;;;;uCASc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;AAEA,IAAM,MAAA,EAAA,GAAKA,mBAAa,MAAM,CAAA,CAAA;;;;;;;;;;;;;;"}