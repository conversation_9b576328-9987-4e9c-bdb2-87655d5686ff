{"version": 3, "file": "node-content.js", "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.ts"], "sourcesContent": ["// @ts-nocheck\nimport { defineComponent, h } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nexport default defineComponent({\n  name: 'NodeContent',\n  setup() {\n    const ns = useNamespace('cascader-node')\n    return {\n      ns,\n    }\n  },\n  render() {\n    const { ns } = this\n    const { node, panel } = this.$parent\n    const { data, label } = node\n    const { renderLabelFn } = panel\n    return h(\n      'span',\n      { class: ns.e('label') },\n      renderLabelFn ? renderLabelFn({ node, data }) : label\n    )\n  },\n})\n"], "names": ["defineComponent", "useNamespace", "h"], "mappings": ";;;;;;;AAEA,kBAAeA,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,KAAK,GAAG;AACV,IAAI,MAAM,EAAE,GAAGC,kBAAY,CAAC,eAAe,CAAC,CAAC;AAC7C,IAAI,OAAO;AACX,MAAM,EAAE;AACR,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACxB,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AACjC,IAAI,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACpC,IAAI,OAAOC,KAAC,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,aAAa,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;AACtG,GAAG;AACH,CAAC,CAAC;;;;"}