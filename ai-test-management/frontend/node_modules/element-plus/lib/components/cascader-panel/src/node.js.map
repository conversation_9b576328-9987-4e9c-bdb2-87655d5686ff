{"version": 3, "file": "node.js", "sources": ["../../../../../../packages/components/cascader-panel/src/node.vue"], "sourcesContent": ["<template>\n  <li\n    :id=\"`${menuId}-${node.uid}`\"\n    role=\"menuitem\"\n    :aria-haspopup=\"!isLeaf\"\n    :aria-owns=\"isLeaf ? undefined : menuId\"\n    :aria-expanded=\"inExpandingPath\"\n    :tabindex=\"expandable ? -1 : undefined\"\n    :class=\"[\n      ns.b(),\n      ns.is('selectable', checkStrictly),\n      ns.is('active', node.checked),\n      ns.is('disabled', !expandable),\n      inExpandingPath && 'in-active-path',\n      inCheckedPath && 'in-checked-path',\n    ]\"\n    @mouseenter=\"handleHoverExpand\"\n    @focus=\"handleHoverExpand\"\n    @click=\"handleClick\"\n  >\n    <!-- prefix -->\n    <el-checkbox\n      v-if=\"multiple\"\n      :model-value=\"node.checked\"\n      :indeterminate=\"node.indeterminate\"\n      :disabled=\"isDisabled\"\n      @click.stop\n      @update:model-value=\"handleSelectCheck\"\n    />\n    <el-radio\n      v-else-if=\"checkStrictly\"\n      :model-value=\"checkedNodeId\"\n      :label=\"node.uid\"\n      :disabled=\"isDisabled\"\n      @update:model-value=\"handleSelectCheck\"\n      @click.stop\n    >\n      <!--\n        Add an empty element to avoid render label,\n        do not use empty fragment here for https://github.com/vuejs/vue-next/pull/2485\n      -->\n      <span />\n    </el-radio>\n    <el-icon v-else-if=\"isLeaf && node.checked\" :class=\"ns.e('prefix')\">\n      <check />\n    </el-icon>\n\n    <!-- content -->\n    <node-content />\n\n    <!-- postfix -->\n    <template v-if=\"!isLeaf\">\n      <el-icon v-if=\"node.loading\" :class=\"[ns.is('loading'), ns.e('postfix')]\">\n        <loading />\n      </el-icon>\n      <el-icon v-else :class=\"['arrow-right', ns.e('postfix')]\">\n        <arrow-right />\n      </el-icon>\n    </template>\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, inject } from 'vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport ElRadio from '@element-plus/components/radio'\nimport ElIcon from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ArrowRight, Check, Loading } from '@element-plus/icons-vue'\nimport NodeContent from './node-content'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\nimport type { default as CascaderNode } from './node'\nimport type { PropType } from 'vue'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\n\nexport default defineComponent({\n  name: 'ElCascaderNode',\n\n  components: {\n    ElCheckbox,\n    ElRadio,\n    NodeContent,\n    ElIcon,\n    Check,\n    Loading,\n    ArrowRight,\n  },\n\n  props: {\n    node: {\n      type: Object as PropType<CascaderNode>,\n      required: true,\n    },\n    menuId: String,\n  },\n\n  emits: ['expand'],\n\n  setup(props, { emit }) {\n    const panel = inject(CASCADER_PANEL_INJECTION_KEY)!\n\n    const ns = useNamespace('cascader-node')\n    const isHoverMenu = computed(() => panel.isHoverMenu)\n    const multiple = computed(() => panel.config.multiple)\n    const checkStrictly = computed(() => panel.config.checkStrictly)\n    const checkedNodeId = computed(() => panel.checkedNodes[0]?.uid)\n    const isDisabled = computed(() => props.node.isDisabled)\n    const isLeaf = computed(() => props.node.isLeaf)\n    const expandable = computed(\n      () => (checkStrictly.value && !isLeaf.value) || !isDisabled.value\n    )\n    const inExpandingPath = computed(() => isInPath(panel.expandingNode!))\n    // only useful in check-strictly mode\n    const inCheckedPath = computed(\n      () => checkStrictly.value && panel.checkedNodes.some(isInPath)\n    )\n\n    const isInPath = (node: CascaderNode) => {\n      const { level, uid } = props.node\n      return node?.pathNodes[level - 1]?.uid === uid\n    }\n\n    const doExpand = () => {\n      if (inExpandingPath.value) return\n      panel.expandNode(props.node)\n    }\n\n    const doCheck = (checked: boolean) => {\n      const { node } = props\n      if (checked === node.checked) return\n      panel.handleCheckChange(node, checked)\n    }\n\n    const doLoad = () => {\n      panel.lazyLoad(props.node, () => {\n        if (!isLeaf.value) doExpand()\n      })\n    }\n\n    const handleHoverExpand = (e: Event) => {\n      if (!isHoverMenu.value) return\n      handleExpand()\n      !isLeaf.value && emit('expand', e)\n    }\n\n    const handleExpand = () => {\n      const { node } = props\n      // do not exclude leaf node because the menus expanded might have to reset\n      if (!expandable.value || node.loading) return\n      node.loaded ? doExpand() : doLoad()\n    }\n\n    const handleClick = () => {\n      if (isHoverMenu.value && !isLeaf.value) return\n\n      if (\n        isLeaf.value &&\n        !isDisabled.value &&\n        !checkStrictly.value &&\n        !multiple.value\n      ) {\n        handleCheck(true)\n      } else {\n        handleExpand()\n      }\n    }\n\n    const handleSelectCheck = (checked: CheckboxValueType | undefined) => {\n      if (checkStrictly.value) {\n        doCheck(checked as boolean)\n        if (props.node.loaded) {\n          doExpand()\n        }\n      } else {\n        handleCheck(checked as boolean)\n      }\n    }\n\n    const handleCheck = (checked: boolean) => {\n      if (!props.node.loaded) {\n        doLoad()\n      } else {\n        doCheck(checked)\n        !checkStrictly.value && doExpand()\n      }\n    }\n\n    return {\n      panel,\n      isHoverMenu,\n      multiple,\n      checkStrictly,\n      checkedNodeId,\n      isDisabled,\n      isLeaf,\n      expandable,\n      inExpandingPath,\n      inCheckedPath,\n      ns,\n      handleHoverExpand,\n      handleExpand,\n      handleClick,\n      handleCheck,\n      handleSelectCheck,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "ElCheckbox", "ElRadio", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElIcon", "Check", "Loading", "ArrowRight", "inject", "CASCADER_PANEL_INJECTION_KEY", "useNamespace", "computed", "_resolveComponent", "_openBlock", "_normalizeClass", "_createCommentVNode", "_createBlock", "_withModifiers", "_createElementVNode", "_createVNode", "_Fragment", "_withCtx"], "mappings": ";;;;;;;;;;;;;;AA2EA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,gBAAA;AAAA,EAEN,UAAY,EAAA;AAAA,gBACVC,gBAAA;AAAA,aACAC,eAAA;AAAA,iBACAC,sBAAA;AAAA,YACAC,cAAA;AAAA,WACAC,cAAA;AAAA,aACAC,gBAAA;AAAA,gBACAC,mBAAA;AAAA,GACF;AAAA,EAEA,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,MAAQ,EAAA,MAAA;AAAA,GACV;AAAA,EAEA,KAAA,EAAO,CAAC,QAAQ,CAAA;AAAA,EAEhB,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAM,MAAA,KAAA,GAAQC,WAAOC,kCAA4B,CAAA,CAAA;AAEjD,IAAM,MAAA,EAAA,GAAKC,qBAAa,eAAe,CAAA,CAAA;AACvC,IAAA,MAAM,WAAc,GAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,WAAW,CAAA,CAAA;AACpD,IAAA,MAAM,QAAW,GAAAA,YAAA,CAAS,MAAM,KAAA,CAAM,OAAO,QAAQ,CAAA,CAAA;AACrD,IAAA,MAAM,aAAgB,GAAAA,YAAA,CAAS,MAAM,KAAA,CAAM,OAAO,aAAa,CAAA,CAAA;AAC/D,IAAA,MAAM,gBAAgBA,YAAS,CAAA,MAAM;AACrC,MAAA,IAAM,EAAa,CAAA;AACnB,MAAA,OAAe,CAAA,EAAA,GAAA,KAAA,CAAA,YAAe,CAAA,CAAA,CAAM,KAAK,IAAM,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA;AAC/C,KAAA,CAAA,CAAA;AAAmB,IAAA,gBACI,GAAAA,YAAA,CAAA,MAAiB,KAAA,CAAA,IAAA,CAAA,UAAsB,CAAA,CAAA;AAAA,IAC9D,MAAA,MAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;AACA,IAAA,MAAM,yBAA2B,CAAA,MAAA,aAAe,CAAA,KAAA,iBAAqB,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAErE,IAAA,MAAM,eAAgB,GAAAA,YAAA,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,CAAA;AAAA,IAAA,mBACA,GAAAA,YAAA,CAAS,MAAM,aAAa,MAAa,IAAA,KAAA,CAAA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IAC/D,MAAA,QAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAA,MAAM,EAAE,KAAA,EAAO,GAAI,EAAA,GAAI,KAAM,CAAA,IAAA,CAAA;AAC7B,MAAA,OAAO,CAAM,CAAA,EAAA,GAAA,IAAA,IAAA,IAAkB,GAAA,MAAI,GAAQ,IAAA,CAAA,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAA,GAAA,CAAA;AAAA,KAC7C,CAAA;AAEA,IAAA,MAAM,WAAW,MAAM;AACrB,MAAA,IAAI,gBAAgB,KAAO;AAC3B,QAAM,OAAA;AAAqB,MAC7B,KAAA,CAAA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAM,MAAA,UAAW,CAAA,OAAA,KAAA;AACjB,MAAI,MAAA,EAAA,IAAA,EAAA,QAA0B,CAAA;AAC9B,MAAM,IAAA,OAAA,KAAA,IAAA,CAAA;AAA+B,QACvC,OAAA;AAEA,MAAA,uBAAqB,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AACnB,KAAM,CAAA;AACJ,IAAI,MAAA,MAAQ,GAAA,MAAA;AAAgB,MAC9B,KAAC,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA;AAAA,QACH,IAAA,CAAA,MAAA,CAAA,KAAA;AAEA,UAAM,QAAA,EAAA,CAAA;AACJ,OAAI,CAAA,CAAA;AACJ,KAAa,CAAA;AACb,IAAA,MAAQ,iBAAc,GAAA,CAAA,CAAA,KAAA;AAAW,MACnC,IAAA,CAAA,WAAA,CAAA,KAAA;AAEA,QAAA;AACE,MAAM,cAAW,CAAA;AAEjB,MAAA,CAAA,MAAK,CAAA,KAAA,IAAoB,IAAA,CAAA,QAAK,EAAS,CAAA,CAAA,CAAA;AACvC,KAAK,CAAA;AAA6B,IACpC,MAAA,YAAA,GAAA,MAAA;AAEA,MAAA,sBAA0B,CAAA;AACxB,MAAA,IAAI,CAAY,UAAA,CAAA,KAAA,IAAS,IAAC,CAAA,OAAc;AAExC,QACE,OAAA;AAKA,MAAA,IAAA,CAAA,MAAA,GAAY,QAAI,EAAA,GAAA,MAAA,EAAA,CAAA;AAAA,KAAA,CAClB;AACE,IAAa,MAAA,WAAA,GAAA,MAAA;AAAA,MACf,IAAA,WAAA,CAAA,KAAA,IAAA,CAAA,MAAA,CAAA,KAAA;AAAA,QACF,OAAA;AAEA,MAAM,IAAA,MAAA,CAAA,KAAA,IAAA,CAAA,UAAgE,CAAA,KAAA,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,EAAA;AACpE,QAAA,gBAAkB,CAAO,CAAA;AACvB,OAAA,MAAA;AACA,QAAI,eAAW;AACb,OAAS;AAAA,KACX,CAAA;AAAA,IAAA,MACK,iBAAA,GAAA,CAAA,OAAA,KAAA;AACL,MAAA,IAAA,aAA8B,CAAA,KAAA,EAAA;AAAA,QAChC,OAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACF,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AAEA,UAAM,QAAA,EAAA,CAAA;AACJ,SAAI;AACF,OAAO,MAAA;AAAA,QACF,WAAA,CAAA,OAAA,CAAA,CAAA;AACL,OAAA;AACA,KAAC,CAAA;AAAgC,IACnC,MAAA,WAAA,GAAA,CAAA,OAAA,KAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AAEA,QAAO,MAAA,EAAA,CAAA;AAAA,OACL,MAAA;AAAA,QACA,OAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACA,CAAA,aAAA,CAAA,KAAA,IAAA,QAAA,EAAA,CAAA;AAAA,OACA;AAAA,KACA,CAAA;AAAA,IACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,UAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,eAAA;AAAA,MACA,aAAA;AAAA,MACF,EAAA;AAAA,MACF,iBAAA;AACF,MAAC,YAAA;;;;;;;;;2BAnJM,GAAAC,oBAAA,CAAA,UAAA,CAAA,CAAA;AAAA,EAAA,MAzDA,gBAAW,uBAAY,CAAA,OAAA,CAAA,CAAA;AAAA,EAAA,MACrB,kBAAA,GAAAA,oBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,EAAA,6BACY,GAAAA,oBAAA,CAAA,cAAA,CAAA,CAAA;AAAA,EAChB,MAAA,yCAAgC,CAAA,SAAA,CAAA,CAAA;AAAA,EAAA,MACjB,sBAAA,GAAAA,oBAAA,CAAA,aAAA,CAAA,CAAA;AAAA,EACf,OAAAC,uCAA4B,CAAA,IAAA,EAAA;AAAA,IAC5B,EAAK,EAAA,CAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAAA,gBAAc;AAAA,IAAU,eAAK,EAAA,CAAA,IAAA,CAAA,MAA4B;AAAA,IAAA,WAAY,EAAE,IAAW,CAAA,MAAA,GAAA,KAAA,CAAK,GAAO,IAAA,CAAA,MAAA;AAAA,IAAS,eAAK,EAAA,IAAA,CAAA,eAAwB;AAAA,IAAA,QAAwB,EAAA,IAAA,CAAA,UAAA,GAAA,CAAA,CAAA,GAAA,KAAA,CAAA;AAAA,IAAA,KAAyC,EAAAC,kBAAA,CAAA;AAAA,MAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA;MAQ9L,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,IAAA,CAAA,aAAA,CAAA;AAAA,MACL,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,QAAA,EAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,MAAA,IAAA,CAAA,eAAA,IAAA,gBAAA;AAER,MAAA,IAAA,CAAA,aAAA,IAAA,iBAAA;AAAA;AAQE,IAAA,YAAA,EAAA,IAAA,CAAA,iBAAA;AALC,IAAA,OAAA,EAAA,IAAA,CAAA,iBAAkB;AAAA,IAAA,yBACE;AAAA,GAAA,EAAA;AACV,IAAAC,sBACX,CAAA,UAAA,CAAA;AAAA,IAAW,IAAA,CAAA,QAAA,IAAAF,aAAA,EAAA,EAAAG,eAAA,CAAA,sBAAA,EAAA;AAAA,MACV,GAAoB,EAAA,CAAA;AAAA,MAAA,aAAA,EAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AAeZ,MAAA,aAAA,EAAA,IAAA,CAAA,IAAA,CAAA,aAAA;MAXR,QAAa,EAAA,IAAA,CAAA,UAAA;AAAA,MACb,OAAO,EAAKC,iBAAA,CAAA,MAAA;AAAA,OACF,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,MACV,qBAAoB,EAAA,IAAA,CAAA,iBAAA;AAAA,KAAA,WACrB,CAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,SAAA,EAAA,qBAAA,CAAA,CAAA,IAAA,IAAA,CAAA,aAAA,IAAAJ,aAAA,EAAA,EAAAG,eAAA,CAAA,mBAAA,EAAA;AAAA,MAAW,GAAA,EAAA,CAAA;AAAA,MAAA,aAAA,EAAA,IAAA,CAAA,aAAA;0BAKR;AAAA,MAHH,QAAA,EAAA,IAAA,CAAA,UAAA;AAAA,MAAA,qBAIQ,EAAA,IAAA,CAAA,iBAAA;AAAA,MAAA,OAAA,EAAAC,iBAAA,CAAA,MAAA;;;;AAIA,QAAAF,sBAAA,CAAA,uJAAA,CAAA;QAFwCG,sBAAA,CAAA;AAAM,OAAA,CAAA;;AAC7C,KAAA,EAAA,CAAT,EAAS,CAAA,aAAA,EAAA,OAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,SAAA,CAAA,CAAA,IAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,IAAA,CAAA,OAAA,IAAAL,aAAA,EAAA,EAAAG,eAAA,CAAA,kBAAA,EAAA;AAAA,MAAA,GAAA,EAAA,CAAA;;;;AAGX,QAAAG,eAAA,CAAA,gBAAA,CAAA;AAAA,OACgB,CAAA;AAAA,MAEhB,CAAA,EAAA,CAAA;AAAA,KACiB,EAAjB,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAJ,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,IAOWA,sBAAA,CAAA,WAAA,CAAA;AAAA,IAAAI,eAAA,CAAA,uBAAA,CAAA;AAAA,IAAAJ,sBAAA,CAAA,WAAA,CAAA;AAAA,IANM,CAAA,IAAA,CAAA,MAAA,2CAEL,CAAAK,YAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA;AAAA,MAAA,IAAA,CAAA,IAAA,CAAA,OAAA,IAAAP,aAAA,EAAA,EAAAG,eAAA,CAAA,kBAAA,EAAA;AAFoB,QAAA,GAAA,EAAA,CAAA;AAA8B,QAAA,KAAA,EAAAF,kBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;AAC/C,QAAA,OAAA,EAAAO,WAAA,CAAA,MAAA;AAAA,UAAAF,eAAA,CAAA,kBAAA,CAAA;;;2DAIH,CAAA,kBAAA,EAAA;AAAA,QAAA,GAAA,EAAA,CAAA;aAFY,EAAAL,kBAAA,CAAA,CAAA,aAAA,EAAA,UAAqB,SAAC,CAAA,CAAA,CAAA;AAAA,OAAA,EAAA;mCAC3B;AAAA,UAAAK,eAAA,CAAA,sBAAA,CAAA;AAAA,SAAA,CAAA;;;;;;;;;;"}