{"version": 3, "file": "use-panel-date-range.js", "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-panel-date-range.ts"], "sourcesContent": ["import { computed, inject, nextTick, ref } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport { getValidDateOfMonth, getValidDateOfYear } from '../utils'\n\nimport type { PanelDateRangeProps } from '../props/panel-date-range'\nimport type { Dayjs } from 'dayjs'\nimport type { ComputedRef, Ref } from 'vue'\n\ntype CurrentView = 'date' | 'year' | 'month'\ntype CurrentViewRef = { focus: () => void }\n\nexport type Emits = (\n  event: 'pick' | 'set-picker-option' | 'calendar-change' | 'panel-change',\n  ...args: any[]\n) => void\n\nexport const usePanelDateRange = (\n  props: PanelDateRangeProps,\n  emit: Emits,\n  leftDate: Ref<Dayjs>,\n  rightDate: Ref<Dayjs>\n) => {\n  const leftCurrentView = ref<CurrentView>('date')\n  const leftCurrentViewRef = ref<CurrentViewRef>()\n  const rightCurrentView = ref<CurrentView>('date')\n  const rightCurrentViewRef = ref<CurrentViewRef>()\n  const pickerBase = inject('EP_PICKER_BASE') as any\n  const { disabledDate } = pickerBase.props\n  const { t, lang } = useLocale()\n\n  const leftYear = computed(() => {\n    return leftDate.value.year()\n  })\n  const leftMonth = computed(() => {\n    return leftDate.value.month()\n  })\n\n  const rightYear = computed(() => {\n    return rightDate.value.year()\n  })\n  const rightMonth = computed(() => {\n    return rightDate.value.month()\n  })\n\n  function computedYearLabel(\n    currentView: Ref<CurrentView>,\n    yearValue: ComputedRef<number>\n  ) {\n    const yearTranslation = t('el.datepicker.year')\n    if (currentView.value === 'year') {\n      const startYear = Math.floor(yearValue.value! / 10) * 10\n      return yearTranslation\n        ? `${startYear} ${yearTranslation} - ${\n            startYear + 9\n          } ${yearTranslation}`\n        : `${startYear} - ${startYear + 9}`\n    }\n    return `${yearValue.value} ${yearTranslation}`\n  }\n\n  function focusPicker(currentViewRef?: CurrentViewRef) {\n    currentViewRef?.focus()\n  }\n\n  async function showPicker(\n    pickerType: 'left' | 'right',\n    view: 'month' | 'year'\n  ) {\n    const currentView =\n      pickerType === 'left' ? leftCurrentView : rightCurrentView\n    const currentViewRef =\n      pickerType === 'left' ? leftCurrentViewRef : rightCurrentViewRef\n    currentView.value = view\n    await nextTick()\n    focusPicker(currentViewRef.value)\n  }\n\n  async function handlePick(\n    mode: 'month' | 'year',\n    pickerType: 'left' | 'right',\n    value: number\n  ) {\n    const isLeftPicker = pickerType === 'left'\n    const startDate = isLeftPicker ? leftDate : rightDate\n    const endDate = isLeftPicker ? rightDate : leftDate\n    const currentView = isLeftPicker ? leftCurrentView : rightCurrentView\n    const currentViewRef = isLeftPicker\n      ? leftCurrentViewRef\n      : rightCurrentViewRef\n\n    if (mode === 'year') {\n      const data = startDate.value.year(value)\n      startDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    }\n\n    if (mode === 'month') {\n      startDate.value = getValidDateOfMonth(\n        startDate.value,\n        startDate.value.year(),\n        value,\n        lang.value,\n        disabledDate\n      )\n    }\n\n    if (!props.unlinkPanels) {\n      endDate.value =\n        pickerType === 'left'\n          ? startDate.value.add(1, 'month')\n          : startDate.value.subtract(1, 'month')\n    }\n\n    currentView.value = mode === 'year' ? 'month' : 'date'\n    await nextTick()\n    focusPicker(currentViewRef.value)\n    handlePanelChange(mode)\n  }\n\n  function handlePanelChange(mode: 'month' | 'year') {\n    emit(\n      'panel-change',\n      [leftDate.value.toDate(), rightDate.value.toDate()],\n      mode\n    )\n  }\n\n  function adjustDateByView(\n    currentView: CurrentView,\n    date: Dayjs,\n    forward: boolean\n  ) {\n    const action = forward ? 'add' : 'subtract'\n    return currentView === 'year'\n      ? date[action](10, 'year')\n      : date[action](1, 'year')\n  }\n\n  return {\n    leftCurrentView,\n    rightCurrentView,\n    leftCurrentViewRef,\n    rightCurrentViewRef,\n    leftYear,\n    rightYear,\n    leftMonth,\n    rightMonth,\n    leftYearLabel: computed(() => computedYearLabel(leftCurrentView, leftYear)),\n    rightYearLabel: computed(() =>\n      computedYearLabel(rightCurrentView, rightYear)\n    ),\n    showLeftPicker: (view: 'month' | 'year') => showPicker('left', view),\n    showRightPicker: (view: 'month' | 'year') => showPicker('right', view),\n    handleLeftYearPick: (year: number) => handlePick('year', 'left', year),\n    handleRightYearPick: (year: number) => handlePick('year', 'right', year),\n    handleLeftMonthPick: (month: number) => handlePick('month', 'left', month),\n    handleRightMonthPick: (month: number) =>\n      handlePick('month', 'right', month),\n    handlePanelChange,\n    adjustDateByView,\n  }\n}\n"], "names": ["ref", "inject", "useLocale", "computed", "nextTick", "getValidDateOfYear", "getValidDateOfMonth"], "mappings": ";;;;;;;;AAGY,MAAC,iBAAiB,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,KAAK;AACvE,EAAE,MAAM,eAAe,GAAGA,OAAG,CAAC,MAAM,CAAC,CAAC;AACtC,EAAE,MAAM,kBAAkB,GAAGA,OAAG,EAAE,CAAC;AACnC,EAAE,MAAM,gBAAgB,GAAGA,OAAG,CAAC,MAAM,CAAC,CAAC;AACvC,EAAE,MAAM,mBAAmB,GAAGA,OAAG,EAAE,CAAC;AACpC,EAAE,MAAM,UAAU,GAAGC,UAAM,CAAC,gBAAgB,CAAC,CAAC;AAC9C,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC;AAC5C,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAGC,eAAS,EAAE,CAAC;AAClC,EAAE,MAAM,QAAQ,GAAGC,YAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACjC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAGA,YAAQ,CAAC,MAAM;AACnC,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAClC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAGA,YAAQ,CAAC,MAAM;AACnC,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACnC,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,iBAAiB,CAAC,WAAW,EAAE,SAAS,EAAE;AACrD,IAAI,MAAM,eAAe,GAAG,CAAC,CAAC,oBAAoB,CAAC,CAAC;AACpD,IAAI,IAAI,WAAW,CAAC,KAAK,KAAK,MAAM,EAAE;AACtC,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AAC9D,MAAM,OAAO,eAAe,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,GAAG,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3I,KAAK;AACL,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC;AACnD,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,cAAc,EAAE;AACvC,IAAI,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC;AAC7D,GAAG;AACH,EAAE,eAAe,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE;AAC9C,IAAI,MAAM,WAAW,GAAG,UAAU,KAAK,MAAM,GAAG,eAAe,GAAG,gBAAgB,CAAC;AACnF,IAAI,MAAM,cAAc,GAAG,UAAU,KAAK,MAAM,GAAG,kBAAkB,GAAG,mBAAmB,CAAC;AAC5F,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7B,IAAI,MAAMC,YAAQ,EAAE,CAAC;AACrB,IAAI,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,eAAe,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;AACrD,IAAI,MAAM,YAAY,GAAG,UAAU,KAAK,MAAM,CAAC;AAC/C,IAAI,MAAM,SAAS,GAAG,YAAY,GAAG,QAAQ,GAAG,SAAS,CAAC;AAC1D,IAAI,MAAM,OAAO,GAAG,YAAY,GAAG,SAAS,GAAG,QAAQ,CAAC;AACxD,IAAI,MAAM,WAAW,GAAG,YAAY,GAAG,eAAe,GAAG,gBAAgB,CAAC;AAC1E,IAAI,MAAM,cAAc,GAAG,YAAY,GAAG,kBAAkB,GAAG,mBAAmB,CAAC;AACnF,IAAI,IAAI,IAAI,KAAK,MAAM,EAAE;AACzB,MAAM,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/C,MAAM,SAAS,CAAC,KAAK,GAAGC,wBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,OAAO,EAAE;AAC1B,MAAM,SAAS,CAAC,KAAK,GAAGC,yBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACtH,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;AAC7B,MAAM,OAAO,CAAC,KAAK,GAAG,UAAU,KAAK,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACrH,KAAK;AACL,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC;AAC3D,IAAI,MAAMF,YAAQ,EAAE,CAAC;AACrB,IAAI,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACtC,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACnC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACpF,GAAG;AACH,EAAE,SAAS,gBAAgB,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE;AACxD,IAAI,MAAM,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,UAAU,CAAC;AAChD,IAAI,OAAO,WAAW,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACvF,GAAG;AACH,EAAE,OAAO;AACT,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,aAAa,EAAED,YAAQ,CAAC,MAAM,iBAAiB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;AAC/E,IAAI,cAAc,EAAEA,YAAQ,CAAC,MAAM,iBAAiB,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;AAClF,IAAI,cAAc,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;AACtD,IAAI,eAAe,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC;AACxD,IAAI,kBAAkB,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;AAClE,IAAI,mBAAmB,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AACpE,IAAI,mBAAmB,EAAE,CAAC,KAAK,KAAK,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC;AACtE,IAAI,oBAAoB,EAAE,CAAC,KAAK,KAAK,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;AACxE,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,GAAG,CAAC;AACJ;;;;"}