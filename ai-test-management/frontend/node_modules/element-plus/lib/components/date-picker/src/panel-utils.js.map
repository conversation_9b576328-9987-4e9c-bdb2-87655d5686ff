{"version": 3, "file": "panel-utils.js", "sources": ["../../../../../../packages/components/date-picker/src/panel-utils.ts"], "sourcesContent": ["import DatePickPanel from './date-picker-com/panel-date-pick.vue'\nimport DateRangePickPanel from './date-picker-com/panel-date-range.vue'\nimport MonthRangePickPanel from './date-picker-com/panel-month-range.vue'\nimport YearRangePickPanel from './date-picker-com/panel-year-range.vue'\nimport type { IDatePickerType } from './date-picker.type'\n\nexport const getPanel = function (type: IDatePickerType) {\n  switch (type) {\n    case 'daterange':\n    case 'datetimerange': {\n      return DateRangePickPanel\n    }\n    case 'monthrange': {\n      return MonthRangePickPanel\n    }\n    case 'yearrange': {\n      return YearRangePickPanel\n    }\n    default: {\n      return DatePickPanel\n    }\n  }\n}\n"], "names": ["DateRangePickPanel", "MonthRangePickPanel", "YearRangePickPanel", "DatePickPanel"], "mappings": ";;;;;;;;;AAIY,MAAC,QAAQ,GAAG,SAAS,IAAI,EAAE;AACvC,EAAE,QAAQ,IAAI;AACd,IAAI,KAAK,WAAW,CAAC;AACrB,IAAI,KAAK,eAAe,EAAE;AAC1B,MAAM,OAAOA,yBAAkB,CAAC;AAChC,KAAK;AACL,IAAI,KAAK,YAAY,EAAE;AACvB,MAAM,OAAOC,0BAAmB,CAAC;AACjC,KAAK;AACL,IAAI,KAAK,WAAW,EAAE;AACtB,MAAM,OAAOC,yBAAkB,CAAC;AAChC,KAAK;AACL,IAAI,SAAS;AACb,MAAM,OAAOC,wBAAa,CAAC;AAC3B,KAAK;AACL,GAAG;AACH;;;;"}