{"version": 3, "file": "basic-year-table.js", "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-year-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const basicYearTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault('year'),\n} as const)\n\nexport type BasicYearTableProps = ExtractPropTypes<typeof basicYearTableProps>\n"], "names": ["buildProps", "datePickerSharedProps", "selectionModeWithDefault"], "mappings": ";;;;;;;AAEY,MAAC,mBAAmB,GAAGA,kBAAU,CAAC;AAC9C,EAAE,GAAGC,4BAAqB;AAC1B,EAAE,aAAa,EAAEC,+BAAwB,CAAC,MAAM,CAAC;AACjD,CAAC;;;;"}