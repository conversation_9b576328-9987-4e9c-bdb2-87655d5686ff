{"version": 3, "file": "panel-month-range.js", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-month-range.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ppNs.b(),\n      drpNs.b(),\n      {\n        'has-sidebar': Bo<PERSON>an($slots.sidebar) || hasShortcuts,\n      },\n    ]\"\n  >\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-left\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-left\"\n              @click=\"leftPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"[\n                ppNs.e('icon-btn'),\n                { [ppNs.is('disabled')]: !enableYearArrow },\n              ]\"\n              class=\"d-arrow-right\"\n              @click=\"leftNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <month-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-right\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"[ppNs.e('icon-btn'), { 'is-disabled': !enableYearArrow }]\"\n              class=\"d-arrow-left\"\n              @click=\"rightPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-right\"\n              @click=\"rightNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <month-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, unref } from 'vue'\nimport dayjs from 'dayjs'\nimport ElIcon from '@element-plus/components/icon'\nimport { isArray } from '@element-plus/utils'\nimport { useLocale } from '@element-plus/hooks'\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'\nimport {\n  correctlyParseUserInput,\n  getDefaultValue,\n  isValidRange,\n} from '../utils'\nimport {\n  panelMonthRangeEmits,\n  panelMonthRangeProps,\n} from '../props/panel-month-range'\nimport { useMonthRangeHeader } from '../composables/use-month-range-header'\nimport { useRangePicker } from '../composables/use-range-picker'\nimport MonthTable from './basic-month-table.vue'\n\nimport type { Dayjs } from 'dayjs'\n\ndefineOptions({\n  name: 'DatePickerMonthRange',\n})\n\nconst props = defineProps(panelMonthRangeProps)\nconst emit = defineEmits(panelMonthRangeEmits)\nconst unit = 'year'\n\nconst { lang } = useLocale()\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst isDefaultFormat = inject('ElIsDefaultFormat') as any\nconst { shortcuts, disabledDate } = pickerBase.props\nconst format = toRef(pickerBase.props, 'format')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\nconst leftDate = ref(dayjs().locale(lang.value))\nconst rightDate = ref(dayjs().locale(lang.value).add(1, unit))\n\nconst {\n  minDate,\n  maxDate,\n  rangeState,\n  ppNs,\n  drpNs,\n\n  handleChangeRange,\n  handleRangeConfirm,\n  handleShortcutClick,\n  onSelect,\n} = useRangePicker(props, {\n  defaultValue,\n  leftDate,\n  rightDate,\n  unit,\n  onParsedValueChanged,\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst {\n  leftPrevYear,\n  rightNextYear,\n  leftNextYear,\n  rightPrevYear,\n  leftLabel,\n  rightLabel,\n  leftYear,\n  rightYear,\n} = useMonthRangeHeader({\n  unlinkPanels: toRef(props, 'unlinkPanels'),\n  leftDate,\n  rightDate,\n})\n\nconst enableYearArrow = computed(() => {\n  return props.unlinkPanels && rightYear.value > leftYear.value + 1\n})\n\ntype RangePickValue = {\n  minDate: Dayjs\n  maxDate: Dayjs\n}\n\nconst handleRangePick = (val: RangePickValue, close = true) => {\n  // const defaultTime = props.defaultTime || []\n  // const minDate_ = modifyWithTimeString(val.minDate, defaultTime[0])\n  // const maxDate_ = modifyWithTimeString(val.maxDate, defaultTime[1])\n  // todo\n  const minDate_ = val.minDate\n  const maxDate_ = val.maxDate\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [minDate_.toDate(), maxDate_ && maxDate_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close) return\n  handleRangeConfirm()\n}\n\nconst handleClear = () => {\n  leftDate.value = getDefaultValue(unref(defaultValue), {\n    lang: unref(lang),\n    unit: 'year',\n    unlinkPanels: props.unlinkPanels,\n  })[0]\n  rightDate.value = leftDate.value.add(1, 'year')\n  emit('pick', null)\n}\n\nconst formatToString = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? value.map((_) => _.format(format.value))\n    : value.format(format.value)\n}\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return correctlyParseUserInput(\n    value,\n    format.value,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nfunction onParsedValueChanged(\n  minDate: Dayjs | undefined,\n  maxDate: Dayjs | undefined\n) {\n  if (props.unlinkPanels && maxDate) {\n    const minDateYear = minDate?.year() || 0\n    const maxDateYear = maxDate.year()\n    rightDate.value =\n      minDateYear === maxDateYear ? maxDate.add(1, unit) : maxDate\n  } else {\n    rightDate.value = leftDate.value.add(1, unit)\n  }\n}\n\nemit('set-picker-option', ['isValidValue', isValidRange])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["useLocale", "inject", "toRef", "ref", "dayjs", "useRangePicker", "computed", "useMonthRangeHeader", "getDefaultValue", "unref", "isArray", "correctlyParseUserInput", "isValidRange", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;uCAkIc,CAAA;AAAA,EACZ,IAAM,EAAA,sBAAA;AACR;;;;;;;AAMA,IAAM,MAAA,EAAE,IAAK,EAAA,GAAIA,eAAU,EAAA,CAAA;AAC3B,IAAM,MAAA,UAAA,GAAaC,WAAO,gBAAgB,CAAA,CAAA;AAC1C,IAAM,MAAA,eAAA,GAAkBA,WAAO,mBAAmB,CAAA,CAAA;AAClD,IAAA,MAAM,EAAE,SAAA,EAAW,YAAa,EAAA,GAAI,UAAW,CAAA,KAAA,CAAA;AAC/C,IAAA,MAAM,MAAS,GAAAC,SAAA,CAAM,UAAW,CAAA,KAAA,EAAO,QAAQ,CAAA,CAAA;AAC/C,IAAA,MAAM,YAAe,GAAAA,SAAA,CAAM,UAAW,CAAA,KAAA,EAAO,cAAc,CAAA,CAAA;AAC3D,IAAA,MAAM,WAAWC,OAAI,CAAAC,yBAAA,GAAQ,MAAO,CAAA,IAAA,CAAK,KAAK,CAAC,CAAA,CAAA;AAC/C,IAAM,MAAA,SAAA,GAAYD,OAAI,CAAAC,yBAAA,EAAQ,CAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,GAAA,CAAI,CAAG,EAAA,IAAI,CAAC,CAAA,CAAA;AAE7D,IAAM,MAAA;AAAA,MACJ,OAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,IAAA;AAAA,MACA,KAAA;AAAA,MAEA,iBAAA;AAAA,MACA,kBAAA;AAAA,MACA,mBAAA;AAAA,MACA,QAAA;AAAA,KACF,GAAIC,8BAAe,KAAO,EAAA;AAAA,MACxB,YAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA,IAAA;AAAA,MACA,oBAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAM,eAAeC,YAAS,CAAA,MAAM,CAAC,CAAC,UAAU,MAAM,CAAA,CAAA;AAEtD,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,QACEC,uCAAoB,CAAA;AAAA,MACtB,YAAA,EAAcL,SAAM,CAAA,KAAA,EAAO,cAAc,CAAA;AAAA,MACzC,QAAA;AAAA,MACA,SAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkBI,aAAS,MAAM;AACrC,MAAA,OAAO,KAAM,CAAA,YAAA,IAAgB,SAAU,CAAA,KAAA,GAAQ,SAAS,KAAQ,GAAA,CAAA,CAAA;AAAA,KACjE,CAAA,CAAA;AAOD,IAAA,MAAM,eAAkB,GAAA,CAAC,GAAqB,EAAA,KAAA,GAAQ,IAAS,KAAA;AAK7D,MAAA,MAAM,WAAW,GAAI,CAAA,OAAA,CAAA;AACrB,MAAA,MAAM,WAAW,GAAI,CAAA,OAAA,CAAA;AACrB,MAAA,IAAI,OAAQ,CAAA,KAAA,KAAU,QAAY,IAAA,OAAA,CAAQ,UAAU,QAAU,EAAA;AAC5D,QAAA,OAAA;AAAA,OACF;AACA,MAAK,IAAA,CAAA,iBAAA,EAAmB,CAAC,QAAS,CAAA,MAAA,IAAU,QAAY,IAAA,QAAA,CAAS,MAAO,EAAC,CAAC,CAAA,CAAA;AAC1E,MAAA,OAAA,CAAQ,KAAQ,GAAA,QAAA,CAAA;AAChB,MAAA,OAAA,CAAQ,KAAQ,GAAA,QAAA,CAAA;AAEhB,MAAA,IAAI,CAAC,KAAO;AACZ,QAAmB,OAAA;AAAA,MACrB,kBAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,WAAiB,GAAA,MAAA;AAAqC,MACpD,QAAM,MAAM,GAAIE,qBAAA,CAAAC,SAAA,CAAA,YAAA,CAAA,EAAA;AAAA,QAChB,IAAM,EAAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QACN;AAAoB,oBAClB,EAAA,KAAA,CAAA,YAAA;AACJ,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,eAAiB,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;AAAA,MACnB,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,cAAoB,GAAA,CAAA,KACV,KAAA;AACmB,MAC/B,OAAAC,cAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAO,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACL,OAAAC,6BAAA,CAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACO,IAAA,SACF,oBAAA,CAAA,QAAA,EAAA,QAAA,EAAA;AAAA,MACL,IAAA,KAAA,CAAA,YAAA,IAAA,QAAA,EAAA;AAAA,QACF,MAAA,WAAA,GAAA,CAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AAAA,QACF,MAAA,WAAA,GAAA,QAAA,CAAA,IAAA,EAAA,CAAA;AAEA,QAAS,SAAA,CAAA,KAAA,GAAA,2BAGP,GAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,GAAA,QAAA,CAAA;AACA,OAAI,MAAA;AACF,QAAM,SAAA,CAAA,KAAA,GAAA,QAAuB,CAAA,KAAA,CAAA,GAAU,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACvC,OAAM;AACN,KAAA;AACuD,IAAA,IAClD,CAAA,mBAAA,EAAA,CAAA,cAAA,EAAAC,kBAAA,CAAA,CAAA,CAAA;AACL,IAAA,IAAA,CAAA,mBAAkB,EAAA,CAAA,kBAA0B,cAAA,CAAA,CAAA,CAAA;AAAA,IAC9C,IAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IACF,IAAA,CAAA,mBAAA,EAAA,CAAA,aAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AAEA,IAAA,OAA0B,CAAA,IAAA,EAAA,MAAA,KAAA;AAC1B,MAAA,OAA0BC,aAAA,EAAA,EAAAC,sBAAmB,CAAA,KAAA,EAAA;AAC7C,QAAA,KAA0B,EAAAC,kBAAA,CAAC;AAC3B,UAA0BC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}