{"version": 3, "file": "use-year-range-header.js", "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-year-range-header.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport type { Ref, ToRef } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const useYearRangeHeader = ({\n  unlinkPanels,\n  leftDate,\n  rightDate,\n}: {\n  unlinkPanels: ToRef<boolean>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n}) => {\n  const leftPrevYear = () => {\n    leftDate.value = leftDate.value.subtract(10, 'year')\n    if (!unlinkPanels.value) {\n      rightDate.value = rightDate.value.subtract(10, 'year')\n    }\n  }\n\n  const rightNextYear = () => {\n    if (!unlinkPanels.value) {\n      leftDate.value = leftDate.value.add(10, 'year')\n    }\n    rightDate.value = rightDate.value.add(10, 'year')\n  }\n\n  const leftNextYear = () => {\n    leftDate.value = leftDate.value.add(10, 'year')\n  }\n\n  const rightPrevYear = () => {\n    rightDate.value = rightDate.value.subtract(10, 'year')\n  }\n\n  const leftLabel = computed(() => {\n    const leftStartDate = Math.floor(leftDate.value.year() / 10) * 10\n    return `${leftStartDate}-${leftStartDate + 9}`\n  })\n\n  const rightLabel = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10\n    return `${rightStartDate}-${rightStartDate + 9}`\n  })\n\n  const leftYear = computed(() => {\n    const leftEndDate = Math.floor(leftDate.value.year() / 10) * 10 + 9\n    return leftEndDate\n  })\n\n  const rightYear = computed(() => {\n    const rightStartDate = Math.floor(rightDate.value.year() / 10) * 10\n    return rightStartDate\n  })\n\n  return {\n    leftPrevYear,\n    rightNextYear,\n    leftNextYear,\n    rightPrevYear,\n    leftLabel,\n    rightLabel,\n    leftYear,\n    rightYear,\n  }\n}\n"], "names": ["computed"], "mappings": ";;;;;;AACY,MAAC,kBAAkB,GAAG,CAAC;AACnC,EAAE,YAAY;AACd,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,CAAC,KAAK;AACN,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC7B,MAAM,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAC7B,MAAM,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACtD,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AACpD,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAC3D,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAGA,YAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AACtE,IAAI,OAAO,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM;AACpC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AACxE,IAAI,OAAO,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,MAAM;AAClC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxE,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAGA,YAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AACxE,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,GAAG,CAAC;AACJ;;;;"}