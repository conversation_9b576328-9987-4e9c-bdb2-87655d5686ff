{"version": 3, "file": "helper.js", "sources": ["../../../../../../packages/components/mention/src/helper.ts"], "sourcesContent": ["import { ensureArray, isFirefox } from '@element-plus/utils'\n\nimport type { MentionCtx, MentionOption } from './types'\n\nexport const filterOption = (\n  pattern: string,\n  option: MentionOption\n): boolean => {\n  const lowerCase = pattern.toLowerCase()\n  const label = option.label || option.value\n  return label.toLowerCase().includes(lowerCase)\n}\n\nexport const getMentionCtx = (\n  inputEl: HTMLInputElement | HTMLTextAreaElement,\n  prefix: string | string[],\n  split: string\n) => {\n  const { selectionEnd } = inputEl\n  if (selectionEnd === null) return\n  const inputValue = inputEl.value\n  const prefixArray = ensureArray(prefix)\n  let splitIndex = -1\n  let mentionCtx: MentionCtx | undefined\n  for (let i = selectionEnd - 1; i >= 0; --i) {\n    const char = inputValue[i]\n    if (char === split || char === '\\n' || char === '\\r') {\n      splitIndex = i\n      continue\n    }\n    if (prefixArray.includes(char)) {\n      const end = splitIndex === -1 ? selectionEnd : splitIndex\n      const pattern = inputValue.slice(i + 1, end)\n      mentionCtx = {\n        pattern,\n        start: i + 1,\n        end,\n        prefix: char,\n        prefixIndex: i,\n        splitIndex,\n        selectionEnd,\n      }\n      break\n    }\n  }\n  return mentionCtx\n}\n\n/**\n * fork from textarea-caret-position\n * https://github.com/component/textarea-caret-position\n * The MIT License (MIT)\n * Copyright (c) 2015 <NAME_EMAIL>\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nexport const getCursorPosition = (\n  element: HTMLInputElement | HTMLTextAreaElement,\n  options = {\n    debug: false,\n    useSelectionEnd: false,\n  }\n) => {\n  const selectionStart =\n    element.selectionStart !== null ? element.selectionStart : 0\n  const selectionEnd = element.selectionEnd !== null ? element.selectionEnd : 0\n  const position = options.useSelectionEnd ? selectionEnd : selectionStart\n  // We'll copy the properties below into the mirror div.\n  // Note that some browsers, such as Firefox, do not concatenate properties\n  // into their shorthand (e.g. padding-top, padding-bottom etc. -> padding),\n  // so we have to list every single property explicitly.\n  const properties: string[] = [\n    'direction', // RTL support\n    'boxSizing',\n    'width', // on Chrome and IE, exclude the scrollbar, so the mirror div wraps exactly as the textarea does\n    'height',\n    'overflowX',\n    'overflowY', // copy the scrollbar for IE\n    'borderTopWidth',\n    'borderRightWidth',\n    'borderBottomWidth',\n    'borderLeftWidth',\n    'borderStyle',\n    'paddingTop',\n    'paddingRight',\n    'paddingBottom',\n    'paddingLeft',\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/font\n    'fontStyle',\n    'fontVariant',\n    'fontWeight',\n    'fontStretch',\n    'fontSize',\n    'fontSizeAdjust',\n    'lineHeight',\n    'fontFamily',\n    'textAlign',\n    'textTransform',\n    'textIndent',\n    'textDecoration', // might not make a difference, but better be safe\n    'letterSpacing',\n    'wordSpacing',\n    'tabSize',\n    'MozTabSize',\n  ]\n\n  if (options.debug) {\n    const el = document.querySelector(\n      '#input-textarea-caret-position-mirror-div'\n    )\n    if (el?.parentNode) el.parentNode.removeChild(el)\n  }\n\n  // The mirror div will replicate the textareas style\n  const div = document.createElement('div')\n  div.id = 'input-textarea-caret-position-mirror-div'\n  document.body.appendChild(div)\n\n  const style = div.style\n  const computed = window.getComputedStyle(element)\n\n  const isInput = element.nodeName === 'INPUT'\n\n  // Default textarea styles\n  style.whiteSpace = isInput ? 'nowrap' : 'pre-wrap'\n  if (!isInput) style.wordWrap = 'break-word' // only for textarea-s\n\n  // Position off-screen\n  style.position = 'absolute' // required to return coordinates properly\n  if (!options.debug) style.visibility = 'hidden' // not 'display: none' because we want rendering\n\n  // Transfer the element's properties to the div\n  properties.forEach((prop) => {\n    if (isInput && prop === 'lineHeight') {\n      // Special case for <input>s because text is rendered centered and line height may be != height\n      if (computed.boxSizing === 'border-box') {\n        const height = Number.parseInt(computed.height as string)\n        const outerHeight =\n          Number.parseInt(computed.paddingTop as string) +\n          Number.parseInt(computed.paddingBottom as string) +\n          Number.parseInt(computed.borderTopWidth as string) +\n          Number.parseInt(computed.borderBottomWidth as string)\n        const targetHeight =\n          outerHeight + Number.parseInt(computed.lineHeight as string)\n        if (height > targetHeight) {\n          style.lineHeight = `${height - outerHeight}px`\n        } else if (height === targetHeight) {\n          style.lineHeight = computed.lineHeight\n        } else {\n          style.lineHeight = '0'\n        }\n      } else {\n        style.lineHeight = computed.height\n      }\n    } else {\n      style[prop as any] = computed[prop as any]\n    }\n  })\n\n  if (isFirefox()) {\n    // Firefox lies about the overflow property for textareas: https://bugzilla.mozilla.org/show_bug.cgi?id=984275\n    if (element.scrollHeight > Number.parseInt(computed.height as string)) {\n      style.overflowY = 'scroll'\n    }\n  } else {\n    style.overflow = 'hidden' // for Chrome to not render a scrollbar; IE keeps overflowY = 'scroll'\n  }\n\n  div.textContent = element.value.slice(0, Math.max(0, position))\n  // The second special handling for input type=\"text\" vs textarea:\n  // spaces need to be replaced with non-breaking spaces - http://stackoverflow.com/a/13402035/1269037\n  if (isInput && div.textContent) {\n    div.textContent = div.textContent.replace(/\\s/g, '\\u00A0')\n  }\n\n  const span = document.createElement('span')\n  // Wrapping must be replicated *exactly*, including when a long word gets\n  // onto the next line, with whitespace at the end of the line before (#7).\n  // The  *only* reliable way to do that is to copy the *entire* rest of the\n  // textareas content into the <span> created at the caret position.\n  // For inputs, just '.' would be enough, but no need to bother.\n  span.textContent = element.value.slice(Math.max(0, position)) || '.' // || because a completely empty faux span doesn't render at all\n  span.style.position = 'relative'\n  span.style.left = `${-element.scrollLeft}px`\n  span.style.top = `${-element.scrollTop}px`\n  div.appendChild(span)\n\n  const relativePosition = {\n    top: span.offsetTop + Number.parseInt(computed.borderTopWidth as string),\n    left: span.offsetLeft + Number.parseInt(computed.borderLeftWidth as string),\n    // We don't use line-height since it may be too large for position. Eg. 34px\n    // for input\n    height: Number.parseInt(computed.fontSize as string) * 1.5,\n  }\n\n  if (options.debug) {\n    span.style.backgroundColor = '#aaa'\n  } else {\n    document.body.removeChild(div)\n  }\n\n  if (relativePosition.left >= element.clientWidth) {\n    relativePosition.left = element.clientWidth\n  }\n  return relativePosition\n}\n"], "names": ["ensureArray", "isFirefox"], "mappings": ";;;;;;;AACY,MAAC,YAAY,GAAG,CAAC,OAAO,EAAE,MAAM,KAAK;AACjD,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;AAC1C,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7C,EAAE,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACjD,EAAE;AACU,MAAC,aAAa,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,KAAK;AACzD,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,YAAY,KAAK,IAAI;AAC3B,IAAI,OAAO;AACX,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC;AACnC,EAAE,MAAM,WAAW,GAAGA,uBAAW,CAAC,MAAM,CAAC,CAAC;AAC1C,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC;AACtB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,YAAY,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC9C,IAAI,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC1D,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,SAAS;AACf,KAAK;AACL,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACpC,MAAM,MAAM,GAAG,GAAG,UAAU,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,UAAU,CAAC;AAChE,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AACnD,MAAM,UAAU,GAAG;AACnB,QAAQ,OAAO;AACf,QAAQ,KAAK,EAAE,CAAC,GAAG,CAAC;AACpB,QAAQ,GAAG;AACX,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,WAAW,EAAE,CAAC;AACtB,QAAQ,UAAU;AAClB,QAAQ,YAAY;AACpB,OAAO,CAAC;AACR,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,EAAE,OAAO,UAAU,CAAC;AACpB,EAAE;AACU,MAAC,iBAAiB,GAAG,CAAC,OAAO,EAAE,OAAO,GAAG;AACrD,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,eAAe,EAAE,KAAK;AACxB,CAAC,KAAK;AACN,EAAE,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC;AACtF,EAAE,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,IAAI,GAAG,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;AAChF,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,GAAG,YAAY,GAAG,cAAc,CAAC;AAC3E,EAAE,MAAM,UAAU,GAAG;AACrB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,gBAAgB;AACpB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,2CAA2C,CAAC,CAAC;AACnF,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU;AAC3C,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC5C,EAAE,GAAG,CAAC,EAAE,GAAG,0CAA0C,CAAC;AACtD,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACjC,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AAC1B,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACpD,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;AAC/C,EAAE,KAAK,CAAC,UAAU,GAAG,OAAO,GAAG,QAAQ,GAAG,UAAU,CAAC;AACrD,EAAE,IAAI,CAAC,OAAO;AACd,IAAI,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC;AAClC,EAAE,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC9B,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;AACpB,IAAI,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AAChC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC/B,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK,YAAY,EAAE;AAC1C,MAAM,IAAI,QAAQ,CAAC,SAAS,KAAK,YAAY,EAAE;AAC/C,QAAQ,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxD,QAAQ,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;AACpM,QAAQ,MAAM,YAAY,GAAG,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAChF,QAAQ,IAAI,MAAM,GAAG,YAAY,EAAE;AACnC,UAAU,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AACzD,SAAS,MAAM,IAAI,MAAM,KAAK,YAAY,EAAE;AAC5C,UAAU,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AACjD,SAAS,MAAM;AACf,UAAU,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;AACjC,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC3C,OAAO;AACP,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,IAAIC,iBAAS,EAAE,EAAE;AACnB,IAAI,IAAI,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACjE,MAAM,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC;AACjC,KAAK;AACL,GAAG,MAAM;AACT,IAAI,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AAClE,EAAE,IAAI,OAAO,IAAI,GAAG,CAAC,WAAW,EAAE;AAClC,IAAI,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC;AACvE,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACnC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;AAC/C,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AAC7C,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACxB,EAAE,MAAM,gBAAgB,GAAG;AAC3B,IAAI,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC;AAClE,IAAI,IAAI,EAAE,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC;AACrE,IAAI,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,GAAG;AACpD,GAAG,CAAC;AACJ,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC;AACxC,GAAG,MAAM;AACT,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,IAAI,gBAAgB,CAAC,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE;AACpD,IAAI,gBAAgB,CAAC,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC;AAChD,GAAG;AACH,EAAE,OAAO,gBAAgB,CAAC;AAC1B;;;;;;"}