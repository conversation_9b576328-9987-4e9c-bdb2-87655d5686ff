{"version": 3, "file": "icon2.js", "sources": ["../../../../../../packages/components/icon/src/icon.vue"], "sourcesContent": ["<template>\n  <i :class=\"ns.b()\" :style=\"style\" v-bind=\"$attrs\">\n    <slot />\n  </i>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { addUnit, isUndefined } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { iconProps } from './icon'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElIcon',\n  inheritAttrs: false,\n})\nconst props = defineProps(iconProps)\nconst ns = useNamespace('icon')\n\nconst style = computed<CSSProperties>(() => {\n  const { size, color } = props\n  if (!size && !color) return {}\n\n  return {\n    fontSize: isUndefined(size) ? undefined : addUnit(size),\n    '--color': color,\n  }\n})\n</script>\n"], "names": ["useNamespace", "style", "computed", "isUndefined", "addUnit"], "mappings": ";;;;;;;;;;;uCAac,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAEA,IAAM,MAAA,EAAA,GAAKA,mBAAa,MAAM,CAAA,CAAA;AAE9B,IAAM,MAAAC,OAAA,GAAQC,aAAwB,MAAM;AAC1C,MAAM,MAAA,EAAE,IAAM,EAAA,KAAA,EAAU,GAAA,KAAA,CAAA;AACxB,MAAA,IAAI,CAAC,IAAA,IAAQ,CAAC,KAAA;AAEd,QAAO,OAAA,EAAA,CAAA;AAAA,MAAA;AACiD,QACtD,QAAW,EAAAC,iBAAA,CAAA,IAAA,CAAA,GAAA,KAAA,CAAA,GAAAC,aAAA,CAAA,IAAA,CAAA;AAAA,QACb,SAAA,EAAA,KAAA;AAAA,OACD,CAAA;;;;;;;;;;;;;;;;"}