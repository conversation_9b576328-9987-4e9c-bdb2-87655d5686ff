{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/teleport/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Teleport from './src/teleport.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTeleport: SFCWithInstall<typeof Teleport> = withInstall(Teleport)\n\nexport default ElTeleport\n\nexport * from './src/teleport'\n"], "names": ["withInstall", "Teleport"], "mappings": ";;;;;;;;AAEY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ;;;;;;"}