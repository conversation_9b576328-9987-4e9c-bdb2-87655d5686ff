{"version": 3, "file": "fixed-size-grid.js", "sources": ["../../../../../../../packages/components/virtual-list/src/components/fixed-size-grid.ts"], "sourcesContent": ["import { isNumber, throwError } from '@element-plus/utils'\nimport createGrid from '../builders/build-grid'\n\nimport {\n  AUTO_ALIGNMENT,\n  CENTERED_ALIGNMENT,\n  END_ALIGNMENT,\n  SMART_ALIGNMENT,\n  START_ALIGNMENT,\n} from '../defaults'\n\nconst SCOPE = 'ElFixedSizeGrid'\n\nconst FixedSizeGrid = createGrid({\n  name: 'ElFixedSizeGrid',\n  getColumnPosition: ({ columnWidth }, index) => [\n    columnWidth as number,\n    index * (columnWidth as number),\n  ],\n\n  getRowPosition: ({ rowHeight }, index) => [\n    rowHeight as number,\n    index * (rowHeight as number),\n  ],\n\n  getEstimatedTotalHeight: ({ totalRow, rowHeight }) =>\n    (rowHeight as number) * totalRow,\n\n  getEstimatedTotalWidth: ({ totalColumn, columnWidth }) =>\n    (columnWidth as number) * totalColumn,\n\n  getColumnOffset: (\n    { totalColumn, columnWidth, width },\n    columnIndex,\n    alignment,\n    scrollLeft,\n    _,\n    scrollBarWidth\n  ) => {\n    width = Number(width)\n    const lastColumnOffset = Math.max(\n      0,\n      totalColumn * (columnWidth as number) - width\n    )\n    const maxOffset = Math.min(\n      lastColumnOffset,\n      columnIndex * (columnWidth as number)\n    )\n    const minOffset = Math.max(\n      0,\n      columnIndex * (columnWidth as number) -\n        width +\n        scrollBarWidth +\n        (columnWidth as number)\n    )\n\n    if (alignment === 'smart') {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        alignment = AUTO_ALIGNMENT\n      } else {\n        alignment = CENTERED_ALIGNMENT\n      }\n    }\n\n    switch (alignment) {\n      case START_ALIGNMENT:\n        return maxOffset\n      case END_ALIGNMENT:\n        return minOffset\n      case CENTERED_ALIGNMENT: {\n        const middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2)\n        if (middleOffset < Math.ceil(width / 2)) {\n          return 0\n        } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n          return lastColumnOffset\n        } else {\n          return middleOffset\n        }\n      }\n      case AUTO_ALIGNMENT:\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft\n        } else if (minOffset > maxOffset) {\n          return minOffset\n        } else if (scrollLeft < minOffset) {\n          return minOffset\n        } else {\n          return maxOffset\n        }\n    }\n  },\n\n  getRowOffset: (\n    { rowHeight, height, totalRow },\n    rowIndex,\n    align,\n    scrollTop,\n    _,\n    scrollBarWidth\n  ): number => {\n    height = Number(height)\n    const lastRowOffset = Math.max(0, totalRow * (rowHeight as number) - height)\n    const maxOffset = Math.min(lastRowOffset, rowIndex * (rowHeight as number))\n    const minOffset = Math.max(\n      0,\n      rowIndex * (rowHeight as number) -\n        height +\n        scrollBarWidth +\n        (rowHeight as number)\n    )\n\n    if (align === SMART_ALIGNMENT) {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = AUTO_ALIGNMENT\n      } else {\n        align = CENTERED_ALIGNMENT\n      }\n    }\n\n    switch (align) {\n      case START_ALIGNMENT:\n        return maxOffset\n      case END_ALIGNMENT:\n        return minOffset\n      case CENTERED_ALIGNMENT: {\n        const middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2)\n        if (middleOffset < Math.ceil(height / 2)) {\n          return 0\n        } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n          return lastRowOffset\n        } else {\n          return middleOffset\n        }\n      }\n      case AUTO_ALIGNMENT:\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop\n        } else if (minOffset > maxOffset) {\n          return minOffset\n        } else if (scrollTop < minOffset) {\n          return minOffset\n        } else {\n          return maxOffset\n        }\n    }\n  },\n\n  getColumnStartIndexForOffset: ({ columnWidth, totalColumn }, scrollLeft) =>\n    Math.max(\n      0,\n      Math.min(\n        totalColumn - 1,\n        Math.floor(scrollLeft / (columnWidth as number))\n      )\n    ),\n\n  getColumnStopIndexForStartIndex: (\n    { columnWidth, totalColumn, width },\n    startIndex: number,\n    scrollLeft: number\n  ): number => {\n    const left = startIndex * (columnWidth as number)\n    const visibleColumnsCount = Math.ceil(\n      ((width as number) + scrollLeft - left) / (columnWidth as number)\n    )\n    return Math.max(\n      0,\n      Math.min(totalColumn - 1, startIndex + visibleColumnsCount - 1)\n    )\n  },\n\n  getRowStartIndexForOffset: (\n    { rowHeight, totalRow },\n    scrollTop: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(totalRow - 1, Math.floor(scrollTop / (rowHeight as number)))\n    ),\n\n  getRowStopIndexForStartIndex: (\n    { rowHeight, totalRow, height },\n    startIndex: number,\n    scrollTop: number\n  ): number => {\n    const top = startIndex * (rowHeight as number)\n    const numVisibleRows = Math.ceil(\n      ((height as number) + scrollTop - top) / (rowHeight as number)\n    )\n    return Math.max(\n      0,\n      Math.min(\n        totalRow - 1,\n        startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n      )\n    )\n  },\n  /**\n   * Fixed size grid does not need this cache\n   * Using any to bypass it, TODO: Using type inference to fix this.\n   */\n  initCache: () => undefined as any,\n\n  clearCache: true,\n\n  validateProps: ({ columnWidth, rowHeight }) => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!isNumber(columnWidth)) {\n        throwError(\n          SCOPE,\n          `\n          \"columnWidth\" must be passed as number,\n            instead ${typeof columnWidth} was given.\n        `\n        )\n      }\n\n      if (!isNumber(rowHeight)) {\n        throwError(\n          SCOPE,\n          `\n          \"columnWidth\" must be passed as number,\n            instead ${typeof rowHeight} was given.\n        `\n        )\n      }\n    }\n  },\n})\n\nexport default FixedSizeGrid\n"], "names": ["createGrid", "AUTO_ALIGNMENT", "CENTERED_ALIGNMENT", "START_ALIGNMENT", "END_ALIGNMENT", "SMART_ALIGNMENT", "isNumber", "throwError"], "mappings": ";;;;;;;;;AASA,MAAM,KAAK,GAAG,iBAAiB,CAAC;AAC3B,MAAC,aAAa,GAAGA,oBAAU,CAAC;AACjC,EAAE,IAAI,EAAE,iBAAiB;AACzB,EAAE,iBAAiB,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,KAAK,KAAK;AACjD,IAAI,WAAW;AACf,IAAI,KAAK,GAAG,WAAW;AACvB,GAAG;AACH,EAAE,cAAc,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,KAAK,KAAK;AAC5C,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,SAAS;AACrB,GAAG;AACH,EAAE,uBAAuB,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,SAAS,GAAG,QAAQ;AAC5E,EAAE,sBAAsB,EAAE,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,WAAW,GAAG,WAAW;AACrF,EAAE,eAAe,EAAE,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,cAAc,KAAK;AACnH,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1B,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC,CAAC;AAC5E,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,WAAW,GAAG,WAAW,CAAC,CAAC;AAC5E,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,WAAW,GAAG,KAAK,GAAG,cAAc,GAAG,WAAW,CAAC,CAAC;AACpG,IAAI,IAAI,SAAS,KAAK,OAAO,EAAE;AAC/B,MAAM,IAAI,UAAU,IAAI,SAAS,GAAG,KAAK,IAAI,UAAU,IAAI,SAAS,GAAG,KAAK,EAAE;AAC9E,QAAQ,SAAS,GAAGC,uBAAc,CAAC;AACnC,OAAO,MAAM;AACb,QAAQ,SAAS,GAAGC,2BAAkB,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,SAAS;AACrB,MAAM,KAAKC,wBAAe;AAC1B,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,KAAKC,sBAAa;AACxB,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,KAAKF,2BAAkB,EAAE;AAC/B,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,CAAC,CAAC;AACjF,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;AACjD,UAAU,OAAO,CAAC,CAAC;AACnB,SAAS,MAAM,IAAI,YAAY,GAAG,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;AAC5E,UAAU,OAAO,gBAAgB,CAAC;AAClC,SAAS,MAAM;AACf,UAAU,OAAO,YAAY,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,MAAM,KAAKD,uBAAc,CAAC;AAC1B,MAAM;AACN,QAAQ,IAAI,UAAU,IAAI,SAAS,IAAI,UAAU,IAAI,SAAS,EAAE;AAChE,UAAU,OAAO,UAAU,CAAC;AAC5B,SAAS,MAAM,IAAI,SAAS,GAAG,SAAS,EAAE;AAC1C,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS,MAAM,IAAI,UAAU,GAAG,SAAS,EAAE;AAC3C,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS,MAAM;AACf,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS;AACT,KAAK;AACL,GAAG;AACH,EAAE,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,EAAE,cAAc,KAAK;AACpG,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC5B,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC;AACrE,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;AACpE,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,GAAG,SAAS,GAAG,MAAM,GAAG,cAAc,GAAG,SAAS,CAAC,CAAC;AAC9F,IAAI,IAAI,KAAK,KAAKI,wBAAe,EAAE;AACnC,MAAM,IAAI,SAAS,IAAI,SAAS,GAAG,MAAM,IAAI,SAAS,IAAI,SAAS,GAAG,MAAM,EAAE;AAC9E,QAAQ,KAAK,GAAGJ,uBAAc,CAAC;AAC/B,OAAO,MAAM;AACb,QAAQ,KAAK,GAAGC,2BAAkB,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,KAAK;AACjB,MAAM,KAAKC,wBAAe;AAC1B,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,KAAKC,sBAAa;AACxB,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,KAAKF,2BAAkB,EAAE;AAC/B,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,CAAC,CAAC;AACjF,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AAClD,UAAU,OAAO,CAAC,CAAC;AACnB,SAAS,MAAM,IAAI,YAAY,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AAC1E,UAAU,OAAO,aAAa,CAAC;AAC/B,SAAS,MAAM;AACf,UAAU,OAAO,YAAY,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,MAAM,KAAKD,uBAAc,CAAC;AAC1B,MAAM;AACN,QAAQ,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,EAAE;AAC9D,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS,MAAM,IAAI,SAAS,GAAG,SAAS,EAAE;AAC1C,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS,MAAM,IAAI,SAAS,GAAG,SAAS,EAAE;AAC1C,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS,MAAM;AACf,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS;AACT,KAAK;AACL,GAAG;AACH,EAAE,4BAA4B,EAAE,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,EAAE,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;AAC1J,EAAE,+BAA+B,EAAE,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,UAAU,KAAK;AACpG,IAAI,MAAM,IAAI,GAAG,UAAU,GAAG,WAAW,CAAC;AAC1C,IAAI,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,UAAU,GAAG,IAAI,IAAI,WAAW,CAAC,CAAC;AACrF,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,EAAE,UAAU,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACxF,GAAG;AACH,EAAE,yBAAyB,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,SAAS,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC;AAC3I,EAAE,4BAA4B,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,SAAS,KAAK;AAC5F,IAAI,MAAM,GAAG,GAAG,UAAU,GAAG,SAAS,CAAC;AACvC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,SAAS,GAAG,GAAG,IAAI,SAAS,CAAC,CAAC;AAC7E,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,UAAU,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,SAAS,EAAE,MAAM,KAAK,CAAC;AACzB,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK;AACjD,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC/C,MAAM,IAAI,CAACK,cAAQ,CAAC,WAAW,CAAC,EAAE;AAClC,QAAQC,gBAAU,CAAC,KAAK,EAAE,CAAC;AAC3B;AACA,oBAAoB,EAAE,OAAO,WAAW,CAAC;AACzC,QAAQ,CAAC,CAAC,CAAC;AACX,OAAO;AACP,MAAM,IAAI,CAACD,cAAQ,CAAC,SAAS,CAAC,EAAE;AAChC,QAAQC,gBAAU,CAAC,KAAK,EAAE,CAAC;AAC3B;AACA,oBAAoB,EAAE,OAAO,SAAS,CAAC;AACvC,QAAQ,CAAC,CAAC,CAAC;AACX,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;;;;"}