{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/statistic/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Statistic from './src/statistic.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElStatistic: SFCWithInstall<typeof Statistic> =\n  withInstall(Statistic)\n\nexport default ElStatistic\nexport * from './src/statistic'\n"], "names": ["withInstall", "Statistic"], "mappings": ";;;;;;;;AAEY,MAAC,WAAW,GAAGA,mBAAW,CAACC,sBAAS;;;;;;"}