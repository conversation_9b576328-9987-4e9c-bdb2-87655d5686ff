{"version": 3, "file": "page-header.js", "sources": ["../../../../../../packages/components/page-header/src/page-header.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport { Back } from '@element-plus/icons-vue'\nimport type { ExtractPropTypes } from 'vue'\nimport type PageHeader from './page-header.vue'\n\nexport const pageHeaderProps = buildProps({\n  /**\n   * @description icon component of page header\n   */\n  icon: {\n    type: iconPropType,\n    default: () => Back,\n  },\n  /**\n   * @description main title of page header\n   */\n  title: String,\n  /**\n   * @description content of page header\n   */\n  content: {\n    type: String,\n    default: '',\n  },\n} as const)\nexport type PageHeaderProps = ExtractPropTypes<typeof pageHeaderProps>\n\nexport const pageHeaderEmits = {\n  back: () => true,\n}\nexport type PageHeaderEmits = typeof pageHeaderEmits\n\nexport type PageHeaderInstance = InstanceType<typeof PageHeader> & unknown\n"], "names": ["buildProps", "iconPropType", "Back"], "mappings": ";;;;;;;;AAEY,MAAC,eAAe,GAAGA,kBAAU,CAAC;AAC1C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,iBAAY;AACtB,IAAI,OAAO,EAAE,MAAMC,aAAI;AACvB,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC,EAAE;AACS,MAAC,eAAe,GAAG;AAC/B,EAAE,IAAI,EAAE,MAAM,IAAI;AAClB;;;;;"}