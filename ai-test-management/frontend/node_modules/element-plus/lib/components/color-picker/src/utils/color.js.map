{"version": 3, "file": "color.js", "sources": ["../../../../../../../packages/components/color-picker/src/utils/color.ts"], "sourcesContent": ["import { hasOwn, isString } from '@element-plus/utils'\n\nconst hsv2hsl = function (hue: number, sat: number, val: number) {\n  return [\n    hue,\n    (sat * val) / ((hue = (2 - sat) * val) < 1 ? hue : 2 - hue) || 0,\n    hue / 2,\n  ]\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nconst isOnePointZero = function (n: unknown) {\n  return isString(n) && n.includes('.') && Number.parseFloat(n) === 1\n}\n\nconst isPercentage = function (n: unknown) {\n  return isString(n) && n.includes('%')\n}\n\n// Take input from [0, n] and return it as [0, 1]\nconst bound01 = function (value: number | string, max: number | string) {\n  if (isOnePointZero(value)) value = '100%'\n\n  const processPercent = isPercentage(value)\n  value = Math.min(max as number, Math.max(0, Number.parseFloat(`${value}`)))\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    value = Number.parseInt(`${value * (max as number)}`, 10) / 100\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(value - (max as number)) < 0.000001) {\n    return 1\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return (value % (max as number)) / Number.parseFloat(max as string)\n}\n\nconst INT_HEX_MAP: Record<string, string> = {\n  10: 'A',\n  11: 'B',\n  12: 'C',\n  13: 'D',\n  14: 'E',\n  15: 'F',\n}\n\nconst hexOne = (value: number) => {\n  value = Math.min(Math.round(value), 255)\n  const high = Math.floor(value / 16)\n  const low = value % 16\n  return `${INT_HEX_MAP[high] || high}${INT_HEX_MAP[low] || low}`\n}\n\nconst toHex = function ({ r, g, b }: { r: number; g: number; b: number }) {\n  if (Number.isNaN(+r) || Number.isNaN(+g) || Number.isNaN(+b)) return ''\n  return `#${hexOne(r)}${hexOne(g)}${hexOne(b)}`\n}\n\nconst HEX_INT_MAP: Record<string, number> = {\n  A: 10,\n  B: 11,\n  C: 12,\n  D: 13,\n  E: 14,\n  F: 15,\n}\n\nconst parseHexChannel = function (hex: string) {\n  if (hex.length === 2) {\n    return (\n      (HEX_INT_MAP[hex[0].toUpperCase()] || +hex[0]) * 16 +\n      (HEX_INT_MAP[hex[1].toUpperCase()] || +hex[1])\n    )\n  }\n\n  return HEX_INT_MAP[hex[1].toUpperCase()] || +hex[1]\n}\n\nconst hsl2hsv = function (hue: number, sat: number, light: number) {\n  sat = sat / 100\n  light = light / 100\n  let smin = sat\n  const lmin = Math.max(light, 0.01)\n  // let sv\n  // let v\n\n  light *= 2\n  sat *= light <= 1 ? light : 2 - light\n  smin *= lmin <= 1 ? lmin : 2 - lmin\n  const v = (light + sat) / 2\n  const sv =\n    light === 0 ? (2 * smin) / (lmin + smin) : (2 * sat) / (light + sat)\n\n  return {\n    h: hue,\n    s: sv * 100,\n    v: v * 100,\n  }\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nconst rgb2hsv = (r: number, g: number, b: number) => {\n  r = bound01(r, 255)\n  g = bound01(g, 255)\n  b = bound01(b, 255)\n\n  const max = Math.max(r, g, b)\n  const min = Math.min(r, g, b)\n  let h: number\n  const v = max\n\n  const d = max - min\n  const s = max === 0 ? 0 : d / max\n\n  if (max === min) {\n    h = 0 // achromatic\n  } else {\n    switch (max) {\n      case r: {\n        h = (g - b) / d + (g < b ? 6 : 0)\n        break\n      }\n      case g: {\n        h = (b - r) / d + 2\n        break\n      }\n      case b: {\n        h = (r - g) / d + 4\n        break\n      }\n    }\n    h! /= 6\n  }\n\n  return { h: h! * 360, s: s * 100, v: v * 100 }\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nconst hsv2rgb = function (h: number, s: number, v: number) {\n  h = bound01(h, 360) * 6\n  s = bound01(s, 100)\n  v = bound01(v, 100)\n\n  const i = Math.floor(h)\n  const f = h - i\n  const p = v * (1 - s)\n  const q = v * (1 - f * s)\n  const t = v * (1 - (1 - f) * s)\n  const mod = i % 6\n  const r = [v, q, p, p, t, v][mod]\n  const g = [t, v, v, q, p, p][mod]\n  const b = [p, p, t, v, v, q][mod]\n\n  return {\n    r: Math.round(r * 255),\n    g: Math.round(g * 255),\n    b: Math.round(b * 255),\n  }\n}\n\ninterface ColorOptions {\n  enableAlpha: boolean\n  format: string\n  value?: string\n}\n\nexport default class Color {\n  private _hue = 0\n  private _saturation = 100\n  private _value = 100\n  _alpha = 100\n  public enableAlpha = false\n  public format = 'hex'\n  public value = ''\n  public selected?: boolean\n\n  constructor(options: Partial<ColorOptions> = {}) {\n    for (const option in options) {\n      if (hasOwn(options, option)) {\n        this[option] = options[option]\n      }\n    }\n    if (options.value) {\n      this.fromString(options.value)\n    } else {\n      this.doOnChange()\n    }\n  }\n\n  set(prop: { [key: string]: any } | any, value?: number) {\n    if (arguments.length === 1 && typeof prop === 'object') {\n      for (const p in prop) {\n        if (hasOwn(prop, p)) {\n          this.set(p, prop[p])\n        }\n      }\n\n      return\n    }\n\n    ;(this as any)[`_${prop}`] = value\n    this.doOnChange()\n  }\n\n  get(prop: string) {\n    if (prop === 'alpha') {\n      return Math.floor(this[`_${prop}`])\n    }\n    return (this as any)[`_${prop}`]\n  }\n\n  toRgb() {\n    return hsv2rgb(this._hue, this._saturation, this._value)\n  }\n\n  fromString(value: string) {\n    if (!value) {\n      this._hue = 0\n      this._saturation = 100\n      this._value = 100\n\n      this.doOnChange()\n      return\n    }\n\n    const fromHSV = (h: number, s: number, v: number) => {\n      this._hue = Math.max(0, Math.min(360, h))\n      this._saturation = Math.max(0, Math.min(100, s))\n      this._value = Math.max(0, Math.min(100, v))\n\n      this.doOnChange()\n    }\n\n    if (value.includes('hsl')) {\n      const parts = value\n        .replace(/hsla|hsl|\\(|\\)/gm, '')\n        .split(/\\s|,/g)\n        .filter((val) => val !== '')\n        .map((val, index) =>\n          index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10)\n        )\n\n      if (parts.length === 4) {\n        // @ts-expect-error\n        this._alpha = Number.parseFloat(parts[3]) * 100\n      } else if (parts.length === 3) {\n        this._alpha = 100\n      }\n      if (parts.length >= 3) {\n        const { h, s, v } = hsl2hsv(parts[0], parts[1], parts[2])\n        fromHSV(h, s, v)\n      }\n    } else if (value.includes('hsv')) {\n      const parts = value\n        .replace(/hsva|hsv|\\(|\\)/gm, '')\n        .split(/\\s|,/g)\n        .filter((val) => val !== '')\n        .map((val, index) =>\n          index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10)\n        )\n\n      if (parts.length === 4) {\n        // @ts-expect-error\n        this._alpha = Number.parseFloat(parts[3]) * 100\n      } else if (parts.length === 3) {\n        this._alpha = 100\n      }\n      if (parts.length >= 3) {\n        fromHSV(parts[0], parts[1], parts[2])\n      }\n    } else if (value.includes('rgb')) {\n      const parts = value\n        .replace(/rgba|rgb|\\(|\\)/gm, '')\n        .split(/\\s|,/g)\n        .filter((val) => val !== '')\n        .map((val, index) =>\n          index > 2 ? Number.parseFloat(val) : Number.parseInt(val, 10)\n        )\n\n      if (parts.length === 4) {\n        // @ts-expect-error\n        this._alpha = Number.parseFloat(parts[3]) * 100\n      } else if (parts.length === 3) {\n        this._alpha = 100\n      }\n      if (parts.length >= 3) {\n        const { h, s, v } = rgb2hsv(parts[0], parts[1], parts[2])\n        fromHSV(h, s, v)\n      }\n    } else if (value.includes('#')) {\n      const hex = value.replace('#', '').trim()\n      if (!/^[0-9a-fA-F]{3}$|^[0-9a-fA-F]{6}$|^[0-9a-fA-F]{8}$/.test(hex))\n        return\n      let r: number, g: number, b: number\n\n      if (hex.length === 3) {\n        r = parseHexChannel(hex[0] + hex[0])\n        g = parseHexChannel(hex[1] + hex[1])\n        b = parseHexChannel(hex[2] + hex[2])\n      } else if (hex.length === 6 || hex.length === 8) {\n        r = parseHexChannel(hex.slice(0, 2))\n        g = parseHexChannel(hex.slice(2, 4))\n        b = parseHexChannel(hex.slice(4, 6))\n      }\n\n      if (hex.length === 8) {\n        this._alpha = (parseHexChannel(hex.slice(6)) / 255) * 100\n      } else if (hex.length === 3 || hex.length === 6) {\n        this._alpha = 100\n      }\n\n      const { h, s, v } = rgb2hsv(r!, g!, b!)\n      fromHSV(h, s, v)\n    }\n  }\n\n  compare(color: this) {\n    return (\n      Math.abs(color._hue - this._hue) < 2 &&\n      Math.abs(color._saturation - this._saturation) < 1 &&\n      Math.abs(color._value - this._value) < 1 &&\n      Math.abs(color._alpha - this._alpha) < 1\n    )\n  }\n\n  doOnChange() {\n    const { _hue, _saturation, _value, _alpha, format } = this\n\n    if (this.enableAlpha) {\n      switch (format) {\n        case 'hsl': {\n          const hsl = hsv2hsl(_hue, _saturation / 100, _value / 100)\n          this.value = `hsla(${_hue}, ${Math.round(\n            hsl[1] * 100\n          )}%, ${Math.round(hsl[2] * 100)}%, ${this.get('alpha') / 100})`\n          break\n        }\n        case 'hsv': {\n          this.value = `hsva(${_hue}, ${Math.round(_saturation)}%, ${Math.round(\n            _value\n          )}%, ${this.get('alpha') / 100})`\n          break\n        }\n        case 'hex': {\n          this.value = `${toHex(hsv2rgb(_hue, _saturation, _value))}${hexOne(\n            (_alpha * 255) / 100\n          )}`\n          break\n        }\n        default: {\n          const { r, g, b } = hsv2rgb(_hue, _saturation, _value)\n          this.value = `rgba(${r}, ${g}, ${b}, ${this.get('alpha') / 100})`\n        }\n      }\n    } else {\n      switch (format) {\n        case 'hsl': {\n          const hsl = hsv2hsl(_hue, _saturation / 100, _value / 100)\n          this.value = `hsl(${_hue}, ${Math.round(hsl[1] * 100)}%, ${Math.round(\n            hsl[2] * 100\n          )}%)`\n          break\n        }\n        case 'hsv': {\n          this.value = `hsv(${_hue}, ${Math.round(_saturation)}%, ${Math.round(\n            _value\n          )}%)`\n          break\n        }\n        case 'rgb': {\n          const { r, g, b } = hsv2rgb(_hue, _saturation, _value)\n          this.value = `rgb(${r}, ${g}, ${b})`\n          break\n        }\n        default: {\n          this.value = toHex(hsv2rgb(_hue, _saturation, _value))\n        }\n      }\n    }\n  }\n}\n"], "names": ["isString", "hasOwn"], "mappings": ";;;;;;AACA,MAAM,OAAO,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC,EAAE,OAAO;AACT,IAAI,GAAG;AACP,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AAClE,IAAI,GAAG,GAAG,CAAC;AACX,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,SAAS,CAAC,EAAE;AACnC,EAAE,OAAOA,eAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACtE,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,SAAS,CAAC,EAAE;AACjC,EAAE,OAAOA,eAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACxC,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,SAAS,KAAK,EAAE,GAAG,EAAE;AACrC,EAAE,IAAI,cAAc,CAAC,KAAK,CAAC;AAC3B,IAAI,KAAK,GAAG,MAAM,CAAC;AACnB,EAAE,MAAM,cAAc,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AAC7C,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;AACxD,GAAG;AACH,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE;AACpC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,EAAE,OAAO,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC9C,CAAC,CAAC;AACF,MAAM,WAAW,GAAG;AACpB,EAAE,EAAE,EAAE,GAAG;AACT,EAAE,EAAE,EAAE,GAAG;AACT,EAAE,EAAE,EAAE,GAAG;AACT,EAAE,EAAE,EAAE,GAAG;AACT,EAAE,EAAE,EAAE,GAAG;AACT,EAAE,EAAE,EAAE,GAAG;AACT,CAAC,CAAC;AACF,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK;AAC1B,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3C,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AACtC,EAAE,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC;AACzB,EAAE,OAAO,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAClE,CAAC,CAAC;AACF,MAAM,KAAK,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;AACpC,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9D,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC;AACF,MAAM,WAAW,GAAG;AACpB,EAAE,CAAC,EAAE,EAAE;AACP,EAAE,CAAC,EAAE,EAAE;AACP,EAAE,CAAC,EAAE,EAAE;AACP,EAAE,CAAC,EAAE,EAAE;AACP,EAAE,CAAC,EAAE,EAAE;AACP,EAAE,CAAC,EAAE,EAAE;AACP,CAAC,CAAC;AACF,MAAM,eAAe,GAAG,SAAS,GAAG,EAAE;AACtC,EAAE,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChH,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;AAC1C,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAClB,EAAE,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC;AACtB,EAAE,IAAI,IAAI,GAAG,GAAG,CAAC;AACjB,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,EAAE,KAAK,IAAI,CAAC,CAAC;AACb,EAAE,GAAG,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;AACxC,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AACtC,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC;AAC9E,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,GAAG;AACV,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG;AACf,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACd,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC7B,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC;AAChB,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACtB,EAAE,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACpC,EAAE,IAAI,GAAG,KAAK,GAAG,EAAE;AACnB,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,GAAG,MAAM;AACT,IAAI,QAAQ,GAAG;AACf,MAAM,KAAK,CAAC,EAAE;AACd,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1C,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,KAAK,CAAC,EAAE;AACd,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,KAAK,CAAC,EAAE;AACd,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG;AACH,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AAChD,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAClC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1B,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACxB,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAClC,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACpC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACpC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACpC,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAC1B,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAC1B,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAC1B,GAAG,CAAC;AACJ,CAAC,CAAC;AACa,MAAM,KAAK,CAAC;AAC3B,EAAE,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACtB,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACtB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAClC,MAAM,IAAIC,aAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AACvB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AACxB,KAAK;AACL,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE;AACnB,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5D,MAAM,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;AAC5B,QAAQ,IAAIA,aAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;AAC7B,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,SAAS;AACT,OAAO;AACP,MAAM,OAAO;AACb,KAAK;AAEL,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE;AACZ,IAAI,IAAI,IAAI,KAAK,OAAO,EAAE;AAC1B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACpB,MAAM,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AAC7B,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AACxB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AACjC,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAChD,MAAM,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACvD,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AACxB,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC/B,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACxL,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACxD,OAAO,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACrC,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AAC7B,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,QAAQ,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,OAAO;AACP,KAAK,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACtC,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACxL,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACxD,OAAO,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACrC,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AAC7B,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,OAAO;AACP,KAAK,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACtC,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACxL,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACxD,OAAO,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACrC,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AAC7B,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,QAAQ,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,OAAO;AACP,KAAK,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACpC,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAChD,MAAM,IAAI,CAAC,oDAAoD,CAAC,IAAI,CAAC,GAAG,CAAC;AACzE,QAAQ,OAAO;AACf,MAAM,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,QAAQ,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,QAAQ,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,QAAQ,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,OAAO,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACvD,QAAQ,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7C,QAAQ,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7C,QAAQ,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7C,OAAO;AACP,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,QAAQ,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AAChE,OAAO,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACvD,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B,OAAO;AACP,MAAM,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,KAAK,EAAE;AACjB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9L,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AAC/D,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAC1B,MAAM,QAAQ,MAAM;AACpB,QAAQ,KAAK,KAAK,EAAE;AACpB,UAAU,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,CAAC;AACrE,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/H,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,KAAK,KAAK,EAAE;AACpB,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACxH,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,KAAK,KAAK,EAAE;AACpB,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnG,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,SAAS;AACjB,UAAU,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;AACjE,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5E,SAAS;AACT,OAAO;AACP,KAAK,MAAM;AACX,MAAM,QAAQ,MAAM;AACpB,QAAQ,KAAK,KAAK,EAAE;AACpB,UAAU,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,CAAC;AACrE,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAClG,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,KAAK,KAAK,EAAE;AACpB,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC3F,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,KAAK,KAAK,EAAE;AACpB,UAAU,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;AACjE,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,SAAS;AACjB,UAAU,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;AACjE,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;;;;"}