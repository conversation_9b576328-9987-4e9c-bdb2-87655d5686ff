import type { CSSProperties } from 'vue';
declare const _default: import("vue").DefineComponent<{
    side: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    width: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    height: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown, null, boolean>;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    side: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./common").TooltipV2Sides) | (() => import("./common").TooltipV2Sides) | ((new (...args: any[]) => import("./common").TooltipV2Sides) | (() => import("./common").TooltipV2Sides))[], import("./common").TooltipV2Sides, unknown>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    width: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    height: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown, null, boolean>;
}>>, {
    style: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown>;
    height: number;
    width: number;
}>;
export default _default;
