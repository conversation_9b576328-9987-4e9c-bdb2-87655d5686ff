{"version": 3, "file": "root2.js", "sources": ["../../../../../../packages/components/tooltip-v2/src/root.vue"], "sourcesContent": ["<template>\n  <slot :open=\"open\" />\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { isNumber, isPropAbsent } from '@element-plus/utils'\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants'\nimport { tooltipV2RootProps } from './root'\n\ndefineOptions({\n  name: 'ElTooltipV2Root',\n})\n\nconst props = defineProps(tooltipV2RootProps)\n\n/**\n * internal open state, when no model value was provided, use this as indicator instead\n */\nconst _open = ref(props.defaultOpen)\nconst triggerRef = ref<HTMLElement | null>(null)\n\nconst open = computed<boolean>({\n  get: () => (isPropAbsent(props.open) ? _open.value : props.open),\n  set: (open) => {\n    _open.value = open\n    props['onUpdate:open']?.(open)\n  },\n})\n\nconst isOpenDelayed = computed(\n  () => isNumber(props.delayDuration) && props.delayDuration > 0\n)\n\nconst { start: onDelayedOpen, stop: clearTimer } = useTimeoutFn(\n  () => {\n    open.value = true\n  },\n  computed(() => props.delayDuration),\n  {\n    immediate: false,\n  }\n)\n\nconst ns = useNamespace('tooltip-v2')\n\nconst contentId = useId()\n\nconst onNormalOpen = () => {\n  clearTimer()\n  open.value = true\n}\n\nconst onDelayOpen = () => {\n  unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen()\n}\n\nconst onOpen = onNormalOpen\n\nconst onClose = () => {\n  clearTimer()\n  open.value = false\n}\n\nconst onChange = (open: boolean) => {\n  if (open) {\n    document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN))\n    onOpen()\n  }\n\n  props.onOpenChange?.(open)\n}\n\nwatch(open, onChange)\n\nonMounted(() => {\n  // Keeps only 1 tooltip open at a time\n  document.addEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nonBeforeUnmount(() => {\n  clearTimer()\n  document.removeEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nprovide(tooltipV2RootKey, {\n  contentId,\n  triggerRef,\n  ns,\n\n  onClose,\n  onDelayOpen,\n  onOpen,\n})\n\ndefineExpose({\n  /**\n   * @description open tooltip programmatically\n   */\n  onOpen,\n\n  /**\n   * @description close tooltip programmatically\n   */\n  onClose,\n})\n</script>\n"], "names": ["ref", "computed", "isPropAbsent", "open", "isNumber", "useTimeoutFn", "useNamespace", "useId", "unref", "TOOLTIP_V2_OPEN", "watch", "onMounted", "onBeforeUnmount", "provide", "tooltipV2RootKey", "_renderSlot", "_unref", "_export_sfc"], "mappings": ";;;;;;;;;;;;;uCAoBc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAOA,IAAM,MAAA,KAAA,GAAQA,OAAI,CAAA,KAAA,CAAM,WAAW,CAAA,CAAA;AACnC,IAAM,MAAA,UAAA,GAAaA,QAAwB,IAAI,CAAA,CAAA;AAE/C,IAAA,MAAM,OAAOC,YAAkB,CAAA;AAAA,MAC7B,GAAA,EAAK,MAAOC,kBAAa,CAAA,KAAA,CAAM,IAAI,CAAI,GAAA,KAAA,CAAM,QAAQ,KAAM,CAAA,IAAA;AAAA,MAC3D,GAAA,EAAK,CAACC,KAAS,KAAA;AACb,QAAA,IAAA,EAAM,CAAQA;AACd,QAAM,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAuB,QAC/B,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AAAA,OACD;AAED,KAAA,CAAA,CAAA;AAAsB,IAAA,mBACL,GAAAF,YAAmB,CAAA,MAAAG,oBAA2B,CAAA,aAAA,CAAA,IAAA,KAAA,CAAA,aAAA,GAAA,CAAA,CAAA,CAAA;AAAA,IAC/D,MAAA,EAAA,KAAA,EAAA,aAAA,EAAA,IAAA,EAAA,UAAA,EAAA,GAAAC,iBAAA,CAAA,MAAA;AAEA,MAAA,IAAM,CAAE,KAAA,GAAO,IAAe,CAAA;AAAqB,KAAA,EAC3CJ,YAAA,CAAA,MAAA,KAAA,CAAA,aAAA,CAAA,EAAA;AACJ,MAAA,SAAa,EAAA,KAAA;AAAA,KACf,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,GAASK,kBAAY,CAAa,YAAA,CAAA,CAAA;AAAA,IAClC,MAAA,SAAA,GAAAC,aAAA,EAAA,CAAA;AAAA,IAAA,MACa,YAAA,GAAA,MAAA;AAAA,MACb,UAAA,EAAA,CAAA;AAAA,MACF,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,KAAM,CAAA;AAEN,IAAA,MAAM,cAAkB,MAAA;AAExB,MAAAC,uBAAqB,CAAM,GAAA,aAAA,EAAA,GAAA,YAAA,EAAA,CAAA;AACzB,KAAW,CAAA;AACX,IAAA,MAAA,MAAa,GAAA,YAAA,CAAA;AAAA,IACf,MAAA,OAAA,GAAA,MAAA;AAEA,MAAA;AACE,MAAA,IAAA,CAAA,KAAmB,GAAA,KAAA,CAAA;AAAmC,KACxD,CAAA;AAEA,IAAA,MAAM,QAAS,GAAA,CAAA,KAAA,KAAA;AAEf,MAAA,IAAM;AACJ,MAAW,IAAA,KAAA,EAAA;AACX,QAAA,QAAa,CAAA,aAAA,CAAA,IAAA,WAAA,CAAAC,yBAAA,CAAA,CAAA,CAAA;AAAA,QACf,MAAA,EAAA,CAAA;AAEA,OAAM;AACJ,MAAA,CAAA,EAAA,GAAU,KAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AACR,KAAA,CAAA;AACA,IAAOC,SAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AAAA,IACTC,aAAA,CAAA,MAAA;AAEA,MAAA,yBAAyB,CAAAF,yBAAA,EAAA,OAAA,CAAA,CAAA;AAAA,KAC3B,CAAA,CAAA;AAEA,IAAAG,mBAAoB,CAAA,MAAA;AAEpB,MAAA,UAAgB,EAAA,CAAA;AAEd,MAAS,QAAA,CAAA,6CAAyC,EAAA,OAAA,CAAA,CAAA;AAAA,KACnD,CAAA,CAAA;AAED,IAAAC,WAAA,CAAAC,0BAAsB,EAAA;AACpB,MAAW,SAAA;AACX,MAAS,UAAA;AAA4C,MACtD,EAAA;AAED,MAAA,OAA0B;AAAA,MACxB,WAAA;AAAA,MACA,MAAA;AAAA,KACA,CAAA,CAAA;AAAA,IAEA,MAAA,CAAA;AAAA,MACA,MAAA;AAAA,MACA,OAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAAC,SAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,GAAA;AAAA,CAIX,CAAA,CAAA;AAAA,oBAAA,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;;;;"}