{"version": 3, "file": "alert.js", "sources": ["../../../../../../packages/components/alert/src/alert.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"visible\"\n      :class=\"[ns.b(), ns.m(type), ns.is('center', center), ns.is(effect)]\"\n      role=\"alert\"\n    >\n      <el-icon\n        v-if=\"showIcon && ($slots.icon || iconComponent)\"\n        :class=\"[ns.e('icon'), { [ns.is('big')]: hasDesc }]\"\n      >\n        <slot name=\"icon\">\n          <component :is=\"iconComponent\" />\n        </slot>\n      </el-icon>\n\n      <div :class=\"ns.e('content')\">\n        <span\n          v-if=\"title || $slots.title\"\n          :class=\"[ns.e('title'), { 'with-description': hasDesc }]\"\n        >\n          <slot name=\"title\">{{ title }}</slot>\n        </span>\n        <p v-if=\"hasDesc\" :class=\"ns.e('description')\">\n          <slot>\n            {{ description }}\n          </slot>\n        </p>\n        <template v-if=\"closable\">\n          <div\n            v-if=\"closeText\"\n            :class=\"[ns.e('close-btn'), ns.is('customed')]\"\n            @click=\"close\"\n          >\n            {{ closeText }}\n          </div>\n          <el-icon v-else :class=\"ns.e('close-btn')\" @click=\"close\">\n            <Close />\n          </el-icon>\n        </template>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, useSlots } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { TypeComponents, TypeComponentsMap } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { alertEmits, alertProps } from './alert'\n\nconst { Close } = TypeComponents\n\ndefineOptions({\n  name: 'ElAlert',\n})\n\nconst props = defineProps(alertProps)\nconst emit = defineEmits(alertEmits)\nconst slots = useSlots()\n\nconst ns = useNamespace('alert')\n\nconst visible = ref(true)\n\nconst iconComponent = computed(() => TypeComponentsMap[props.type])\n\nconst hasDesc = computed(() => !!(props.description || slots.default))\n\nconst close = (evt: MouseEvent) => {\n  visible.value = false\n  emit('close', evt)\n}\n</script>\n"], "names": ["TypeComponents", "useSlots", "useNamespace", "ref", "computed", "TypeComponentsMap"], "mappings": ";;;;;;;;;;;uCAsDc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAJA,IAAM,MAAA,EAAE,OAAU,GAAAA,mBAAA,CAAA;AAQlB,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAA,GAAKC,mBAAa,OAAO,CAAA,CAAA;AAE/B,IAAM,MAAA,OAAA,GAAUC,QAAI,IAAI,CAAA,CAAA;AAExB,IAAA,MAAM,gBAAgBC,YAAS,CAAA,MAAMC,sBAAkB,CAAA,KAAA,CAAM,IAAI,CAAC,CAAA,CAAA;AAElE,IAAM,MAAA,OAAA,GAAUD,aAAS,MAAM,CAAC,EAAE,KAAM,CAAA,WAAA,IAAe,MAAM,OAAQ,CAAA,CAAA,CAAA;AAErE,IAAM,MAAA,KAAA,GAAQ,CAAC,GAAoB,KAAA;AACjC,MAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA,CAAA;AAChB,MAAA,IAAA,CAAK,SAAS,GAAG,CAAA,CAAA;AAAA,KACnB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}