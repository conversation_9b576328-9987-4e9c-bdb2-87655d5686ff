{"version": 3, "file": "tree-node-content.js", "sources": ["../../../../../../packages/components/tree/src/tree-node-content.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, h, inject, renderSlot } from 'vue'\nimport ElText from '@element-plus/components/text'\n\nimport { useNamespace } from '@element-plus/hooks'\nimport type { ComponentInternalInstance } from 'vue'\nimport type { RootTreeType } from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTreeNodeContent',\n  props: {\n    node: {\n      type: Object,\n      required: true,\n    },\n    renderContent: Function,\n  },\n  setup(props) {\n    const ns = useNamespace('tree')\n    const nodeInstance = inject<ComponentInternalInstance>('NodeInstance')\n    const tree = inject<RootTreeType>('RootTree')!\n    return () => {\n      const node = props.node\n      const { data, store } = node\n      return props.renderContent\n        ? props.renderContent(h, { _self: nodeInstance, node, data, store })\n        : renderSlot(tree.ctx.slots, 'default', { node, data }, () => [\n            h(\n              ElText,\n              { tag: 'span', truncated: true, class: ns.be('node', 'label') },\n              () => [node.label]\n            ),\n          ])\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "useNamespace", "inject", "h", "renderSlot", "ElText", "_export_sfc"], "mappings": ";;;;;;;;;AAQA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,mBAAA;AAAA,EACN,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,aAAe,EAAA,QAAA;AAAA,GACjB;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAKC,mBAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,YAAA,GAAeC,WAAkC,cAAc,CAAA,CAAA;AACrE,IAAM,MAAA,IAAA,GAAOA,WAAqB,UAAU,CAAA,CAAA;AAC5C,IAAA,OAAO,MAAM;AACX,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AACnB,MAAM,MAAA,EAAE,IAAM,EAAA,KAAA,EAAU,GAAA,IAAA,CAAA;AACxB,MAAO,OAAA,KAAA,CAAM,gBACT,KAAM,CAAA,aAAA,CAAcC,OAAG,EAAE,KAAA,EAAO,YAAc,EAAA,IAAA,EAAM,IAAM,EAAA,KAAA,EAAO,CACjE,GAAAC,cAAA,CAAW,KAAK,GAAI,CAAA,KAAA,EAAO,WAAW,EAAE,IAAA,EAAM,IAAK,EAAA,EAAG,MAAM;AAAA,QAC1DD,KAAA,CAAAE,cAAA,EAAA,EAAA,GAAA,EAAA,MAAA,EAAA,SAAA,EAAA,IAAA,EAAA,KAAA,EAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACE,CAAA,CAAA;AAAA,KACA,CAAA;AAA8D,GAC9D;AAAiB,CACnB,CAAA,CAAA;AACD,kBACP,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,uBAAA,CAAA,CAAA,CAAA;;;;"}