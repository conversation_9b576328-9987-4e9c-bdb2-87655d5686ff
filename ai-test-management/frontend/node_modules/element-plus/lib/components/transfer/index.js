'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var transfer$1 = require('./src/transfer2.js');
var transfer = require('./src/transfer.js');
var install = require('../../utils/vue/install.js');

const ElTransfer = install.withInstall(transfer$1["default"]);

exports.LEFT_CHECK_CHANGE_EVENT = transfer.LEFT_CHECK_CHANGE_EVENT;
exports.RIGHT_CHECK_CHANGE_EVENT = transfer.RIGHT_CHECK_CHANGE_EVENT;
exports.transferCheckedChangeFn = transfer.transferCheckedChangeFn;
exports.transferEmits = transfer.transferEmits;
exports.transferProps = transfer.transferProps;
exports.ElTransfer = ElTransfer;
exports["default"] = ElTransfer;
//# sourceMappingURL=index.js.map
