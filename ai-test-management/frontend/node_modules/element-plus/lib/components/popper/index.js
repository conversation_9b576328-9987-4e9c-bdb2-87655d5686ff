'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var popper$1 = require('./src/popper2.js');
var arrow = require('./src/arrow2.js');
var trigger = require('./src/trigger2.js');
var content = require('./src/content2.js');
var popper = require('./src/popper.js');
var trigger$1 = require('./src/trigger.js');
var content$1 = require('./src/content.js');
var arrow$1 = require('./src/arrow.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElPopper = install.withInstall(popper$1["default"]);

exports.ElPopperArrow = arrow["default"];
exports.ElPopperTrigger = trigger["default"];
exports.ElPopperContent = content["default"];
exports.Effect = popper.Effect;
exports.popperProps = popper.popperProps;
exports.roleTypes = popper.roleTypes;
exports.usePopperProps = popper.usePopperProps;
exports.popperTriggerProps = trigger$1.popperTriggerProps;
exports.usePopperTriggerProps = trigger$1.usePopperTriggerProps;
exports.popperContentEmits = content$1.popperContentEmits;
exports.popperContentProps = content$1.popperContentProps;
exports.popperCoreConfigProps = content$1.popperCoreConfigProps;
exports.usePopperContentEmits = content$1.usePopperContentEmits;
exports.usePopperContentProps = content$1.usePopperContentProps;
exports.usePopperCoreConfigProps = content$1.usePopperCoreConfigProps;
exports.popperArrowProps = arrow$1.popperArrowProps;
exports.usePopperArrowProps = arrow$1.usePopperArrowProps;
exports.POPPER_CONTENT_INJECTION_KEY = constants.POPPER_CONTENT_INJECTION_KEY;
exports.POPPER_INJECTION_KEY = constants.POPPER_INJECTION_KEY;
exports.ElPopper = ElPopper;
exports["default"] = ElPopper;
//# sourceMappingURL=index.js.map
