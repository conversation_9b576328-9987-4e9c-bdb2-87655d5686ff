{"version": 3, "file": "use-focus-trap.js", "sources": ["../../../../../../../packages/components/popper/src/composables/use-focus-trap.ts"], "sourcesContent": ["import { ref } from 'vue'\n\nimport type { SetupContext } from 'vue'\nimport type { PopperContentEmits, PopperContentProps } from '../content'\n\nexport const usePopperContentFocusTrap = (\n  props: PopperContentProps,\n  emit: SetupContext<PopperContentEmits>['emit']\n) => {\n  const trapped = ref(false)\n  const focusStartRef = ref<'container' | 'first' | HTMLElement>()\n\n  const onFocusAfterTrapped = () => {\n    emit('focus')\n  }\n\n  const onFocusAfterReleased = (event: CustomEvent) => {\n    if (event.detail?.focusReason !== 'pointer') {\n      focusStartRef.value = 'first'\n      emit('blur')\n    }\n  }\n\n  const onFocusInTrap = (event: FocusEvent) => {\n    if (props.visible && !trapped.value) {\n      if (event.target) {\n        focusStartRef.value = event.target as typeof focusStartRef.value\n      }\n      trapped.value = true\n    }\n  }\n\n  const onFocusoutPrevented = (event: CustomEvent) => {\n    if (!props.trapping) {\n      if (event.detail.focusReason === 'pointer') {\n        event.preventDefault()\n      }\n      trapped.value = false\n    }\n  }\n\n  const onReleaseRequested = () => {\n    trapped.value = false\n    emit('close')\n  }\n\n  return {\n    focusStartRef,\n    trapped,\n\n    onFocusAfterReleased,\n    onFocusAfterTrapped,\n    onFocusInTrap,\n    onFocusoutPrevented,\n    onReleaseRequested,\n  }\n}\n\nexport type UsePopperContentFocusTrapReturn = ReturnType<\n  typeof usePopperContentFocusTrap\n>\n"], "names": ["ref"], "mappings": ";;;;;;AACY,MAAC,yBAAyB,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAC1D,EAAE,MAAM,OAAO,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE,MAAM,aAAa,GAAGA,OAAG,EAAE,CAAC;AAC9B,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,KAAK;AAC1C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,MAAM,SAAS,EAAE;AAC/E,MAAM,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC;AACpC,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;AACnB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AACzC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;AACxB,QAAQ,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;AAC3C,OAAO;AACP,MAAM,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,CAAC,KAAK,KAAK;AACzC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE;AAClD,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,OAAO;AACP,MAAM,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC5B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,MAAM;AACnC,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,mBAAmB;AACvB,IAAI,kBAAkB;AACtB,GAAG,CAAC;AACJ;;;;"}