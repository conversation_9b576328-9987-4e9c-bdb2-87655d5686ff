{"version": 3, "file": "options.js", "sources": ["../../../../../../packages/components/select/src/options.ts"], "sourcesContent": ["import { defineComponent, inject } from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { isArray, isFunction, isString } from '@element-plus/utils'\nimport { selectKey } from './token'\n\nimport type { Component, VNode, VNodeNormalizedChildren } from 'vue'\nimport type { OptionValue } from './type'\n\nexport default defineComponent({\n  name: 'ElOptions',\n  setup(_, { slots }) {\n    const select = inject(selectKey)\n    let cachedValueList: OptionValue[] = []\n\n    return () => {\n      const children = slots.default?.()!\n      const valueList: OptionValue[] = []\n\n      function filterOptions(children?: VNodeNormalizedChildren) {\n        if (!isArray(children)) return\n        ;(children as VNode[]).forEach((item) => {\n          const name = ((item?.type || {}) as Component)?.name\n\n          if (name === 'ElOptionGroup') {\n            filterOptions(\n              !isString(item.children) &&\n                !isArray(item.children) &&\n                isFunction(item.children?.default)\n                ? item.children?.default()\n                : item.children\n            )\n          } else if (name === 'ElOption') {\n            valueList.push(item.props?.value)\n          } else if (isArray(item.children)) {\n            filterOptions(item.children)\n          }\n        })\n      }\n\n      if (children.length) {\n        filterOptions(children[0]?.children)\n      }\n\n      if (!isEqual(valueList, cachedValueList)) {\n        cachedValueList = valueList\n        if (select) {\n          select.states.optionValues = valueList\n        }\n      }\n\n      return children\n    }\n  },\n})\n"], "names": ["defineComponent", "inject", "<PERSON><PERSON><PERSON>", "isArray", "isString", "isFunction", "isEqual"], "mappings": ";;;;;;;;;AAIA,gBAAeA,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;AACtB,IAAI,MAAM,MAAM,GAAGC,UAAM,CAACC,eAAS,CAAC,CAAC;AACrC,IAAI,IAAI,eAAe,GAAG,EAAE,CAAC;AAC7B,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9E,MAAM,MAAM,SAAS,GAAG,EAAE,CAAC;AAC3B,MAAM,SAAS,aAAa,CAAC,SAAS,EAAE;AACxC,QAAQ,IAAI,CAACC,cAAO,CAAC,SAAS,CAAC;AAC/B,UAAU,OAAO;AACjB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACpC,UAAU,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B,UAAU,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;AACrG,UAAU,IAAI,IAAI,KAAK,eAAe,EAAE;AACxC,YAAY,aAAa,CAAC,CAACC,eAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAACD,cAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAIE,iBAAU,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1N,WAAW,MAAM,IAAI,IAAI,KAAK,UAAU,EAAE;AAC1C,YAAY,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAC1E,WAAW,MAAM,IAAIF,cAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC7C,YAAY,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B,QAAQ,aAAa,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC;AACzE,OAAO;AACP,MAAM,IAAI,CAACG,qBAAO,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;AAChD,QAAQ,eAAe,GAAG,SAAS,CAAC;AACpC,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,MAAM,CAAC,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;AACjD,SAAS;AACT,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;"}