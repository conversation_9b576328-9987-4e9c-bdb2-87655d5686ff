'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var rate$1 = require('./src/rate2.js');
var rate = require('./src/rate.js');
var install = require('../../utils/vue/install.js');

const ElRate = install.withInstall(rate$1["default"]);

exports.rateEmits = rate.rateEmits;
exports.rateProps = rate.rateProps;
exports.ElRate = ElRate;
exports["default"] = ElRate;
//# sourceMappingURL=index.js.map
