{"version": 3, "file": "defaults.js", "sources": ["../../../../../../../packages/components/table/src/table-column/defaults.ts"], "sourcesContent": ["// @ts-nocheck\nimport type { ComponentInternalInstance, PropType, Ref, VNode } from 'vue'\nimport type { DefaultRow, Table } from '../table/defaults'\nimport type {\n  TableOverflowTooltipFormatter,\n  TableOverflowTooltipOptions,\n} from '../util'\n\ntype CI<T> = { column: TableColumnCtx<T>; $index: number }\n\ntype Filters = {\n  text: string\n  value: string\n}[]\n\ntype FilterMethods<T> = (value, row: T, column: TableColumnCtx<T>) => void\n\ntype ValueOf<T> = T[keyof T]\n\ninterface TableColumnCtx<T> {\n  id: string\n  realWidth: number\n  type: string\n  label: string\n  className: string\n  labelClassName: string\n  property: string\n  prop: string\n  width: string | number\n  minWidth: string | number\n  renderHeader: (data: CI<T>) => VNode\n  sortable: boolean | string\n  sortMethod: (a: T, b: T) => number\n  sortBy: string | ((row: T, index: number) => string) | string[]\n  resizable: boolean\n  columnKey: string\n  rawColumnKey: string\n  align: string\n  headerAlign: string\n  showOverflowTooltip?: boolean | TableOverflowTooltipOptions\n  tooltipFormatter?: TableOverflowTooltipFormatter<T>\n  fixed: boolean | string\n  formatter: (\n    row: T,\n    column: TableColumnCtx<T>,\n    cellValue,\n    index: number\n  ) => VNode | string\n  selectable: (row: T, index: number) => boolean\n  reserveSelection: boolean\n  filterMethod: FilterMethods<T>\n  filteredValue: string[]\n  filters: Filters\n  filterPlacement: string\n  filterMultiple: boolean\n  filterClassName: string\n  index: number | ((index: number) => number)\n  sortOrders: ('ascending' | 'descending' | null)[]\n  renderCell: (data: any) => void\n  colSpan: number\n  rowSpan: number\n  children: TableColumnCtx<T>[]\n  level: number\n  filterable: boolean | FilterMethods<T> | Filters\n  order: string\n  isColumnGroup: boolean\n  isSubColumn: boolean\n  columns: TableColumnCtx<T>[]\n  getColumnIndex: () => number\n  no: number\n  filterOpened?: boolean\n}\n\ninterface TableColumn<T> extends ComponentInternalInstance {\n  vnode: {\n    vParent: TableColumn<T> | Table<T>\n  } & VNode\n  vParent: TableColumn<T> | Table<T>\n  columnId: string\n  columnConfig: Ref<Partial<TableColumnCtx<T>>>\n}\n\nexport type { Filters, FilterMethods, TableColumnCtx, TableColumn, ValueOf }\n\nexport default {\n  /**\n   * @description type of the column. If set to `selection`, the column will display checkbox. If set to `index`, the column will display index of the row (staring from 1). If set to `expand`, the column will display expand icon\n   */\n  type: {\n    type: String,\n    default: 'default',\n  },\n  /**\n   * @description column label\n   */\n  label: String,\n  /**\n   * @description class name of cells in the column\n   */\n  className: String,\n  /**\n   * @description class name of the label of this column\n   */\n  labelClassName: String,\n  /**\n   * @description\n   */\n  property: String,\n  /**\n   * @description field name. You can also use its alias: `property`\n   */\n  prop: String,\n  /**\n   * @description column width\n   */\n  width: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column minimum width. Columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion\n   */\n  minWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description render function for table header of this column\n   */\n  renderHeader: Function as PropType<\n    TableColumnCtx<DefaultRow>['renderHeader']\n  >,\n  /**\n   * @description whether column can be sorted. Remote sorting can be done by setting this attribute to 'custom' and listening to the `sort-change` event of Table\n   */\n  sortable: {\n    type: [Boolean, String],\n    default: false,\n  },\n  /**\n   * @description sorting method, works when `sortable` is `true`. Should return a number, just like Array.sort\n   */\n  sortMethod: Function as PropType<TableColumnCtx<DefaultRow>['sortMethod']>,\n  /**\n   * @description specify which property to sort by, works when `sortable` is `true` and `sort-method` is `undefined`. If set to an Array, the column will sequentially sort by the next property if the previous one is equal\n   */\n  sortBy: [String, Function, Array] as PropType<\n    TableColumnCtx<DefaultRow>['sortBy']\n  >,\n  /**\n   * @description whether column width can be resized, works when `border` of `el-table` is `true`\n   */\n  resizable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description column's key. If you need to use the filter-change event, you need this attribute to identify which column is being filtered\n   */\n  columnKey: String,\n  /**\n   * @description alignment, the value should be 'left' \\/ 'center' \\/ 'right'\n   */\n  align: String,\n  /**\n   * @description alignment of the table header. If omitted, the value of the above `align` attribute will be applied, the value should be 'left' \\/ 'center' \\/ 'right'\n   */\n  headerAlign: String,\n  /**\n   * @description whether to hide extra content and show them in a tooltip when hovering on the cell\n   */\n  showOverflowTooltip: {\n    type: [Boolean, Object] as PropType<\n      TableColumnCtx<DefaultRow>['showOverflowTooltip']\n    >,\n    default: undefined,\n  },\n  /**\n   * @description function that formats cell tooltip content, works when `show-overflow-tooltip` is `true`\n   */\n  tooltipFormatter: Function as PropType<\n    TableColumnCtx<DefaultRow>['tooltipFormatter']\n  >,\n  /**\n   * @description whether column is fixed at left / right. Will be fixed at left if `true`\n   */\n  fixed: [Boolean, String],\n  /**\n   * @description function that formats cell content\n   */\n  formatter: Function as PropType<TableColumnCtx<DefaultRow>['formatter']>,\n  /**\n   * @description function that determines if a certain row can be selected, works when `type` is 'selection'\n   */\n  selectable: Function as PropType<TableColumnCtx<DefaultRow>['selectable']>,\n  /**\n   * @description whether to reserve selection after data refreshing, works when `type` is 'selection'. Note that `row-key` is required for this to work\n   */\n  reserveSelection: Boolean,\n  /**\n   * @description data filtering method. If `filter-multiple` is on, this method will be called multiple times for each row, and a row will display if one of the calls returns `true`\n   */\n  filterMethod: Function as PropType<\n    TableColumnCtx<DefaultRow>['filterMethod']\n  >,\n  /**\n   * @description filter value for selected data, might be useful when table header is rendered with `render-header`\n   */\n  filteredValue: Array as PropType<TableColumnCtx<DefaultRow>['filteredValue']>,\n  /**\n   * @description an array of data filtering options. For each element in this array, `text` and `value` are required\n   */\n  filters: Array as PropType<TableColumnCtx<DefaultRow>['filters']>,\n  /**\n   * @description placement for the filter dropdown\n   */\n  filterPlacement: String,\n  /**\n   * @description whether data filtering supports multiple options\n   */\n  filterMultiple: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description className for the filter dropdown\n   */\n  filterClassName: String,\n  /**\n   * @description customize indices for each row, works on columns with `type=index`\n   */\n  index: [Number, Function] as PropType<TableColumnCtx<DefaultRow>['index']>,\n  /**\n   * @description the order of the sorting strategies used when sorting the data, works when `sortable` is `true`. Accepts an array, as the user clicks on the header, the column is sorted in order of the elements in the array\n   */\n  sortOrders: {\n    type: Array as PropType<TableColumnCtx<DefaultRow>['sortOrders']>,\n    default: () => {\n      return ['ascending', 'descending', null]\n    },\n    validator: (val: TableColumnCtx<unknown>['sortOrders']) => {\n      return val.every((order: string) =>\n        ['ascending', 'descending', null].includes(order)\n      )\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,mBAAe;AACf,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,cAAc,EAAE,MAAM;AACxB,EAAE,QAAQ,EAAE,MAAM;AAClB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC3B,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;AACnC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC3B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,gBAAgB,EAAE,QAAQ;AAC5B,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC1B,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,aAAa,EAAE,KAAK;AACtB,EAAE,OAAO,EAAE,KAAK;AAChB,EAAE,eAAe,EAAE,MAAM;AACzB,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,eAAe,EAAE,MAAM;AACzB,EAAE,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC3B,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK;AACxB,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACrF,KAAK;AACL,GAAG;AACH,CAAC;;;;"}