{"version": 3, "file": "key-render-helper.js", "sources": ["../../../../../../../packages/components/table/src/table/key-render-helper.ts"], "sourcesContent": ["import { onMounted, onUnmounted, ref } from 'vue'\nimport type { Table } from './defaults'\n\nexport default function use<PERSON>eyRender(table: Table<[]>) {\n  const observer = ref<MutationObserver>()\n\n  const initWatchDom = () => {\n    const el = table.vnode.el\n    const columnsWrapper = (el as HTMLElement).querySelector('.hidden-columns')\n    const config = { childList: true, subtree: true }\n    const updateOrderFns = table.store.states.updateOrderFns\n    observer.value = new MutationObserver(() => {\n      updateOrderFns.forEach((fn: () => void) => fn())\n    })\n\n    observer.value.observe(columnsWrapper!, config)\n  }\n\n  onMounted(() => {\n    // fix https://github.com/element-plus/element-plus/issues/8528\n    initWatchDom()\n  })\n\n  onUnmounted(() => {\n    observer.value?.disconnect()\n  })\n}\n"], "names": ["ref", "onMounted", "onUnmounted"], "mappings": ";;;;;;AACe,SAAS,YAAY,CAAC,KAAK,EAAE;AAC5C,EAAE,MAAM,QAAQ,GAAGA,OAAG,EAAE,CAAC;AACzB,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;AAC9B,IAAI,MAAM,cAAc,GAAG,EAAE,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;AAC/D,IAAI,MAAM,MAAM,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACtD,IAAI,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC;AAC7D,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,gBAAgB,CAAC,MAAM;AAChD,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AAC3C,KAAK,CAAC,CAAC;AACP,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAI,YAAY,EAAE,CAAC;AACnB,GAAG,CAAC,CAAC;AACL,EAAEC,eAAW,CAAC,MAAM;AACpB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;AAC7D,GAAG,CAAC,CAAC;AACL;;;;"}