{"version": 3, "file": "expand.js", "sources": ["../../../../../../../packages/components/table/src/store/expand.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, ref } from 'vue'\nimport { getKeysMap, getRowIdentity, toggleRowStatus } from '../util'\n\nimport type { Ref } from 'vue'\nimport type { WatcherPropsData } from '.'\nimport type { Table } from '../table/defaults'\n\nfunction useExpand<T>(watcherData: WatcherPropsData<T>) {\n  const instance = getCurrentInstance() as Table<T>\n  const defaultExpandAll = ref(false)\n  const expandRows: Ref<T[]> = ref([])\n  const updateExpandRows = () => {\n    const data = watcherData.data.value || []\n    const rowKey = watcherData.rowKey.value\n    if (defaultExpandAll.value) {\n      expandRows.value = data.slice()\n    } else if (rowKey) {\n      // TODO：这里的代码可以优化\n      const expandRowsMap = getKeysMap(expandRows.value, rowKey)\n      expandRows.value = data.reduce((prev: T[], row: T) => {\n        const rowId = getRowIdentity(row, rowKey)\n        const rowInfo = expandRowsMap[rowId]\n        if (rowInfo) {\n          prev.push(row)\n        }\n        return prev\n      }, [])\n    } else {\n      expandRows.value = []\n    }\n  }\n\n  const toggleRowExpansion = (row: T, expanded?: boolean) => {\n    const changed = toggleRowStatus(expandRows.value, row, expanded)\n    if (changed) {\n      instance.emit('expand-change', row, expandRows.value.slice())\n    }\n  }\n\n  const setExpandRowKeys = (rowKeys: string[]) => {\n    instance.store.assertRowKey()\n    // TODO：这里的代码可以优化\n    const data = watcherData.data.value || []\n    const rowKey = watcherData.rowKey.value\n    const keysMap = getKeysMap(data, rowKey)\n    expandRows.value = rowKeys.reduce((prev: T[], cur: string) => {\n      const info = keysMap[cur]\n      if (info) {\n        prev.push(info.row)\n      }\n      return prev\n    }, [])\n  }\n\n  const isRowExpanded = (row: T): boolean => {\n    const rowKey = watcherData.rowKey.value\n    if (rowKey) {\n      const expandMap = getKeysMap(expandRows.value, rowKey)\n      return !!expandMap[getRowIdentity(row, rowKey)]\n    }\n    return expandRows.value.includes(row)\n  }\n  return {\n    updateExpandRows,\n    toggleRowExpansion,\n    setExpandRowKeys,\n    isRowExpanded,\n    states: {\n      expandRows,\n      defaultExpandAll,\n    },\n  }\n}\n\nexport default useExpand\n"], "names": ["getCurrentInstance", "ref", "getKeysMap", "getRowIdentity", "toggleRowStatus"], "mappings": ";;;;;;;AAEA,SAAS,SAAS,CAAC,WAAW,EAAE;AAChC,EAAE,MAAM,QAAQ,GAAGA,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,gBAAgB,GAAGC,OAAG,CAAC,KAAK,CAAC,CAAC;AACtC,EAAE,MAAM,UAAU,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AAC7B,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,IAAI,gBAAgB,CAAC,KAAK,EAAE;AAChC,MAAM,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AACtC,KAAK,MAAM,IAAI,MAAM,EAAE;AACvB,MAAM,MAAM,aAAa,GAAGC,eAAU,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACjE,MAAM,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK;AACpD,QAAQ,MAAM,KAAK,GAAGC,mBAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClD,QAAQ,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7C,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO,EAAE,EAAE,CAAC,CAAC;AACb,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;AAC5B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK;AAChD,IAAI,MAAM,OAAO,GAAGC,oBAAe,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;AACrE,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;AACpE,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,OAAO,KAAK;AACxC,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AAClC,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAGF,eAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC7C,IAAI,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK;AACrD,MAAM,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK;AACjC,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,SAAS,GAAGA,eAAU,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7D,MAAM,OAAO,CAAC,CAAC,SAAS,CAACC,mBAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,OAAO,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC1C,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,MAAM,EAAE;AACZ,MAAM,UAAU;AAChB,MAAM,gBAAgB;AACtB,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}