{"version": 3, "file": "watcher-helper.js", "sources": ["../../../../../../../packages/components/table/src/table-column/watcher-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, watch } from 'vue'\nimport { hasOwn } from '@element-plus/utils'\nimport { parseMinWidth, parseWidth } from '../util'\n\nimport type { ComputedRef } from 'vue'\nimport type { TableColumn, TableColumnCtx, ValueOf } from './defaults'\n\nfunction getAllAliases(props, aliases) {\n  return props.reduce((prev, cur) => {\n    prev[cur] = cur\n    return prev\n  }, aliases)\n}\nfunction useWatcher<T>(\n  owner: ComputedRef<any>,\n  props_: Partial<TableColumnCtx<T>>\n) {\n  const instance = getCurrentInstance() as TableColumn<T>\n  const registerComplexWatchers = () => {\n    const props = ['fixed']\n    const aliases = {\n      realWidth: 'width',\n      realMinWidth: 'minWidth',\n    }\n    const allAliases = getAllAliases(props, aliases)\n    Object.keys(allAliases).forEach((key) => {\n      const columnKey = aliases[key]\n      if (hasOwn(props_, columnKey)) {\n        watch(\n          () => props_[columnKey],\n          (newVal) => {\n            let value: ValueOf<TableColumnCtx<T>> = newVal\n            if (columnKey === 'width' && key === 'realWidth') {\n              value = parseWidth(newVal)\n            }\n            if (columnKey === 'minWidth' && key === 'realMinWidth') {\n              value = parseMinWidth(newVal)\n            }\n            instance.columnConfig.value[columnKey as any] = value\n            instance.columnConfig.value[key] = value\n            const updateColumns = columnKey === 'fixed'\n            owner.value.store.scheduleLayout(updateColumns)\n          }\n        )\n      }\n    })\n  }\n  const registerNormalWatchers = () => {\n    const props = [\n      'label',\n      'filters',\n      'filterMultiple',\n      'filteredValue',\n      'sortable',\n      'index',\n      'formatter',\n      'className',\n      'labelClassName',\n      'filterClassName',\n      'showOverflowTooltip',\n      'tooltipFormatter',\n    ]\n    const aliases = {\n      property: 'prop',\n      align: 'realAlign',\n      headerAlign: 'realHeaderAlign',\n    }\n    const allAliases = getAllAliases(props, aliases)\n    Object.keys(allAliases).forEach((key) => {\n      const columnKey = aliases[key]\n      if (hasOwn(props_, columnKey)) {\n        watch(\n          () => props_[columnKey],\n          (newVal) => {\n            instance.columnConfig.value[key] = newVal\n          }\n        )\n      }\n    })\n  }\n\n  return {\n    registerComplexWatchers,\n    registerNormalWatchers,\n  }\n}\n\nexport default useWatcher\n"], "names": ["getCurrentInstance", "hasOwn", "watch", "parse<PERSON>idth", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;AAGA,SAAS,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE;AACvC,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK;AACrC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACpB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,EAAE,OAAO,CAAC,CAAC;AACd,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE;AACnC,EAAE,MAAM,QAAQ,GAAGA,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,uBAAuB,GAAG,MAAM;AACxC,IAAI,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC;AAC5B,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,YAAY,EAAE,UAAU;AAC9B,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACrD,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC7C,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,IAAIC,aAAM,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AACrC,QAAQC,SAAK,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,KAAK;AACnD,UAAU,IAAI,KAAK,GAAG,MAAM,CAAC;AAC7B,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,GAAG,KAAK,WAAW,EAAE;AAC5D,YAAY,KAAK,GAAGC,eAAU,CAAC,MAAM,CAAC,CAAC;AACvC,WAAW;AACX,UAAU,IAAI,SAAS,KAAK,UAAU,IAAI,GAAG,KAAK,cAAc,EAAE;AAClE,YAAY,KAAK,GAAGC,kBAAa,CAAC,MAAM,CAAC,CAAC;AAC1C,WAAW;AACX,UAAU,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AACzD,UAAU,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACnD,UAAU,MAAM,aAAa,GAAG,SAAS,KAAK,OAAO,CAAC;AACtD,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAC1D,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAG,MAAM;AACvC,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AACxB,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,QAAQ,EAAE,MAAM;AACtB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,WAAW,EAAE,iBAAiB;AACpC,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACrD,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC7C,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,IAAIH,aAAM,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AACrC,QAAQC,SAAK,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,KAAK;AACnD,UAAU,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;AACpD,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;AAC1B,GAAG,CAAC;AACJ;;;;"}