{"version": 3, "file": "skeleton-item2.js", "sources": ["../../../../../../packages/components/skeleton/src/skeleton-item.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.e('item'), ns.e(variant)]\">\n    <picture-filled v-if=\"variant === 'image'\" />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport { PictureFilled } from '@element-plus/icons-vue'\nimport { skeletonItemProps } from './skeleton-item'\n\ndefineOptions({\n  name: 'ElSkeletonItem',\n})\ndefineProps(skeletonItemProps)\nconst ns = useNamespace('skeleton')\n</script>\n"], "names": ["useNamespace"], "mappings": ";;;;;;;;;;uCAWc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;AAEA,IAAM,MAAA,EAAA,GAAKA,mBAAa,UAAU,CAAA,CAAA;;;;;;;;;;;;;;"}