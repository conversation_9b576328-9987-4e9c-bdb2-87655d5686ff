'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var af = require('./locale/lang/af.js');
var arEg = require('./locale/lang/ar-eg.js');
var ar = require('./locale/lang/ar.js');
var az = require('./locale/lang/az.js');
var bg = require('./locale/lang/bg.js');
var bn = require('./locale/lang/bn.js');
var ca = require('./locale/lang/ca.js');
var ckb = require('./locale/lang/ckb.js');
var cs = require('./locale/lang/cs.js');
var da = require('./locale/lang/da.js');
var de = require('./locale/lang/de.js');
var el = require('./locale/lang/el.js');
var en = require('./locale/lang/en.js');
var eo = require('./locale/lang/eo.js');
var es = require('./locale/lang/es.js');
var et = require('./locale/lang/et.js');
var eu = require('./locale/lang/eu.js');
var fa = require('./locale/lang/fa.js');
var fi = require('./locale/lang/fi.js');
var fr = require('./locale/lang/fr.js');
var he = require('./locale/lang/he.js');
var hi = require('./locale/lang/hi.js');
var hr = require('./locale/lang/hr.js');
var hu = require('./locale/lang/hu.js');
var hyAm = require('./locale/lang/hy-am.js');
var id = require('./locale/lang/id.js');
var it = require('./locale/lang/it.js');
var ja = require('./locale/lang/ja.js');
var kk = require('./locale/lang/kk.js');
var km = require('./locale/lang/km.js');
var ko = require('./locale/lang/ko.js');
var ku = require('./locale/lang/ku.js');
var ky = require('./locale/lang/ky.js');
var lo = require('./locale/lang/lo.js');
var lt = require('./locale/lang/lt.js');
var lv = require('./locale/lang/lv.js');
var mg = require('./locale/lang/mg.js');
var mn = require('./locale/lang/mn.js');
var ms = require('./locale/lang/ms.js');
var my = require('./locale/lang/my.js');
var nbNo = require('./locale/lang/nb-no.js');
var nl = require('./locale/lang/nl.js');
var no = require('./locale/lang/no.js');
var pa = require('./locale/lang/pa.js');
var pl = require('./locale/lang/pl.js');
var ptBr = require('./locale/lang/pt-br.js');
var pt = require('./locale/lang/pt.js');
var ro = require('./locale/lang/ro.js');
var ru = require('./locale/lang/ru.js');
var sk = require('./locale/lang/sk.js');
var sl = require('./locale/lang/sl.js');
var sr = require('./locale/lang/sr.js');
var sv = require('./locale/lang/sv.js');
var sw = require('./locale/lang/sw.js');
var ta = require('./locale/lang/ta.js');
var te = require('./locale/lang/te.js');
var th = require('./locale/lang/th.js');
var tk = require('./locale/lang/tk.js');
var tr = require('./locale/lang/tr.js');
var ugCn = require('./locale/lang/ug-cn.js');
var uk = require('./locale/lang/uk.js');
var uzUz = require('./locale/lang/uz-uz.js');
var vi = require('./locale/lang/vi.js');
var zhCn = require('./locale/lang/zh-cn.js');
var zhTw = require('./locale/lang/zh-tw.js');
var zhHk = require('./locale/lang/zh-hk.js');
var zhMo = require('./locale/lang/zh-mo.js');



exports.af = af["default"];
exports.arEg = arEg["default"];
exports.ar = ar["default"];
exports.az = az["default"];
exports.bg = bg["default"];
exports.bn = bn["default"];
exports.ca = ca["default"];
exports.ckb = ckb["default"];
exports.cs = cs["default"];
exports.da = da["default"];
exports.de = de["default"];
exports.el = el["default"];
exports.en = en["default"];
exports.eo = eo["default"];
exports.es = es["default"];
exports.et = et["default"];
exports.eu = eu["default"];
exports.fa = fa["default"];
exports.fi = fi["default"];
exports.fr = fr["default"];
exports.he = he["default"];
exports.hi = hi["default"];
exports.hr = hr["default"];
exports.hu = hu["default"];
exports.hyAm = hyAm["default"];
exports.id = id["default"];
exports.it = it["default"];
exports.ja = ja["default"];
exports.kk = kk["default"];
exports.km = km["default"];
exports.ko = ko["default"];
exports.ku = ku["default"];
exports.ky = ky["default"];
exports.lo = lo["default"];
exports.lt = lt["default"];
exports.lv = lv["default"];
exports.mg = mg["default"];
exports.mn = mn["default"];
exports.ms = ms["default"];
exports.my = my["default"];
exports.nbNo = nbNo["default"];
exports.nl = nl["default"];
exports.no = no["default"];
exports.pa = pa["default"];
exports.pl = pl["default"];
exports.ptBr = ptBr["default"];
exports.pt = pt["default"];
exports.ro = ro["default"];
exports.ru = ru["default"];
exports.sk = sk["default"];
exports.sl = sl["default"];
exports.sr = sr["default"];
exports.sv = sv["default"];
exports.sw = sw["default"];
exports.ta = ta["default"];
exports.te = te["default"];
exports.th = th["default"];
exports.tk = tk["default"];
exports.tr = tr["default"];
exports.ugCn = ugCn["default"];
exports.uk = uk["default"];
exports.uzUz = uzUz["default"];
exports.vi = vi["default"];
exports.zhCn = zhCn["default"];
exports.zhTw = zhTw["default"];
exports.zhHk = zhHk["default"];
exports.zhMo = zhMo["default"];
//# sourceMappingURL=locales.js.map
