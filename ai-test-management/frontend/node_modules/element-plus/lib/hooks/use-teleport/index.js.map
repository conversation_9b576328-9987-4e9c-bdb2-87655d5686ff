{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-teleport/index.ts"], "sourcesContent": ["import { Teleport, h, onUnmounted, ref } from 'vue'\nimport {\n  NOOP,\n  createGlobalNode,\n  isClient,\n  removeGlobalNode,\n} from '@element-plus/utils'\n\nimport type { Ref, VNode } from 'vue'\n\nexport const useTeleport = (\n  contentRenderer: () => VNode,\n  appendToBody: Ref<boolean>\n) => {\n  const isTeleportVisible = ref(false)\n\n  if (!isClient) {\n    return {\n      isTeleportVisible,\n      showTeleport: NOOP,\n      hideTeleport: NOOP,\n      renderTeleport: NOOP,\n    }\n  }\n\n  let $el: HTMLElement | null = null\n\n  const showTeleport = () => {\n    isTeleportVisible.value = true\n    // this allows the delayed showing strategy since the the content itself could be enterable\n    // e.g. el-popper\n    if ($el !== null) return\n\n    $el = createGlobalNode()\n  }\n\n  const hideTeleport = () => {\n    isTeleportVisible.value = false\n    if ($el !== null) {\n      removeGlobalNode($el)\n      $el = null\n    }\n  }\n\n  const renderTeleport = () => {\n    return appendToBody.value !== true\n      ? contentRenderer()\n      : isTeleportVisible.value\n      ? [h(Teleport, { to: $el }, contentRenderer())]\n      : undefined\n  }\n\n  onUnmounted(hideTeleport)\n\n  return {\n    isTeleportVisible,\n    showTeleport,\n    hideTeleport,\n    renderTeleport,\n  }\n}\n"], "names": ["ref", "isClient", "NOOP", "createGlobalNode", "removeGlobalNode", "h", "Teleport", "onUnmounted"], "mappings": ";;;;;;;;;AAOY,MAAC,WAAW,GAAG,CAAC,eAAe,EAAE,YAAY,KAAK;AAC9D,EAAE,MAAM,iBAAiB,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACvC,EAAE,IAAI,CAACC,aAAQ,EAAE;AACjB,IAAI,OAAO;AACX,MAAM,iBAAiB;AACvB,MAAM,YAAY,EAAEC,WAAI;AACxB,MAAM,YAAY,EAAEA,WAAI;AACxB,MAAM,cAAc,EAAEA,WAAI;AAC1B,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC;AACjB,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC;AACnC,IAAI,IAAI,GAAG,KAAK,IAAI;AACpB,MAAM,OAAO;AACb,IAAI,GAAG,GAAGC,2BAAgB,EAAE,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC;AACpC,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE;AACtB,MAAMC,2BAAgB,CAAC,GAAG,CAAC,CAAC;AAC5B,MAAM,GAAG,GAAG,IAAI,CAAC;AACjB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,OAAO,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,eAAe,EAAE,GAAG,iBAAiB,CAAC,KAAK,GAAG,CAACC,KAAC,CAACC,YAAQ,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAC9I,GAAG,CAAC;AACJ,EAAEC,eAAW,CAAC,YAAY,CAAC,CAAC;AAC5B,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}