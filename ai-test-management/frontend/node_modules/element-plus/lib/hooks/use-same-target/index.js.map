{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-same-target/index.ts"], "sourcesContent": ["import { NOOP } from '@element-plus/utils'\n\nexport const useSameTarget = (handleClick?: (e: MouseEvent) => void) => {\n  if (!handleClick) {\n    return { onClick: NOOP, onMousedown: NOOP, onMouseup: NOOP }\n  }\n\n  let mousedownTarget = false\n  let mouseupTarget = false\n  // refer to this https://javascript.info/mouse-events-basics\n  // events fired in the order: mousedown -> mouseup -> click\n  // we need to set the mousedown handle to false after click fired.\n  const onClick = (e: MouseEvent) => {\n    // if and only if\n    if (mousedownTarget && mouseupTarget) {\n      handleClick(e)\n    }\n    mousedownTarget = mouseupTarget = false\n  }\n\n  const onMousedown = (e: MouseEvent) => {\n    // marking current mousedown target.\n    mousedownTarget = e.target === e.currentTarget\n  }\n  const onMouseup = (e: MouseEvent) => {\n    // marking current mouseup target.\n    mouseupTarget = e.target === e.currentTarget\n  }\n\n  return { onClick, onMousedown, onMouseup }\n}\n"], "names": ["NOOP"], "mappings": ";;;;;;AACY,MAAC,aAAa,GAAG,CAAC,WAAW,KAAK;AAC9C,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,EAAE,OAAO,EAAEA,WAAI,EAAE,WAAW,EAAEA,WAAI,EAAE,SAAS,EAAEA,WAAI,EAAE,CAAC;AACjE,GAAG;AACH,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B,EAAE,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK;AACzB,IAAI,IAAI,eAAe,IAAI,aAAa,EAAE;AAC1C,MAAM,WAAW,CAAC,CAAC,CAAC,CAAC;AACrB,KAAK;AACL,IAAI,eAAe,GAAG,aAAa,GAAG,KAAK,CAAC;AAC5C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,eAAe,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,aAAa,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK;AAC3B,IAAI,aAAa,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,aAAa,CAAC;AACjD,GAAG,CAAC;AACJ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;AAC7C;;;;"}