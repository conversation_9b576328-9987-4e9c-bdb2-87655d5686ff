{"version": 3, "file": "tk.js", "sources": ["../../../../../packages/locale/lang/tk.ts"], "sourcesContent": ["export default {\n  name: 'tk',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>wagt',\n      today: '<PERSON>ü<PERSON><PERSON><PERSON>',\n      cancel: 'Bes et',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON> sa<PERSON>',\n      selectTime: 'Wagty saýlaň',\n      startDate: '<PERSON><PERSON><PERSON><PERSON><PERSON> güni',\n      startTime: '<PERSON><PERSON><PERSON><PERSON><PERSON> wagty',\n      endDate: 'G<PERSON>r<PERSON><PERSON> güni',\n      endTime: 'G<PERSON>r<PERSON>an wagty',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: '',\n      month1: 'Ýan',\n      month2: 'Few',\n      month3: 'Mar',\n      month4: 'Apr',\n      month5: 'Maý',\n      month6: 'Iýn',\n      month7: 'Iýl',\n      month8: 'Awg',\n      month9: 'Sen',\n      month10: 'Okt',\n      month11: 'Noý',\n      month12: 'Dek',\n      // week: 'week',\n      weeks: {\n        sun: 'Ýek',\n        mon: 'Du<PERSON>',\n        tue: 'Si<PERSON>',\n        wed: 'Çar',\n        thu: 'Pen',\n        fri: 'Ann',\n        sat: 'Şen',\n      },\n      months: {\n        jan: 'Ýan',\n        feb: 'Few',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maý',\n        jun: 'Iýn',\n        jul: 'Iýl',\n        aug: 'Awg',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Noý',\n        dec: 'Dek',\n      },\n    },\n    select: {\n      loading: 'Indirilýär',\n      noMatch: 'Hiçzat tapylmady',\n      noData: 'Hiçzat ýok',\n      placeholder: 'Saýla',\n    },\n    mention: {\n      loading: 'Indirilýär',\n    },\n    cascader: {\n      noMatch: 'Hiçzat tapylmady',\n      loading: 'Indirilýär',\n      placeholder: 'Saýlaň',\n      noData: 'Hiçzat ýok',\n    },\n    pagination: {\n      goto: 'Git',\n      pagesize: '/sahypa',\n      total: 'Umumy {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Hat',\n      confirm: 'OK',\n      cancel: 'Bes et',\n      error: 'Ýalňyş girizme',\n    },\n    upload: {\n      deleteTip: 'Pozmak üçin \"poz\" düwmä basyň',\n      delete: 'Poz',\n      preview: 'Gör',\n      continue: 'Dowam et',\n    },\n    table: {\n      emptyText: 'Maglumat ýok',\n      confirmFilter: 'Tassykla',\n      resetFilter: 'Arassala',\n      clearFilter: 'Hemmesi',\n      sumText: 'Jemi',\n    },\n    tree: {\n      emptyText: 'Maglumat ýok',\n    },\n    transfer: {\n      noMatch: 'Hiçzat tapylmady',\n      noData: 'Hiçzat ýok',\n      titles: ['Sanaw 1', 'Sanaw 2'],\n      filterPlaceholder: 'Gözleg sözlerini giriziň',\n      noCheckedFormat: '{total} sany',\n      hasCheckedFormat: '{checked}/{total} saýlanan',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,UAAU;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,aAAa;AACxB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,wBAAwB;AAC1C,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,WAAW,EAAE,UAAU;AAC7B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kBAAkB;AACjC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,WAAW,EAAE,gBAAgB;AACnC,MAAM,MAAM,EAAE,kBAAkB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,6BAA6B;AAC1C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,gDAAgD;AACjE,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,iBAAiB;AAClC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,qCAAqC;AAC9D,MAAM,eAAe,EAAE,cAAc;AACrC,MAAM,gBAAgB,EAAE,+BAA+B;AACvD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}