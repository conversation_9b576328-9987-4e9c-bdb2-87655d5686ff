{"version": 3, "file": "pl.js", "sources": ["../../../../../packages/locale/lang/pl.ts"], "sourcesContent": ["export default {\n  name: 'pl',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON><PERSON><PERSON>',\n      cancel: 'Anuluj',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON><PERSON> datę',\n      selectTime: '<PERSON><PERSON><PERSON><PERSON> godzin<PERSON>',\n      startDate: 'Data początkowa',\n      startTime: '<PERSON><PERSON><PERSON> początkowa',\n      endDate: 'Data końcowa',\n      endTime: '<PERSON><PERSON> końcowa',\n      prevYear: 'Poprzedni rok',\n      nextYear: 'Następny rok',\n      prevMonth: 'Poprzedni miesiąc',\n      nextMonth: 'Następny miesiąc',\n      year: 'rok',\n      month1: 'styczeń',\n      month2: 'luty',\n      month3: 'marzec',\n      month4: 'kwiecie<PERSON>',\n      month5: 'maj',\n      month6: 'czerwiec',\n      month7: 'lipiec',\n      month8: 'sierpie<PERSON>',\n      month9: 'wrzesie<PERSON>',\n      month10: 'paźd<PERSON><PERSON>',\n      month11: 'listopad',\n      month12: 'grudzie<PERSON>',\n      week: 'tydzień',\n      weeks: {\n        sun: 'niedz.',\n        mon: 'pon.',\n        tue: 'wt.',\n        wed: 'śr.',\n        thu: 'czw.',\n        fri: 'pt.',\n        sat: 'sob.',\n      },\n      months: {\n        jan: 'STY',\n        feb: 'LUT',\n        mar: 'MAR',\n        apr: 'KWI',\n        may: 'MAJ',\n        jun: 'CZE',\n        jul: 'LIP',\n        aug: 'SIE',\n        sep: 'WRZ',\n        oct: 'PAŹ',\n        nov: 'LIS',\n        dec: 'GRU',\n      },\n    },\n    select: {\n      loading: 'Ładowanie',\n      noMatch: 'Brak dopasowań',\n      noData: 'Brak danych',\n      placeholder: 'Wybierz',\n    },\n    mention: {\n      loading: 'Ładowanie',\n    },\n    cascader: {\n      noMatch: 'Brak dopasowań',\n      loading: 'Ładowanie',\n      placeholder: 'Wybierz',\n      noData: 'Brak danych',\n    },\n    pagination: {\n      goto: 'Idź do',\n      pagesize: '/stronę',\n      total: 'Wszystkich {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Wiadomość',\n      confirm: 'OK',\n      cancel: 'Anuluj',\n      error: 'Wiadomość zawiera niedozwolone znaki',\n    },\n    upload: {\n      deleteTip: 'kliknij kasuj aby usunąć',\n      delete: 'Kasuj',\n      preview: 'Podgląd',\n      continue: 'Kontynuuj',\n    },\n    table: {\n      emptyText: 'Brak danych',\n      confirmFilter: 'Potwierdź',\n      resetFilter: 'Resetuj',\n      clearFilter: 'Wszystko',\n      sumText: 'Razem',\n    },\n    tour: {\n      next: 'Dalej',\n      previous: 'Wróć',\n      finish: 'Zakończ',\n    },\n    tree: {\n      emptyText: 'Brak danych',\n    },\n    transfer: {\n      noMatch: 'Brak dopasowań',\n      noData: 'Brak danych',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Wpisz szukaną frazę',\n      noCheckedFormat: 'razem: {total}',\n      hasCheckedFormat: 'wybranych: {checked}/{total}',\n    },\n    image: {\n      error: 'BŁĄD',\n    },\n    pageHeader: {\n      title: 'Wstecz',\n    },\n    popconfirm: {\n      confirmButtonText: 'Tak',\n      cancelButtonText: 'Nie',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,sBAAsB;AACvC,MAAM,SAAS,EAAE,yBAAyB;AAC1C,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,WAAW,EAAE,SAAS;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,gBAAgB;AAC/B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,qBAAqB;AAClC,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,gDAAgD;AAC7D,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,oCAAoC;AACrD,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,QAAQ,EAAE,WAAW;AAC3B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,MAAM,EAAE,cAAc;AAC5B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,aAAa;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,+BAA+B;AACxD,MAAM,eAAe,EAAE,gBAAgB;AACvC,MAAM,gBAAgB,EAAE,8BAA8B;AACtD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,gBAAgB;AAC7B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,KAAK;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}