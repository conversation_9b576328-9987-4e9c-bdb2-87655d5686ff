declare const _default: {
    name: string;
    el: {
        breadcrumb: {
            label: string;
        };
        colorpicker: {
            confirm: string;
            clear: string;
        };
        datepicker: {
            now: string;
            today: string;
            cancel: string;
            clear: string;
            confirm: string;
            selectDate: string;
            selectTime: string;
            startDate: string;
            startTime: string;
            endDate: string;
            endTime: string;
            prevYear: string;
            nextYear: string;
            prevMonth: string;
            nextMonth: string;
            year: string;
            month1: string;
            month2: string;
            month3: string;
            month4: string;
            month5: string;
            month6: string;
            month7: string;
            month8: string;
            month9: string;
            month10: string;
            month11: string;
            month12: string;
            weeks: {
                sun: string;
                mon: string;
                tue: string;
                wed: string;
                thu: string;
                fri: string;
                sat: string;
            };
            months: {
                jan: string;
                feb: string;
                mar: string;
                apr: string;
                may: string;
                jun: string;
                jul: string;
                aug: string;
                sep: string;
                oct: string;
                nov: string;
                dec: string;
            };
        };
        select: {
            loading: string;
            noMatch: string;
            noData: string;
            placeholder: string;
        };
        mention: {
            loading: string;
        };
        cascader: {
            noMatch: string;
            loading: string;
            placeholder: string;
            noData: string;
        };
        pagination: {
            goto: string;
            pagesize: string;
            total: string;
            pageClassifier: string;
            page: string;
            prev: string;
            next: string;
            currentPage: string;
            prevPages: string;
            nextPages: string;
        };
        messagebox: {
            title: string;
            confirm: string;
            cancel: string;
            error: string;
        };
        upload: {
            deleteTip: string;
            delete: string;
            preview: string;
            continue: string;
        };
        table: {
            emptyText: string;
            confirmFilter: string;
            resetFilter: string;
            clearFilter: string;
            sumText: string;
        };
        tree: {
            emptyText: string;
        };
        transfer: {
            noMatch: string;
            noData: string;
            titles: string[];
            filterPlaceholder: string;
            noCheckedFormat: string;
            hasCheckedFormat: string;
        };
        image: {
            error: string;
        };
        pageHeader: {
            title: string;
        };
        popconfirm: {
            confirmButtonText: string;
            cancelButtonText: string;
        };
        carousel: {
            leftArrow: string;
            rightArrow: string;
            indicator: string;
        };
    };
};
export default _default;
