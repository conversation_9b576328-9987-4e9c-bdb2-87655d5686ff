{"version": 3, "file": "ku.js", "sources": ["../../../../../packages/locale/lang/ku.ts"], "sourcesContent": ["export default {\n  name: 'ku',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON>ma<PERSON>',\n      clear: 'Paqij bike',\n    },\n    datepicker: {\n      now: '<PERSON>ha',\n      today: 'Îro',\n      cancel: 'Betal bike',\n      clear: '<PERSON>qij bike',\n      confirm: 'Tema<PERSON>',\n      selectDate: 'Dîrokê bibijêre',\n      selectTime: 'Demê bibijêre',\n      startDate: 'Dîrok<PERSON> Destpêkê',\n      startTime: '<PERSON><PERSON>tpêkê',\n      endDate: 'Dîroka Dawî',\n      endTime: 'Dema Dawî',\n      prevYear: 'Sala <PERSON>',\n      nextYear: '<PERSON>a <PERSON>',\n      prevMonth: '<PERSON>ha <PERSON>',\n      nextMonth: '<PERSON><PERSON>',\n      year: 'Sal',\n      month1: 'Rêbendan',\n      month2: 'Reşemeh',\n      month3: 'Adar',\n      month4: 'Avrêl',\n      month5: 'Gulan',\n      month6: '<PERSON><PERSON><PERSON><PERSON>',\n      month7: 'T<PERSON><PERSON><PERSON>',\n      month8: '<PERSON>av<PERSON><PERSON>',\n      month9: '<PERSON><PERSON><PERSON>',\n      month10: 'Kew<PERSON>êr',\n      month11: 'Sarmawaz',\n      month12: 'Be<PERSON><PERSON>bar',\n      // week: 'week',\n      weeks: {\n        sun: 'Yek',\n        mon: 'Duş',\n        tue: 'Sêş',\n        wed: 'Çar',\n        thu: 'Pên',\n        fri: 'În',\n        sat: 'Şem',\n      },\n      months: {\n        jan: 'Rêb',\n        feb: 'Reş',\n        mar: 'Ada',\n        apr: 'Avr',\n        may: 'Gul',\n        jun: 'Pûş',\n        jul: 'Tîr',\n        aug: 'Gil',\n        sep: 'Rez',\n        oct: 'Kew',\n        nov: 'Sar',\n        dec: 'Ber',\n      },\n    },\n    select: {\n      loading: 'Bardibe',\n      noMatch: 'Li hembere ve agahî tune',\n      noData: 'Agahî tune',\n      placeholder: 'Bibijêre',\n    },\n    mention: {\n      loading: 'Bardibe',\n    },\n    cascader: {\n      noMatch: 'Li hembere ve agahî tune',\n      loading: 'Bardibe',\n      placeholder: 'Bibijêre',\n      noData: 'Agahî tune',\n    },\n    pagination: {\n      goto: 'Biçe',\n      pagesize: '/rupel',\n      total: 'Tevahî {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Peyam',\n      confirm: 'Temam',\n      cancel: 'Betal bike',\n      error: 'Beyana çewt',\n    },\n    upload: {\n      deleteTip: 'ji bo rake pêl \"delete\" bike',\n      delete: 'Rake',\n      preview: 'Pêşdîtin',\n      continue: 'Berdewam',\n    },\n    table: {\n      emptyText: 'Agahî tune',\n      confirmFilter: 'Piştrast bike',\n      resetFilter: 'Jê bibe',\n      clearFilter: 'Hemû',\n      sumText: 'Kom',\n    },\n    tree: {\n      emptyText: 'Agahî tune',\n    },\n    transfer: {\n      noMatch: 'Li hembere ve agahî tune',\n      noData: 'Agahî tune',\n      titles: ['Lîste 1', 'Lîste 2'],\n      filterPlaceholder: 'Binivîse',\n      noCheckedFormat: '{total} lib',\n      hasCheckedFormat: '{checked}/{total} bijartin',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,UAAU,EAAE,0BAA0B;AAC5C,MAAM,UAAU,EAAE,qBAAqB;AACvC,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,aAAa;AAC1B,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,aAAa;AAC1B,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,WAAW,EAAE,aAAa;AAChC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,SAAS;AACxB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,MAAM,EAAE,eAAe;AAC7B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,gBAAgB;AAC7B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,iCAAiC;AAClD,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,aAAa,EAAE,oBAAoB;AACzC,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,eAAe;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AAC1C,MAAM,iBAAiB,EAAE,aAAa;AACtC,MAAM,eAAe,EAAE,aAAa;AACpC,MAAM,gBAAgB,EAAE,4BAA4B;AACpD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}