'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var kk = {
  name: "kk",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "\u049A\u0430\u0431\u044B\u043B\u0434\u0430\u0443",
      clear: "\u0422\u0430\u0437\u0430\u043B\u0430\u0443"
    },
    datepicker: {
      now: "\u049A\u0430\u0437\u0456\u0440",
      today: "\u0411\u04AF\u0433\u0456\u043D",
      cancel: "\u0411\u043E\u043B\u0434\u044B\u0440\u043C\u0430\u0443",
      clear: "\u0422\u0430\u0437\u0430\u043B\u0430\u0443",
      confirm: "\u049A\u0430\u0431\u044B\u043B\u0434\u0430\u0443",
      selectDate: "\u041A\u04AF\u043D\u0434\u0456 \u0442\u0430\u04A3\u0434\u0430\u04A3\u044B\u0437",
      selectTime: "\u0421\u0430\u0493\u0430\u0442\u0442\u044B \u0442\u0430\u04A3\u0434\u0430\u04A3\u044B\u0437",
      startDate: "\u0411\u0430\u0441\u0442\u0430\u043B\u0443 \u043A\u04AF\u043D\u0456",
      startTime: "\u0411\u0430\u0441\u0442\u0430\u043B\u0443 \u0441\u0430\u0493\u0430\u0442\u044B",
      endDate: "\u0410\u044F\u049B\u0442\u0430\u043B\u0443 \u043A\u04AF\u043D\u0456",
      endTime: "\u0410\u044F\u049B\u0442\u0430\u043B\u0443 \u0441\u0430\u0493\u0430\u0442\u044B",
      prevYear: "\u0410\u043B\u0434\u044B\u04A3\u0493\u044B \u0436\u044B\u043B",
      nextYear: "\u041A\u0435\u043B\u0435\u0441\u0456 \u0436\u044B\u043B",
      prevMonth: "\u0410\u043B\u0434\u044B\u04A3\u0493\u044B \u0430\u0439",
      nextMonth: "\u041A\u0435\u043B\u0435\u0441\u0456 \u0430\u0439",
      year: "\u0416\u044B\u043B",
      month1: "\u049A\u0430\u04A3\u0442\u0430\u0440",
      month2: "\u0410\u049B\u043F\u0430\u043D",
      month3: "\u041D\u0430\u0443\u0440\u044B\u0437",
      month4: "\u0421\u04D9\u0443\u0456\u0440",
      month5: "\u041C\u0430\u043C\u044B\u0440",
      month6: "\u041C\u0430\u0443\u0441\u044B\u043C",
      month7: "\u0428\u0456\u043B\u0434\u0435",
      month8: "\u0422\u0430\u043C\u044B\u0437",
      month9: "\u049A\u044B\u0440\u043A\u04AF\u0439\u0435\u043A",
      month10: "\u049A\u0430\u0437\u0430\u043D",
      month11: "\u049A\u0430\u0440\u0430\u0448\u0430",
      month12: "\u0416\u0435\u043B\u0442\u043E\u049B\u0441\u0430\u043D",
      week: "\u0410\u043F\u0442\u0430",
      weeks: {
        sun: "\u0416\u0435\u043A",
        mon: "\u0414\u04AF\u0439",
        tue: "\u0421\u0435\u0439",
        wed: "\u0421\u04D9\u0440",
        thu: "\u0411\u0435\u0439",
        fri: "\u0416\u04B1\u043C",
        sat: "\u0421\u0435\u043D"
      },
      months: {
        jan: "\u049A\u0430\u04A3",
        feb: "\u0410\u049B\u043F",
        mar: "\u041D\u0430\u0443",
        apr: "\u0421\u04D9\u0443",
        may: "\u041C\u0430\u043C",
        jun: "\u041C\u0430\u0443",
        jul: "\u0428\u0456\u043B",
        aug: "\u0422\u0430\u043C",
        sep: "\u049A\u044B\u0440",
        oct: "\u049A\u0430\u0437",
        nov: "\u049A\u0430\u0440",
        dec: "\u0416\u0435\u043B"
      }
    },
    select: {
      loading: "\u0416\u04AF\u043A\u0442\u0435\u043B\u0443\u0434\u0435",
      noMatch: "\u0421\u04D9\u0439\u043A\u0435\u0441 \u0434\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B",
      noData: "\u0414\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B",
      placeholder: "\u0422\u0430\u04A3\u0434\u0430\u04A3\u044B\u0437"
    },
    mention: {
      loading: "\u0416\u04AF\u043A\u0442\u0435\u043B\u0443\u0434\u0435"
    },
    cascader: {
      noMatch: "\u0421\u04D9\u0439\u043A\u0435\u0441 \u0434\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B",
      loading: "\u0416\u04AF\u043A\u0442\u0435\u043B\u0443\u0434\u0435",
      placeholder: "\u0422\u0430\u04A3\u0434\u0430\u04A3\u044B\u0437",
      noData: "\u0414\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B"
    },
    pagination: {
      goto: "\u0411\u0430\u0440\u0443",
      pagesize: "/page",
      total: "\u0411\u0430\u0440\u043B\u044B\u0493\u044B {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "\u0425\u0430\u0431\u0430\u0440",
      confirm: "\u049A\u0430\u0431\u044B\u043B\u0434\u0430\u0443",
      cancel: "\u0411\u043E\u043B\u0434\u044B\u0440\u043C\u0430\u0443",
      error: "\u0416\u0430\u0440\u0430\u043C\u0441\u044B\u0437 \u0435\u043D\u0433\u0456\u0437\u0443\u043B\u0435\u0440"
    },
    upload: {
      deleteTip: "\u04E8\u0448\u0456\u0440\u0443\u0434\u0456 \u0431\u0430\u0441\u044B\u043F \u04E9\u0448\u0456\u0440\u0456\u04A3\u0456\u0437",
      delete: "\u04E8\u0448\u0456\u0440\u0443",
      preview: "\u0410\u043B\u0434\u044B\u043D \u0430\u043B\u0430 \u049B\u0430\u0440\u0430\u0443",
      continue: "\u0416\u0430\u043B\u0493\u0430\u0441\u0442\u044B\u0440\u0443"
    },
    table: {
      emptyText: "\u0414\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B",
      confirmFilter: "\u049A\u0430\u0431\u044B\u043B\u0434\u0430\u0443",
      resetFilter: "\u049A\u0430\u043B\u043F\u044B\u043D\u0430 \u043A\u0435\u043B\u0442\u0456\u0440\u0443",
      clearFilter: "\u0411\u0430\u0440\u043B\u044B\u0493\u044B",
      sumText: "\u0421\u043E\u043C\u0430\u0441\u044B"
    },
    tree: {
      emptyText: "\u0414\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B"
    },
    transfer: {
      noMatch: "\u0421\u04D9\u0439\u043A\u0435\u0441 \u0434\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B",
      noData: "\u0414\u0435\u0440\u0435\u043A\u0442\u0435\u0440 \u0436\u043E\u049B",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "\u041A\u0456\u043B\u0442 \u0441\u04E9\u0437\u0434\u0456 \u0435\u043D\u0433\u0456\u0437\u0456\u04A3\u0456\u0437",
      noCheckedFormat: "{total} \u044D\u043B\u044D\u043C\u044D\u043D\u0442",
      hasCheckedFormat: "{checked}/{total} \u049B\u04B1\u0441\u0431\u0435\u043B\u0433\u0456\u0441\u0456 \u049B\u043E\u0439\u044B\u043B\u0434\u044B"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

exports["default"] = kk;
//# sourceMappingURL=kk.js.map
