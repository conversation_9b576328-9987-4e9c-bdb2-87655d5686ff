{"version": 3, "file": "scroll.js", "sources": ["../../../../../packages/utils/dom/scroll.ts"], "sourcesContent": ["import { isClient } from '../browser'\nimport { easeInOutCubic } from '../easings'\nimport { isFunction, isWindow } from '../types'\nimport { cAF, rAF } from '../raf'\nimport { getStyle } from './style'\n\nexport const isScroll = (el: HTMLElement, isVertical?: boolean): boolean => {\n  if (!isClient) return false\n\n  const key = (\n    {\n      undefined: 'overflow',\n      true: 'overflow-y',\n      false: 'overflow-x',\n    } as const\n  )[String(isVertical)]!\n  const overflow = getStyle(el, key)\n  return ['scroll', 'auto', 'overlay'].some((s) => overflow.includes(s))\n}\n\nexport const getScrollContainer = (\n  el: HTMLElement,\n  isVertical?: boolean\n): Window | HTMLElement | undefined => {\n  if (!isClient) return\n\n  let parent: HTMLElement = el\n  while (parent) {\n    if ([window, document, document.documentElement].includes(parent))\n      return window\n\n    if (isScroll(parent, isVertical)) return parent\n\n    parent = parent.parentNode as HTMLElement\n  }\n\n  return parent\n}\n\nlet scrollBarWidth: number\nexport const getScrollBarWidth = (namespace: string): number => {\n  if (!isClient) return 0\n  if (scrollBarWidth !== undefined) return scrollBarWidth\n\n  const outer = document.createElement('div')\n  outer.className = `${namespace}-scrollbar__wrap`\n  outer.style.visibility = 'hidden'\n  outer.style.width = '100px'\n  outer.style.position = 'absolute'\n  outer.style.top = '-9999px'\n  document.body.appendChild(outer)\n\n  const widthNoScroll = outer.offsetWidth\n  outer.style.overflow = 'scroll'\n\n  const inner = document.createElement('div')\n  inner.style.width = '100%'\n  outer.appendChild(inner)\n\n  const widthWithScroll = inner.offsetWidth\n  outer.parentNode?.removeChild(outer)\n  scrollBarWidth = widthNoScroll - widthWithScroll\n\n  return scrollBarWidth\n}\n\n/**\n * Scroll with in the container element, positioning the **selected** element at the top\n * of the container\n */\nexport function scrollIntoView(\n  container: HTMLElement,\n  selected: HTMLElement\n): void {\n  if (!isClient) return\n\n  if (!selected) {\n    container.scrollTop = 0\n    return\n  }\n\n  const offsetParents: HTMLElement[] = []\n  let pointer = selected.offsetParent\n  while (\n    pointer !== null &&\n    container !== pointer &&\n    container.contains(pointer)\n  ) {\n    offsetParents.push(pointer as HTMLElement)\n    pointer = (pointer as HTMLElement).offsetParent\n  }\n  const top =\n    selected.offsetTop +\n    offsetParents.reduce((prev, curr) => prev + curr.offsetTop, 0)\n  const bottom = top + selected.offsetHeight\n  const viewRectTop = container.scrollTop\n  const viewRectBottom = viewRectTop + container.clientHeight\n\n  if (top < viewRectTop) {\n    container.scrollTop = top\n  } else if (bottom > viewRectBottom) {\n    container.scrollTop = bottom - container.clientHeight\n  }\n}\n\nexport function animateScrollTo(\n  container: HTMLElement | Window,\n  from: number,\n  to: number,\n  duration: number,\n  callback?: unknown\n) {\n  const startTime = Date.now()\n\n  let handle: number | undefined\n  const scroll = () => {\n    const timestamp = Date.now()\n    const time = timestamp - startTime\n    const nextScrollTop = easeInOutCubic(\n      time > duration ? duration : time,\n      from,\n      to,\n      duration\n    )\n\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop)\n    } else {\n      container.scrollTop = nextScrollTop\n    }\n    if (time < duration) {\n      handle = rAF(scroll)\n    } else if (isFunction(callback)) {\n      callback()\n    }\n  }\n\n  scroll()\n\n  return () => {\n    handle && cAF(handle)\n  }\n}\n\nexport const getScrollElement = (\n  target: HTMLElement,\n  container: HTMLElement | Window\n) => {\n  if (isWindow(container)) {\n    return target.ownerDocument.documentElement\n  }\n  return container\n}\n\nexport const getScrollTop = (container: HTMLElement | Window) => {\n  if (isWindow(container)) {\n    return window.scrollY\n  }\n  return container.scrollTop\n}\n"], "names": ["isClient", "getStyle", "easeInOutCubic", "isWindow", "rAF", "isFunction", "cAF"], "mappings": ";;;;;;;;;;;AAKY,MAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,UAAU,KAAK;AAC5C,EAAE,IAAI,CAACA,aAAQ;AACf,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,MAAM,GAAG,GAAG;AACd,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,KAAK,EAAE,YAAY;AACvB,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AACxB,EAAE,MAAM,QAAQ,GAAGC,cAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AACrC,EAAE,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,EAAE;AACU,MAAC,kBAAkB,GAAG,CAAC,EAAE,EAAE,UAAU,KAAK;AACtD,EAAE,IAAI,CAACD,aAAQ;AACf,IAAI,OAAO;AACX,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,OAAO,MAAM,EAAE;AACjB,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;AACrE,MAAM,OAAO,MAAM,CAAC;AACpB,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC;AACpC,MAAM,OAAO,MAAM,CAAC;AACpB,IAAI,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACF,IAAI,cAAc,CAAC;AACP,MAAC,iBAAiB,GAAG,CAAC,SAAS,KAAK;AAChD,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,CAACA,aAAQ;AACf,IAAI,OAAO,CAAC,CAAC;AACb,EAAE,IAAI,cAAc,KAAK,KAAK,CAAC;AAC/B,IAAI,OAAO,cAAc,CAAC;AAC1B,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC;AACnD,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACpC,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;AAC9B,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACpC,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC;AAC9B,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnC,EAAE,MAAM,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC;AAC1C,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAClC,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;AAC7B,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3B,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,CAAC;AAC5C,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnE,EAAE,cAAc,GAAG,aAAa,GAAG,eAAe,CAAC;AACnD,EAAE,OAAO,cAAc,CAAC;AACxB,EAAE;AACK,SAAS,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;AACpD,EAAE,IAAI,CAACA,aAAQ;AACf,IAAI,OAAO;AACX,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;AAC5B,IAAI,OAAO;AACX,GAAG;AACH,EAAE,MAAM,aAAa,GAAG,EAAE,CAAC;AAC3B,EAAE,IAAI,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC;AACtC,EAAE,OAAO,OAAO,KAAK,IAAI,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AACnF,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC;AACnC,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAClG,EAAE,MAAM,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC7C,EAAE,MAAM,WAAW,GAAG,SAAS,CAAC,SAAS,CAAC;AAC1C,EAAE,MAAM,cAAc,GAAG,WAAW,GAAG,SAAS,CAAC,YAAY,CAAC;AAC9D,EAAE,IAAI,GAAG,GAAG,WAAW,EAAE;AACzB,IAAI,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC;AAC9B,GAAG,MAAM,IAAI,MAAM,GAAG,cAAc,EAAE;AACtC,IAAI,SAAS,CAAC,SAAS,GAAG,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC;AAC1D,GAAG;AACH,CAAC;AACM,SAAS,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACzE,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC/B,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,MAAM,MAAM,GAAG,MAAM;AACvB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,IAAI,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,CAAC;AACvC,IAAI,MAAM,aAAa,GAAGE,sBAAc,CAAC,IAAI,GAAG,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;AAChG,IAAI,IAAIC,cAAQ,CAAC,SAAS,CAAC,EAAE;AAC7B,MAAM,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;AAC5D,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,SAAS,GAAG,aAAa,CAAC;AAC1C,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,QAAQ,EAAE;AACzB,MAAM,MAAM,GAAGC,OAAG,CAAC,MAAM,CAAC,CAAC;AAC3B,KAAK,MAAM,IAAIC,iBAAU,CAAC,QAAQ,CAAC,EAAE;AACrC,MAAM,QAAQ,EAAE,CAAC;AACjB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,OAAO,MAAM;AACf,IAAI,MAAM,IAAIC,OAAG,CAAC,MAAM,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,CAAC;AACW,MAAC,gBAAgB,GAAG,CAAC,MAAM,EAAE,SAAS,KAAK;AACvD,EAAE,IAAIH,cAAQ,CAAC,SAAS,CAAC,EAAE;AAC3B,IAAI,OAAO,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC;AAChD,GAAG;AACH,EAAE,OAAO,SAAS,CAAC;AACnB,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,SAAS,KAAK;AAC3C,EAAE,IAAIA,cAAQ,CAAC,SAAS,CAAC,EAAE;AAC3B,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC;AAC1B,GAAG;AACH,EAAE,OAAO,SAAS,CAAC,SAAS,CAAC;AAC7B;;;;;;;;;;"}