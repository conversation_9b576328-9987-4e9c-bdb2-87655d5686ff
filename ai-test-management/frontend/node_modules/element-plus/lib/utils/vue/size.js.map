{"version": 3, "file": "size.js", "sources": ["../../../../../packages/utils/vue/size.ts"], "sourcesContent": ["import { componentSizeMap } from '@element-plus/constants'\n\nimport type { ComponentSize } from '@element-plus/constants'\n\nexport const getComponentSize = (size?: ComponentSize) => {\n  return componentSizeMap[size || 'default']\n}\n"], "names": ["size", "componentSizeMap"], "mappings": ";;;;;;AACY,MAAC,gBAAgB,GAAG,CAACA,MAAI,KAAK;AAC1C,EAAE,OAAOC,qBAAgB,CAACD,MAAI,IAAI,SAAS,CAAC,CAAC;AAC7C;;;;"}