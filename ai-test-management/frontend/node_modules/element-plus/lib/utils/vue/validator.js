'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var size = require('../../constants/size.js');
var date = require('../../constants/date.js');

const isValidComponentSize = (val) => ["", ...size.componentSizes].includes(val);
const isValidDatePickType = (val) => [...date.datePickTypes].includes(val);

exports.isValidComponentSize = isValidComponentSize;
exports.isValidDatePickType = isValidDatePickType;
//# sourceMappingURL=validator.js.map
