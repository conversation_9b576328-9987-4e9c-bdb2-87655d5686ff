import{s as R,g as Z}from"./confbox.9388d834.mjs";function $(n,l=!1){const g=n.length;let e=0,u="",p=0,k=16,A=0,o=0,O=0,B=0,b=0;function I(i,T){let s=0,c=0;for(;s<i||!T;){let t=n.charCodeAt(e);if(t>=48&&t<=57)c=c*16+t-48;else if(t>=65&&t<=70)c=c*16+t-65+10;else if(t>=97&&t<=102)c=c*16+t-97+10;else break;e++,s++}return s<i&&(c=-1),c}function V(i){e=i,u="",p=0,k=16,b=0}function F(){let i=e;if(n.charCodeAt(e)===48)e++;else for(e++;e<n.length&&L(n.charCodeAt(e));)e++;if(e<n.length&&n.charCodeAt(e)===46)if(e++,e<n.length&&L(n.charCodeAt(e)))for(e++;e<n.length&&L(n.charCodeAt(e));)e++;else return b=3,n.substring(i,e);let T=e;if(e<n.length&&(n.charCodeAt(e)===69||n.charCodeAt(e)===101))if(e++,(e<n.length&&n.charCodeAt(e)===43||n.charCodeAt(e)===45)&&e++,e<n.length&&L(n.charCodeAt(e))){for(e++;e<n.length&&L(n.charCodeAt(e));)e++;T=e}else b=3;return n.substring(i,T)}function j(){let i="",T=e;for(;;){if(e>=g){i+=n.substring(T,e),b=2;break}const s=n.charCodeAt(e);if(s===34){i+=n.substring(T,e),e++;break}if(s===92){if(i+=n.substring(T,e),e++,e>=g){b=2;break}switch(n.charCodeAt(e++)){case 34:i+='"';break;case 92:i+="\\";break;case 47:i+="/";break;case 98:i+="\b";break;case 102:i+="\f";break;case 110:i+=`
`;break;case 114:i+="\r";break;case 116:i+="	";break;case 117:const t=I(4,!0);t>=0?i+=String.fromCharCode(t):b=4;break;default:b=5}T=e;continue}if(s>=0&&s<=31)if(r(s)){i+=n.substring(T,e),b=2;break}else b=6;e++}return i}function w(){if(u="",b=0,p=e,o=A,B=O,e>=g)return p=g,k=17;let i=n.charCodeAt(e);if(J(i)){do e++,u+=String.fromCharCode(i),i=n.charCodeAt(e);while(J(i));return k=15}if(r(i))return e++,u+=String.fromCharCode(i),i===13&&n.charCodeAt(e)===10&&(e++,u+=`
`),A++,O=e,k=14;switch(i){case 123:return e++,k=1;case 125:return e++,k=2;case 91:return e++,k=3;case 93:return e++,k=4;case 58:return e++,k=6;case 44:return e++,k=5;case 34:return e++,u=j(),k=10;case 47:const T=e-1;if(n.charCodeAt(e+1)===47){for(e+=2;e<g&&!r(n.charCodeAt(e));)e++;return u=n.substring(T,e),k=12}if(n.charCodeAt(e+1)===42){e+=2;const s=g-1;let c=!1;for(;e<s;){const t=n.charCodeAt(e);if(t===42&&n.charCodeAt(e+1)===47){e+=2,c=!0;break}e++,r(t)&&(t===13&&n.charCodeAt(e)===10&&e++,A++,O=e)}return c||(e++,b=1),u=n.substring(T,e),k=13}return u+=String.fromCharCode(i),e++,k=16;case 45:if(u+=String.fromCharCode(i),e++,e===g||!L(n.charCodeAt(e)))return k=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return u+=F(),k=11;default:for(;e<g&&v(i);)e++,i=n.charCodeAt(e);if(p!==e){switch(u=n.substring(p,e),u){case"true":return k=8;case"false":return k=9;case"null":return k=7}return k=16}return u+=String.fromCharCode(i),e++,k=16}}function v(i){if(J(i)||r(i))return!1;switch(i){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}function E(){let i;do i=w();while(i>=12&&i<=15);return i}return{setPosition:V,getPosition:()=>e,scan:l?E:w,getToken:()=>k,getTokenValue:()=>u,getTokenOffset:()=>p,getTokenLength:()=>e-p,getTokenStartLine:()=>o,getTokenStartCharacter:()=>p-B,getTokenError:()=>b}}function J(n){return n===32||n===9}function r(n){return n===10||n===13}function L(n){return n>=48&&n<=57}var Q;(function(n){n[n.lineFeed=10]="lineFeed",n[n.carriageReturn=13]="carriageReturn",n[n.space=32]="space",n[n._0=48]="_0",n[n._1=49]="_1",n[n._2=50]="_2",n[n._3=51]="_3",n[n._4=52]="_4",n[n._5=53]="_5",n[n._6=54]="_6",n[n._7=55]="_7",n[n._8=56]="_8",n[n._9=57]="_9",n[n.a=97]="a",n[n.b=98]="b",n[n.c=99]="c",n[n.d=100]="d",n[n.e=101]="e",n[n.f=102]="f",n[n.g=103]="g",n[n.h=104]="h",n[n.i=105]="i",n[n.j=106]="j",n[n.k=107]="k",n[n.l=108]="l",n[n.m=109]="m",n[n.n=110]="n",n[n.o=111]="o",n[n.p=112]="p",n[n.q=113]="q",n[n.r=114]="r",n[n.s=115]="s",n[n.t=116]="t",n[n.u=117]="u",n[n.v=118]="v",n[n.w=119]="w",n[n.x=120]="x",n[n.y=121]="y",n[n.z=122]="z",n[n.A=65]="A",n[n.B=66]="B",n[n.C=67]="C",n[n.D=68]="D",n[n.E=69]="E",n[n.F=70]="F",n[n.G=71]="G",n[n.H=72]="H",n[n.I=73]="I",n[n.J=74]="J",n[n.K=75]="K",n[n.L=76]="L",n[n.M=77]="M",n[n.N=78]="N",n[n.O=79]="O",n[n.P=80]="P",n[n.Q=81]="Q",n[n.R=82]="R",n[n.S=83]="S",n[n.T=84]="T",n[n.U=85]="U",n[n.V=86]="V",n[n.W=87]="W",n[n.X=88]="X",n[n.Y=89]="Y",n[n.Z=90]="Z",n[n.asterisk=42]="asterisk",n[n.backslash=92]="backslash",n[n.closeBrace=125]="closeBrace",n[n.closeBracket=93]="closeBracket",n[n.colon=58]="colon",n[n.comma=44]="comma",n[n.dot=46]="dot",n[n.doubleQuote=34]="doubleQuote",n[n.minus=45]="minus",n[n.openBrace=123]="openBrace",n[n.openBracket=91]="openBracket",n[n.plus=43]="plus",n[n.slash=47]="slash",n[n.formFeed=12]="formFeed",n[n.tab=9]="tab"})(Q||(Q={})),new Array(20).fill(0).map((n,l)=>" ".repeat(l));const N=200;new Array(N).fill(0).map((n,l)=>`
`+" ".repeat(l)),new Array(N).fill(0).map((n,l)=>"\r"+" ".repeat(l)),new Array(N).fill(0).map((n,l)=>`\r
`+" ".repeat(l)),new Array(N).fill(0).map((n,l)=>`
`+"	".repeat(l)),new Array(N).fill(0).map((n,l)=>"\r"+"	".repeat(l)),new Array(N).fill(0).map((n,l)=>`\r
`+"	".repeat(l));var U;(function(n){n.DEFAULT={allowTrailingComma:!1}})(U||(U={}));function S(n,l=[],g=U.DEFAULT){let e=null,u=[];const p=[];function k(o){Array.isArray(u)?u.push(o):e!==null&&(u[e]=o)}return P(n,{onObjectBegin:()=>{const o={};k(o),p.push(u),u=o,e=null},onObjectProperty:o=>{e=o},onObjectEnd:()=>{u=p.pop()},onArrayBegin:()=>{const o=[];k(o),p.push(u),u=o,e=null},onArrayEnd:()=>{u=p.pop()},onLiteralValue:k,onError:(o,O,B)=>{l.push({error:o,offset:O,length:B})}},g),u[0]}function P(n,l,g=U.DEFAULT){const e=$(n,!1),u=[];let p=0;function k(f){return f?()=>p===0&&f(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter()):()=>!0}function A(f){return f?m=>p===0&&f(m,e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter()):()=>!0}function o(f){return f?m=>p===0&&f(m,e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter(),()=>u.slice()):()=>!0}function O(f){return f?()=>{p>0?p++:f(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter(),()=>u.slice())===!1&&(p=1)}:()=>!0}function B(f){return f?()=>{p>0&&p--,p===0&&f(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter())}:()=>!0}const b=O(l.onObjectBegin),I=o(l.onObjectProperty),V=B(l.onObjectEnd),F=O(l.onArrayBegin),j=B(l.onArrayEnd),w=o(l.onLiteralValue),v=A(l.onSeparator),E=k(l.onComment),i=A(l.onError),T=g&&g.disallowComments,s=g&&g.allowTrailingComma;function c(){for(;;){const f=e.scan();switch(e.getTokenError()){case 4:t(14);break;case 5:t(15);break;case 3:t(13);break;case 1:T||t(11);break;case 2:t(12);break;case 6:t(16);break}switch(f){case 12:case 13:T?t(10):E();break;case 16:t(1);break;case 15:case 14:break;default:return f}}}function t(f,m=[],y=[]){if(i(f),m.length+y.length>0){let _=e.getToken();for(;_!==17;){if(m.indexOf(_)!==-1){c();break}else if(y.indexOf(_)!==-1)break;_=c()}}}function D(f){const m=e.getTokenValue();return f?w(m):(I(m),u.push(m)),c(),!0}function G(){switch(e.getToken()){case 11:const f=e.getTokenValue();let m=Number(f);isNaN(m)&&(t(2),m=0),w(m);break;case 7:w(null);break;case 8:w(!0);break;case 9:w(!1);break;default:return!1}return c(),!0}function M(){return e.getToken()!==10?(t(3,[],[2,5]),!1):(D(!1),e.getToken()===6?(v(":"),c(),a()||t(4,[],[2,5])):t(5,[],[2,5]),u.pop(),!0)}function X(){b(),c();let f=!1;for(;e.getToken()!==2&&e.getToken()!==17;){if(e.getToken()===5){if(f||t(4,[],[]),v(","),c(),e.getToken()===2&&s)break}else f&&t(6,[],[]);M()||t(4,[],[2,5]),f=!0}return V(),e.getToken()!==2?t(7,[2],[]):c(),!0}function Y(){F(),c();let f=!0,m=!1;for(;e.getToken()!==4&&e.getToken()!==17;){if(e.getToken()===5){if(m||t(4,[],[]),v(","),c(),e.getToken()===4&&s)break}else m&&t(6,[],[]);f?(u.push(0),f=!1):u[u.length-1]++,a()||t(4,[],[4,5]),m=!0}return j(),f||u.pop(),e.getToken()!==4?t(8,[4],[]):c(),!0}function a(){switch(e.getToken()){case 3:return Y();case 1:return X();case 10:return D(!0);default:return G()}}return c(),e.getToken()===17?g.allowEmptyContent?!0:(t(4,[],[]),!1):a()?(e.getToken()!==17&&t(9,[],[]),!0):(t(4,[],[]),!1)}var W;(function(n){n[n.None=0]="None",n[n.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",n[n.UnexpectedEndOfString=2]="UnexpectedEndOfString",n[n.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",n[n.InvalidUnicode=4]="InvalidUnicode",n[n.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",n[n.InvalidCharacter=6]="InvalidCharacter"})(W||(W={}));var H;(function(n){n[n.OpenBraceToken=1]="OpenBraceToken",n[n.CloseBraceToken=2]="CloseBraceToken",n[n.OpenBracketToken=3]="OpenBracketToken",n[n.CloseBracketToken=4]="CloseBracketToken",n[n.CommaToken=5]="CommaToken",n[n.ColonToken=6]="ColonToken",n[n.NullKeyword=7]="NullKeyword",n[n.TrueKeyword=8]="TrueKeyword",n[n.FalseKeyword=9]="FalseKeyword",n[n.StringLiteral=10]="StringLiteral",n[n.NumericLiteral=11]="NumericLiteral",n[n.LineCommentTrivia=12]="LineCommentTrivia",n[n.BlockCommentTrivia=13]="BlockCommentTrivia",n[n.LineBreakTrivia=14]="LineBreakTrivia",n[n.Trivia=15]="Trivia",n[n.Unknown=16]="Unknown",n[n.EOF=17]="EOF"})(H||(H={}));const K=S;var q;(function(n){n[n.InvalidSymbol=1]="InvalidSymbol",n[n.InvalidNumberFormat=2]="InvalidNumberFormat",n[n.PropertyNameExpected=3]="PropertyNameExpected",n[n.ValueExpected=4]="ValueExpected",n[n.ColonExpected=5]="ColonExpected",n[n.CommaExpected=6]="CommaExpected",n[n.CloseBraceExpected=7]="CloseBraceExpected",n[n.CloseBracketExpected=8]="CloseBracketExpected",n[n.EndOfFileExpected=9]="EndOfFileExpected",n[n.InvalidCommentToken=10]="InvalidCommentToken",n[n.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",n[n.UnexpectedEndOfString=12]="UnexpectedEndOfString",n[n.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",n[n.InvalidUnicode=14]="InvalidUnicode",n[n.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",n[n.InvalidCharacter=16]="InvalidCharacter"})(q||(q={}));function x(n,l){const g=JSON.parse(n,l?.reviver);return R(n,g,l),g}function z(n,l){const g=Z(n,l),e=JSON.stringify(n,l?.replacer,g.indent);return g.whitespace.start+e+g.whitespace.end}function h(n,l){const g=K(n,l?.errors,l);return R(n,g,l),g}function d(n,l){return z(n,l)}export{x as a,z as b,h as p,d as s};
