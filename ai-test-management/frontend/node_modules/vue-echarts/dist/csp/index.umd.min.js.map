{"version": 3, "file": "index.umd.min.js", "sources": ["../../node_modules/.pnpm/tslib@2.6.2/node_modules/tslib/tslib.es6.js", "../../src/composables/api.ts", "../../node_modules/.pnpm/resize-detector@0.3.0/node_modules/resize-detector/esm/index.js", "../../src/composables/autoresize.ts", "../../src/utils.ts", "../../src/composables/loading.ts", "../../src/wc.ts", "../../src/ECharts.ts", "../../src/global.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Ref } from \"vue-demi\";\nimport { EChartsType } from \"../types\";\n\nconst METHOD_NAMES = [\n  \"getWidth\",\n  \"getHeight\",\n  \"getDom\",\n  \"getOption\",\n  \"resize\",\n  \"dispatchAction\",\n  \"convertToPixel\",\n  \"convertFromPixel\",\n  \"containPixel\",\n  \"getDataURL\",\n  \"getConnectedDataURL\",\n  \"appendData\",\n  \"clear\",\n  \"isDisposed\",\n  \"dispose\"\n] as const;\n\ntype MethodName = (typeof METHOD_NAMES)[number];\n\ntype PublicMethods = Pick<EChartsType, MethodName>;\n\nexport function usePublicAPI(\n  chart: Ref<EChartsType | undefined>\n): PublicMethods {\n  function makePublicMethod<T extends MethodName>(\n    name: T\n  ): (...args: Parameters<EChartsType[T]>) => ReturnType<EChartsType[T]> {\n    return (...args) => {\n      if (!chart.value) {\n        throw new Error(\"ECharts is not initialized yet.\");\n      }\n      return (chart.value[name] as any).apply(chart.value, args);\n    };\n  }\n\n  function makePublicMethods(): PublicMethods {\n    const methods = Object.create(null);\n    METHOD_NAMES.forEach(name => {\n      methods[name] = makePublicMethod(name);\n    });\n\n    return methods as PublicMethods;\n  }\n\n  return makePublicMethods();\n}\n", "var raf = null;\nfunction requestAnimationFrame (callback) {\n  if (!raf) {\n    raf = (\n      window.requestAnimationFrame ||\n      window.webkitRequestAnimationFrame ||\n      window.mozRequestAnimationFrame ||\n      function (callback) {\n        return setTimeout(callback, 16)\n      }\n    ).bind(window);\n  }\n  return raf(callback)\n}\n\nvar caf = null;\nfunction cancelAnimationFrame (id) {\n  if (!caf) {\n    caf = (\n      window.cancelAnimationFrame ||\n      window.webkitCancelAnimationFrame ||\n      window.mozCancelAnimationFrame ||\n      function (id) {\n        clearTimeout(id);\n      }\n    ).bind(window);\n  }\n\n  caf(id);\n}\n\nfunction createStyles (styleText) {\n  var style = document.createElement('style');\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = styleText;\n  } else {\n    style.appendChild(document.createTextNode(styleText));\n  }\n  (document.querySelector('head') || document.body).appendChild(style);\n  return style\n}\n\nfunction createElement (tagName, props) {\n  if ( props === void 0 ) props = {};\n\n  var elem = document.createElement(tagName);\n  Object.keys(props).forEach(function (key) {\n    elem[key] = props[key];\n  });\n  return elem\n}\n\nfunction getComputedStyle (elem, prop, pseudo) {\n  // for older versions of Firefox, `getComputedStyle` required\n  // the second argument and may return `null` for some elements\n  // when `display: none`\n  var computedStyle = window.getComputedStyle(elem, pseudo || null) || {\n    display: 'none'\n  };\n\n  return computedStyle[prop]\n}\n\nfunction getRenderInfo (elem) {\n  if (!document.documentElement.contains(elem)) {\n    return {\n      detached: true,\n      rendered: false\n    }\n  }\n\n  var current = elem;\n  while (current !== document) {\n    if (getComputedStyle(current, 'display') === 'none') {\n      return {\n        detached: false,\n        rendered: false\n      }\n    }\n    current = current.parentNode;\n  }\n\n  return {\n    detached: false,\n    rendered: true\n  }\n}\n\nvar css_248z = \".resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:\\\"\\\";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}\";\n\nvar total = 0;\nvar style = null;\n\nfunction addListener (elem, callback) {\n  if (!elem.__resize_mutation_handler__) {\n    elem.__resize_mutation_handler__ = handleMutation.bind(elem);\n  }\n\n  var listeners = elem.__resize_listeners__;\n\n  if (!listeners) {\n    elem.__resize_listeners__ = [];\n    if (window.ResizeObserver) {\n      var offsetWidth = elem.offsetWidth;\n      var offsetHeight = elem.offsetHeight;\n      var ro = new ResizeObserver(function () {\n        if (!elem.__resize_observer_triggered__) {\n          elem.__resize_observer_triggered__ = true;\n          if (elem.offsetWidth === offsetWidth && elem.offsetHeight === offsetHeight) {\n            return\n          }\n        }\n        runCallbacks(elem);\n      });\n\n      // initially display none won't trigger ResizeObserver callback\n      var ref = getRenderInfo(elem);\n      var detached = ref.detached;\n      var rendered = ref.rendered;\n      elem.__resize_observer_triggered__ = detached === false && rendered === false;\n      elem.__resize_observer__ = ro;\n      ro.observe(elem);\n    } else if (elem.attachEvent && elem.addEventListener) {\n      // targeting IE9/10\n      elem.__resize_legacy_resize_handler__ = function handleLegacyResize () {\n        runCallbacks(elem);\n      };\n      elem.attachEvent('onresize', elem.__resize_legacy_resize_handler__);\n      document.addEventListener('DOMSubtreeModified', elem.__resize_mutation_handler__);\n    } else {\n      if (!total) {\n        style = createStyles(css_248z);\n      }\n      initTriggers(elem);\n\n      elem.__resize_rendered__ = getRenderInfo(elem).rendered;\n      if (window.MutationObserver) {\n        var mo = new MutationObserver(elem.__resize_mutation_handler__);\n        mo.observe(document, {\n          attributes: true,\n          childList: true,\n          characterData: true,\n          subtree: true\n        });\n        elem.__resize_mutation_observer__ = mo;\n      }\n    }\n  }\n\n  elem.__resize_listeners__.push(callback);\n  total++;\n}\n\nfunction removeListener (elem, callback) {\n  var listeners = elem.__resize_listeners__;\n  if (!listeners) {\n    return\n  }\n\n  if (callback) {\n    listeners.splice(listeners.indexOf(callback), 1);\n  }\n\n  // no listeners exist, or removing all listeners\n  if (!listeners.length || !callback) {\n    // targeting IE9/10\n    if (elem.detachEvent && elem.removeEventListener) {\n      elem.detachEvent('onresize', elem.__resize_legacy_resize_handler__);\n      document.removeEventListener('DOMSubtreeModified', elem.__resize_mutation_handler__);\n      return\n    }\n\n    if (elem.__resize_observer__) {\n      elem.__resize_observer__.unobserve(elem);\n      elem.__resize_observer__.disconnect();\n      elem.__resize_observer__ = null;\n    } else {\n      if (elem.__resize_mutation_observer__) {\n        elem.__resize_mutation_observer__.disconnect();\n        elem.__resize_mutation_observer__ = null;\n      }\n      elem.removeEventListener('scroll', handleScroll);\n      elem.removeChild(elem.__resize_triggers__.triggers);\n      elem.__resize_triggers__ = null;\n    }\n    elem.__resize_listeners__ = null;\n  }\n\n  if (!--total && style) {\n    style.parentNode.removeChild(style);\n  }\n}\n\nfunction getUpdatedSize (elem) {\n  var ref = elem.__resize_last__;\n  var width = ref.width;\n  var height = ref.height;\n  var offsetWidth = elem.offsetWidth;\n  var offsetHeight = elem.offsetHeight;\n  if (offsetWidth !== width || offsetHeight !== height) {\n    return {\n      width: offsetWidth,\n      height: offsetHeight\n    }\n  }\n  return null\n}\n\nfunction handleMutation () {\n  // `this` denotes the scrolling element\n  var ref = getRenderInfo(this);\n  var rendered = ref.rendered;\n  var detached = ref.detached;\n  if (rendered !== this.__resize_rendered__) {\n    if (!detached && this.__resize_triggers__) {\n      resetTriggers(this);\n      this.addEventListener('scroll', handleScroll, true);\n    }\n    this.__resize_rendered__ = rendered;\n    runCallbacks(this);\n  }\n}\n\nfunction handleScroll () {\n  var this$1 = this;\n\n  // `this` denotes the scrolling element\n  resetTriggers(this);\n  if (this.__resize_raf__) {\n    cancelAnimationFrame(this.__resize_raf__);\n  }\n  this.__resize_raf__ = requestAnimationFrame(function () {\n    var updated = getUpdatedSize(this$1);\n    if (updated) {\n      this$1.__resize_last__ = updated;\n      runCallbacks(this$1);\n    }\n  });\n}\n\nfunction runCallbacks (elem) {\n  if (!elem || !elem.__resize_listeners__) {\n    return\n  }\n  elem.__resize_listeners__.forEach(function (callback) {\n    callback.call(elem, elem);\n  });\n}\n\nfunction initTriggers (elem) {\n  var position = getComputedStyle(elem, 'position');\n  if (!position || position === 'static') {\n    elem.style.position = 'relative';\n  }\n\n  elem.__resize_old_position__ = position;\n  elem.__resize_last__ = {};\n\n  var triggers = createElement('div', {\n    className: 'resize-triggers'\n  });\n  var expand = createElement('div', {\n    className: 'resize-expand-trigger'\n  });\n  var expandChild = createElement('div');\n  var contract = createElement('div', {\n    className: 'resize-contract-trigger'\n  });\n  expand.appendChild(expandChild);\n  triggers.appendChild(expand);\n  triggers.appendChild(contract);\n  elem.appendChild(triggers);\n\n  elem.__resize_triggers__ = {\n    triggers: triggers,\n    expand: expand,\n    expandChild: expandChild,\n    contract: contract\n  };\n\n  resetTriggers(elem);\n  elem.addEventListener('scroll', handleScroll, true);\n\n  elem.__resize_last__ = {\n    width: elem.offsetWidth,\n    height: elem.offsetHeight\n  };\n}\n\nfunction resetTriggers (elem) {\n  var ref = elem.__resize_triggers__;\n  var expand = ref.expand;\n  var expandChild = ref.expandChild;\n  var contract = ref.contract;\n\n  // batch read\n  var csw = contract.scrollWidth;\n  var csh = contract.scrollHeight;\n  var eow = expand.offsetWidth;\n  var eoh = expand.offsetHeight;\n  var esw = expand.scrollWidth;\n  var esh = expand.scrollHeight;\n\n  // batch write\n  contract.scrollLeft = csw;\n  contract.scrollTop = csh;\n  expandChild.style.width = eow + 1 + 'px';\n  expandChild.style.height = eoh + 1 + 'px';\n  expand.scrollLeft = esw;\n  expand.scrollTop = esh;\n}\n\nexport { addListener, removeListener };\n", "import { watch, type Ref, type PropType } from \"vue-demi\";\nimport { throttle } from \"echarts/core\";\nimport {\n  addListener,\n  removeListener,\n  type ResizeCallback\n} from \"resize-detector\";\nimport { type EChartsType } from \"../types\";\n\ntype AutoresizeProp =\n  | boolean\n  | {\n      throttle?: number;\n      onResize?: () => void;\n    };\n\nexport function useAutoresize(\n  chart: Ref<EChartsType | undefined>,\n  autoresize: Ref<AutoresizeProp | undefined>,\n  root: Ref<HTMLElement | undefined>\n): void {\n  let resizeListener: ResizeCallback | null = null;\n\n  watch([root, chart, autoresize], ([root, chart, autoresize], _, cleanup) => {\n    if (root && chart && autoresize) {\n      const autoresizeOptions = autoresize === true ? {} : autoresize;\n      const { throttle: wait = 100, onResize } = autoresizeOptions;\n\n      const callback = () => {\n        chart.resize();\n        onResize?.();\n      };\n\n      resizeListener = wait ? throttle(callback, wait) : callback;\n      addListener(root, resizeListener);\n    }\n\n    cleanup(() => {\n      if (root && resizeListener) {\n        removeListener(root, resizeListener);\n      }\n    });\n  });\n}\n\nexport const autoresizeProps = {\n  autoresize: [Boolean, Object] as PropType<AutoresizeProp>\n};\n", "import { unref, isRef } from \"vue-demi\";\nimport type { Injection } from \"./types\";\n\ntype Attrs = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key: string]: any;\n};\n\n// Copied from\n// https://github.com/vuejs/vue-next/blob/5a7a1b8293822219283d6e267496bec02234b0bc/packages/shared/src/index.ts#L40-L41\nconst onRE = /^on[^a-z]/;\nexport const isOn = (key: string): boolean => onRE.test(key);\n\nexport function omitOn(attrs: Attrs): Attrs {\n  const result: Attrs = {};\n  for (const key in attrs) {\n    if (!isOn(key)) {\n      result[key] = attrs[key];\n    }\n  }\n\n  return result;\n}\n\nexport function unwrapInjected<T, V>(\n  injection: Injection<T>,\n  defaultValue: V\n): T | V {\n  const value = isRef(injection) ? unref(injection) : injection;\n\n  if (value && typeof value === \"object\" && \"value\" in value) {\n    return value.value || defaultValue;\n  }\n\n  return value || defaultValue;\n}\n", "import { unwrapInjected } from \"../utils\";\nimport {\n  inject,\n  computed,\n  watchEffect,\n  type Ref,\n  type InjectionKey,\n  type PropType\n} from \"vue-demi\";\nimport type { EChartsType, LoadingOptions } from \"../types\";\n\nexport const LOADING_OPTIONS_KEY =\n  \"ecLoadingOptions\" as unknown as InjectionKey<\n    LoadingOptions | Ref<LoadingOptions>\n  >;\n\nexport function useLoading(\n  chart: Ref<EChartsType | undefined>,\n  loading: Ref<boolean>,\n  loadingOptions: Ref<LoadingOptions | undefined>\n): void {\n  const defaultLoadingOptions = inject(LOADING_OPTIONS_KEY, {});\n  const realLoadingOptions = computed(() => ({\n    ...unwrapInjected(defaultLoadingOptions, {}),\n    ...loadingOptions?.value\n  }));\n\n  watchEffect(() => {\n    const instance = chart.value;\n    if (!instance) {\n      return;\n    }\n\n    if (loading.value) {\n      instance.showLoading(realLoadingOptions.value);\n    } else {\n      instance.hideLoading();\n    }\n  });\n}\n\nexport const loadingProps = {\n  loading: Boolean,\n  loadingOptions: Object as PropType<LoadingOptions>\n};\n", "let registered: boolean | null = null;\n\nexport const TAG_NAME = \"x-vue-echarts\";\n\nexport interface EChartsElement extends HTMLElement {\n  __dispose: (() => void) | null;\n}\n\nexport function register(): boolean {\n  if (registered != null) {\n    return registered;\n  }\n\n  if (\n    typeof HTMLElement === \"undefined\" ||\n    typeof customElements === \"undefined\"\n  ) {\n    return (registered = false);\n  }\n\n  try {\n    // Class definitions cannot be transpiled to ES5\n    // so we are doing a little trick here to ensure\n    // we are using native classes. As we use this as\n    // a progressive enhancement, it will be fine even\n    // if the browser doesn't support native classes.\n    const reg = new Function(\n      \"tag\",\n      `class EChartsElement extends HTMLElement {\n  __dispose = null;\n\n  disconnectedCallback() {\n    if (this.__dispose) {\n      this.__dispose();\n      this.__dispose = null;\n    }\n  }\n}\n\nif (customElements.get(tag) == null) {\n  customElements.define(tag, EChartsElement);\n}\n`\n    );\n    reg(TAG_NAME);\n  } catch (e) {\n    return (registered = false);\n  }\n\n  return (registered = true);\n}\n", "/* eslint-disable vue/multi-word-component-names */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n  defineComponent,\n  shallowRef,\n  toRefs,\n  watch,\n  computed,\n  inject,\n  onMounted,\n  onBeforeUnmount,\n  h,\n  nextTick,\n  watchEffect,\n  getCurrentInstance,\n  Vue2,\n  type PropType,\n  type InjectionKey\n} from \"vue-demi\";\nimport { init as initChart } from \"echarts/core\";\nimport type {\n  EChartsType,\n  EventTarget,\n  Option,\n  Theme,\n  ThemeInjection,\n  InitOptions,\n  InitOptionsInjection,\n  UpdateOptions,\n  UpdateOptionsInjection,\n  Emits\n} from \"./types\";\nimport {\n  usePublicAPI,\n  useAutoresize,\n  autoresizeProps,\n  useLoading,\n  loadingProps\n} from \"./composables\";\nimport { isOn, omitOn, unwrapInjected } from \"./utils\";\nimport { register, TAG_NAME, type EChartsElement } from \"./wc\";\nimport \"./style.css\";\n\nconst __CSP__ = false;\nconst wcRegistered = __CSP__ ? false : register();\n\nif (Vue2) {\n  Vue2.config.ignoredElements.push(TAG_NAME);\n}\n\nexport const THEME_KEY = \"ecTheme\" as unknown as InjectionKey<ThemeInjection>;\nexport const INIT_OPTIONS_KEY =\n  \"ecInitOptions\" as unknown as InjectionKey<InitOptionsInjection>;\nexport const UPDATE_OPTIONS_KEY =\n  \"ecUpdateOptions\" as unknown as InjectionKey<UpdateOptionsInjection>;\nexport { LOADING_OPTIONS_KEY } from \"./composables\";\n\nconst NATIVE_EVENT_RE = /(^&?~?!?)native:/;\n\nexport default defineComponent({\n  name: \"echarts\",\n  props: {\n    option: Object as PropType<Option>,\n    theme: {\n      type: [Object, String] as PropType<Theme>\n    },\n    initOptions: Object as PropType<InitOptions>,\n    updateOptions: Object as PropType<UpdateOptions>,\n    group: String,\n    manualUpdate: Boolean,\n    ...autoresizeProps,\n    ...loadingProps\n  },\n  emits: {} as unknown as Emits,\n  inheritAttrs: false,\n  setup(props, { attrs }) {\n    const root = shallowRef<EChartsElement>();\n    const inner = shallowRef<HTMLElement>();\n    const chart = shallowRef<EChartsType>();\n    const manualOption = shallowRef<Option>();\n    const defaultTheme = inject(THEME_KEY, null);\n    const defaultInitOptions = inject(INIT_OPTIONS_KEY, null);\n    const defaultUpdateOptions = inject(UPDATE_OPTIONS_KEY, null);\n\n    const { autoresize, manualUpdate, loading, loadingOptions } = toRefs(props);\n\n    const realOption = computed(\n      () => manualOption.value || props.option || null\n    );\n    const realTheme = computed(\n      () => props.theme || unwrapInjected(defaultTheme, {})\n    );\n    const realInitOptions = computed(\n      () => props.initOptions || unwrapInjected(defaultInitOptions, {})\n    );\n    const realUpdateOptions = computed(\n      () => props.updateOptions || unwrapInjected(defaultUpdateOptions, {})\n    );\n    const nonEventAttrs = computed(() => omitOn(attrs));\n    const nativeListeners: Record<string, unknown> = {};\n\n    // @ts-expect-error listeners for Vue 2 compatibility\n    const listeners = getCurrentInstance().proxy.$listeners;\n    const realListeners: Record<string, any> = {};\n\n    if (!listeners) {\n      // This is for Vue 3.\n      // We are converting all `on<Event>` props to event listeners compatible with Vue 2\n      // and collect them into `realListeners` so that we can bind them to the chart instance\n      // later in the same way.\n      // For `onNative:<event>` props, we just strip the `Native:` part and collect them into\n      // `nativeListeners` so that we can bind them to the root element directly.\n      Object.keys(attrs)\n        .filter(key => isOn(key))\n        .forEach(key => {\n          // onClick    -> c + lick\n          // onZr:click -> z + r:click\n          let event = key.charAt(2).toLowerCase() + key.slice(3);\n\n          // Collect native DOM events\n          if (event.indexOf(\"native:\") === 0) {\n            // native:click -> onClick\n            const nativeKey = `on${event.charAt(7).toUpperCase()}${event.slice(\n              8\n            )}`;\n\n            nativeListeners[nativeKey] = attrs[key];\n            return;\n          }\n\n          // clickOnce    -> ~click\n          // zr:clickOnce -> ~zr:click\n          if (event.substring(event.length - 4) === \"Once\") {\n            event = `~${event.substring(0, event.length - 4)}`;\n          }\n\n          realListeners[event] = attrs[key];\n        });\n    } else {\n      // This is for Vue 2.\n      // We just need to distinguish normal events and `native:<event>` events and\n      // collect them into `realListeners` and `nativeListeners` respectively.\n      // For `native:<event>` events, we just strip the `native:` part and collect them\n      // into `nativeListeners` so that we can bind them to the root element directly.\n      // native:click   -> click\n      // ~native:click  -> ~click\n      // &~!native:click -> &~!click\n      Object.keys(listeners).forEach(key => {\n        if (NATIVE_EVENT_RE.test(key)) {\n          nativeListeners[key.replace(NATIVE_EVENT_RE, \"$1\")] = listeners[key];\n        } else {\n          realListeners[key] = listeners[key];\n        }\n      });\n    }\n\n    function init(option?: Option) {\n      if (!inner.value) {\n        return;\n      }\n\n      const instance = (chart.value = initChart(\n        inner.value,\n        realTheme.value,\n        realInitOptions.value\n      ));\n\n      if (props.group) {\n        instance.group = props.group;\n      }\n\n      Object.keys(realListeners).forEach(key => {\n        let handler = realListeners[key];\n\n        if (!handler) {\n          return;\n        }\n\n        let event = key.toLowerCase();\n        if (event.charAt(0) === \"~\") {\n          event = event.substring(1);\n          handler.__once__ = true;\n        }\n\n        let target: EventTarget = instance;\n        if (event.indexOf(\"zr:\") === 0) {\n          target = instance.getZr();\n          event = event.substring(3);\n        }\n\n        if (handler.__once__) {\n          delete handler.__once__;\n\n          const raw = handler;\n\n          handler = (...args: any[]) => {\n            raw(...args);\n            target.off(event, handler);\n          };\n        }\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore EChartsType[\"on\"] is not compatible with ZRenderType[\"on\"]\n        // but it's okay here\n        target.on(event, handler);\n      });\n\n      function resize() {\n        if (instance && !instance.isDisposed()) {\n          instance.resize();\n        }\n      }\n\n      function commit() {\n        const opt = option || realOption.value;\n        if (opt) {\n          instance.setOption(opt, realUpdateOptions.value);\n        }\n      }\n\n      if (autoresize.value) {\n        // Try to make chart fit to container in case container size\n        // is changed synchronously or in already queued microtasks\n        nextTick(() => {\n          resize();\n          commit();\n        });\n      } else {\n        commit();\n      }\n    }\n\n    function setOption(option: Option, updateOptions?: UpdateOptions) {\n      if (props.manualUpdate) {\n        manualOption.value = option;\n      }\n\n      if (!chart.value) {\n        init(option);\n      } else {\n        chart.value.setOption(option, updateOptions || {});\n      }\n    }\n\n    function cleanup() {\n      if (chart.value) {\n        chart.value.dispose();\n        chart.value = undefined;\n      }\n    }\n\n    let unwatchOption: (() => void) | null = null;\n    watch(\n      manualUpdate,\n      manualUpdate => {\n        if (typeof unwatchOption === \"function\") {\n          unwatchOption();\n          unwatchOption = null;\n        }\n\n        if (!manualUpdate) {\n          unwatchOption = watch(\n            () => props.option,\n            (option, oldOption) => {\n              if (!option) {\n                return;\n              }\n              if (!chart.value) {\n                init();\n              } else {\n                chart.value.setOption(option, {\n                  // mutating `option` will lead to `notMerge: false` and\n                  // replacing it with new reference will lead to `notMerge: true`\n                  notMerge: option !== oldOption,\n                  ...realUpdateOptions.value\n                });\n              }\n            },\n            { deep: true }\n          );\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      [realTheme, realInitOptions],\n      () => {\n        cleanup();\n        init();\n      },\n      {\n        deep: true\n      }\n    );\n\n    watchEffect(() => {\n      if (props.group && chart.value) {\n        chart.value.group = props.group;\n      }\n    });\n\n    const publicApi = usePublicAPI(chart);\n\n    useLoading(chart, loading, loadingOptions);\n\n    useAutoresize(chart, autoresize, inner);\n\n    onMounted(() => {\n      init();\n    });\n\n    onBeforeUnmount(() => {\n      if (wcRegistered && root.value) {\n        // For registered web component, we can leverage the\n        // `disconnectedCallback` to dispose the chart instance\n        // so that we can delay the cleanup after exsiting leaving\n        // transition.\n        root.value.__dispose = cleanup;\n      } else {\n        cleanup();\n      }\n    });\n\n    return {\n      chart,\n      root,\n      inner,\n      setOption,\n      nonEventAttrs,\n      nativeListeners,\n      ...publicApi\n    };\n  },\n  render() {\n    // Vue 3 and Vue 2 have different vnode props format:\n    // See https://v3-migration.vuejs.org/breaking-changes/render-function-api.html#vnode-props-format\n    const attrs = (\n      Vue2\n        ? { attrs: this.nonEventAttrs, on: this.nativeListeners }\n        : { ...this.nonEventAttrs, ...this.nativeListeners }\n    ) as any;\n    attrs.ref = \"root\";\n    attrs.class = attrs.class ? [\"echarts\"].concat(attrs.class) : \"echarts\";\n    return h(TAG_NAME, attrs, [\n      h(\"div\", { ref: \"inner\", class: \"vue-echarts-inner\" })\n    ]);\n  }\n});\n", "import \"echarts\";\nimport ECharts, * as exported from \"./index\";\n\nexport default {\n  ...ECharts,\n  ...exported\n};\n"], "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "SuppressedError", "METHOD_NAMES", "usePublicAPI", "chart", "methods", "create", "for<PERSON>ach", "name", "args", "_i", "value", "Error", "makePublicMethod", "raf", "caf", "createElement", "tagName", "props", "elem", "document", "keys", "key", "getComputedStyle", "prop", "pseudo", "window", "display", "getRenderInfo", "documentElement", "contains", "detached", "rendered", "current", "parentNode", "css_248z", "total", "style", "addListener", "callback", "__resize_mutation_handler__", "handleMutation", "bind", "__resize_listeners__", "ResizeObserver", "offsetWidth", "offsetHeight", "ro", "__resize_observer_triggered__", "runCallbacks", "ref", "__resize_observer__", "observe", "attachEvent", "addEventListener", "__resize_legacy_resize_handler__", "styleText", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "querySelector", "body", "createStyles", "position", "__resize_old_position__", "__resize_last__", "triggers", "className", "expand", "expand<PERSON><PERSON>d", "contract", "__resize_triggers__", "resetTriggers", "handleScroll", "width", "height", "initTriggers", "__resize_rendered__", "MutationObserver", "mo", "attributes", "childList", "characterData", "subtree", "__resize_mutation_observer__", "push", "id", "this$1", "__resize_raf__", "cancelAnimationFrame", "webkitCancelAnimationFrame", "mozCancelAnimationFrame", "clearTimeout", "updated", "requestAnimationFrame", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "setTimeout", "csw", "scrollWidth", "csh", "scrollHeight", "eow", "eoh", "esw", "esh", "scrollLeft", "scrollTop", "useAutoresize", "autoresize", "root", "resizeListener", "watch", "_a", "_", "cleanup", "autoresizeOptions", "_b", "throttle", "wait", "onResize_1", "resize", "listeners", "splice", "indexOf", "detachEvent", "removeEventListener", "unobserve", "disconnect", "<PERSON><PERSON><PERSON><PERSON>", "removeListener", "autoresizeProps", "Boolean", "onRE", "isOn", "test", "unwrapInjected", "injection", "defaultValue", "isRef", "unref", "LOADING_OPTIONS_KEY", "loadingProps", "loading", "loadingOptions", "TAG_NAME", "Vue2", "config", "ignoredElements", "THEME_KEY", "INIT_OPTIONS_KEY", "UPDATE_OPTIONS_KEY", "NATIVE_EVENT_RE", "<PERSON><PERSON><PERSON>", "defineComponent", "option", "theme", "type", "String", "initOptions", "updateOptions", "group", "manualUpdate", "emits", "inheritAttrs", "setup", "attrs", "shallowRef", "inner", "manualOption", "defaultTheme", "inject", "defaultInitOptions", "defaultUpdateOptions", "toRefs", "realOption", "computed", "realTheme", "realInitOptions", "realUpdateOptions", "nonEventAttrs", "result", "omitOn", "nativeListeners", "getCurrentInstance", "proxy", "$listeners", "realListeners", "init", "instance", "initChart", "handler", "event", "toLowerCase", "char<PERSON>t", "substring", "__once__", "target", "getZr", "raw_1", "off", "on", "nextTick", "isDisposed", "commit", "opt", "setOption", "dispose", "undefined", "replace", "filter", "slice", "concat", "<PERSON><PERSON><PERSON>", "toUpperCase", "unwatchOption", "oldOption", "notMerge", "deep", "immediate", "watchEffect", "publicApi", "defaultLoadingOptions", "realLoadingOptions", "showLoading", "hideLoading", "useLoading", "onMounted", "onBeforeUnmount", "render", "h", "class", "exported"], "mappings": "y9DA+BO,IAAIA,EAAW,WAQlB,OAPAA,EAAWC,OAAOC,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAII,KADTL,EAAIG,UAAUF,GACOJ,OAAOS,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE9E,OAAON,CACV,EACMH,EAASa,MAAMC,KAAMP,UAChC,EAmRkD,mBAApBQ,iBAAiCA,gBCvT/D,IAAMC,EAAe,CACnB,WACA,YACA,SACA,YACA,SACA,iBACA,iBACA,mBACA,eACA,aACA,sBACA,aACA,QACA,aACA,WAOI,SAAUC,EACdC,GAsBA,OARQC,EAAUlB,OAAOmB,OAAO,MAC9BJ,EAAaK,SAAQ,SAAAC,GACnBH,EAAQG,GAdZ,SACEA,GAEA,OAAO,eAAC,IAAOC,EAAA,GAAAC,EAAA,EAAPA,EAAOjB,UAAAC,OAAPgB,IAAAD,EAAOC,GAAAjB,UAAAiB,GACb,IAAKN,EAAMO,MACT,MAAM,IAAIC,MAAM,mCAElB,OAAQR,EAAMO,MAAMH,GAAcT,MAAMK,EAAMO,MAAOF,EACvD,CACD,CAKmBI,CAAiBL,EACnC,IAEOH,EANT,IACQA,CASV,CClDA,IAAIS,EAAM,KAeV,IAAIC,EAAM,KA4BV,SAASC,EAAeC,EAASC,QAChB,IAAVA,IAAmBA,EAAQ,CAAA,GAEhC,IAAIC,EAAOC,SAASJ,cAAcC,GAIlC,OAHA9B,OAAOkC,KAAKH,GAAOX,SAAQ,SAAUe,GACnCH,EAAKG,GAAOJ,EAAMI,EACtB,IACSH,CACT,CAEA,SAASI,EAAkBJ,EAAMK,EAAMC,GAQrC,OAJoBC,OAAOH,iBAAiBJ,EAAMM,GAAU,OAAS,CACnEE,QAAS,SAGUH,EACvB,CAEA,SAASI,EAAeT,GACtB,IAAKC,SAASS,gBAAgBC,SAASX,GACrC,MAAO,CACLY,UAAU,EACVC,UAAU,GAKd,IADA,IAAIC,EAAUd,EACPc,IAAYb,UAAU,CAC3B,GAA6C,SAAzCG,EAAiBU,EAAS,WAC5B,MAAO,CACLF,UAAU,EACVC,UAAU,GAGdC,EAAUA,EAAQC,UACnB,CAED,MAAO,CACLH,UAAU,EACVC,UAAU,EAEd,CAEA,IAAIG,EAAW,2XAEXC,EAAQ,EACRC,EAAQ,KAEZ,SAASC,EAAanB,EAAMoB,GAO1B,GANKpB,EAAKqB,8BACRrB,EAAKqB,4BAA8BC,EAAeC,KAAKvB,KAGzCA,EAAKwB,qBAInB,GADAxB,EAAKwB,qBAAuB,GACxBjB,OAAOkB,eAAgB,CACzB,IAAIC,EAAc1B,EAAK0B,YACnBC,EAAe3B,EAAK2B,aACpBC,EAAK,IAAIH,gBAAe,YACrBzB,EAAK6B,gCACR7B,EAAK6B,+BAAgC,EACjC7B,EAAK0B,cAAgBA,GAAe1B,EAAK2B,eAAiBA,KAIhEG,EAAa9B,EACrB,IAGU+B,EAAMtB,EAAcT,GACpBY,EAAWmB,EAAInB,SACfC,EAAWkB,EAAIlB,SACnBb,EAAK6B,+BAA6C,IAAbjB,IAAmC,IAAbC,EAC3Db,EAAKgC,oBAAsBJ,EAC3BA,EAAGK,QAAQjC,EACZ,MAAM,GAAIA,EAAKkC,aAAelC,EAAKmC,iBAElCnC,EAAKoC,iCAAmC,WACtCN,EAAa9B,EACrB,EACMA,EAAKkC,YAAY,WAAYlC,EAAKoC,kCAClCnC,SAASkC,iBAAiB,qBAAsBnC,EAAKqB,kCAQrD,GANKJ,IACHC,EArGR,SAAuBmB,GACrB,IAAInB,EAAQjB,SAASJ,cAAc,SAQnC,OANIqB,EAAMoB,WACRpB,EAAMoB,WAAWC,QAAUF,EAE3BnB,EAAMsB,YAAYvC,SAASwC,eAAeJ,KAE3CpC,SAASyC,cAAc,SAAWzC,SAAS0C,MAAMH,YAAYtB,GACvDA,CACT,CA2FgB0B,CAAa5B,IAsH7B,SAAuBhB,GACrB,IAAI6C,EAAWzC,EAAiBJ,EAAM,YACjC6C,GAAyB,WAAbA,IACf7C,EAAKkB,MAAM2B,SAAW,YAGxB7C,EAAK8C,wBAA0BD,EAC/B7C,EAAK+C,gBAAkB,GAEvB,IAAIC,EAAWnD,EAAc,MAAO,CAClCoD,UAAW,oBAETC,EAASrD,EAAc,MAAO,CAChCoD,UAAW,0BAETE,EAActD,EAAc,OAC5BuD,EAAWvD,EAAc,MAAO,CAClCoD,UAAW,4BAEbC,EAAOV,YAAYW,GACnBH,EAASR,YAAYU,GACrBF,EAASR,YAAYY,GACrBpD,EAAKwC,YAAYQ,GAEjBhD,EAAKqD,oBAAsB,CACzBL,SAAUA,EACVE,OAAQA,EACRC,YAAaA,EACbC,SAAUA,GAGZE,EAActD,GACdA,EAAKmC,iBAAiB,SAAUoB,GAAc,GAE9CvD,EAAK+C,gBAAkB,CACrBS,MAAOxD,EAAK0B,YACZ+B,OAAQzD,EAAK2B,aAEjB,CA1JM+B,CAAa1D,GAEbA,EAAK2D,oBAAsBlD,EAAcT,GAAMa,SAC3CN,OAAOqD,iBAAkB,CAC3B,IAAIC,EAAK,IAAID,iBAAiB5D,EAAKqB,6BACnCwC,EAAG5B,QAAQhC,SAAU,CACnB6D,YAAY,EACZC,WAAW,EACXC,eAAe,EACfC,SAAS,IAEXjE,EAAKkE,6BAA+BL,CACrC,CAIL7D,EAAKwB,qBAAqB2C,KAAK/C,GAC/BH,GACF,CAyDA,SAASK,IAEP,IAAIS,EAAMtB,EAAc5B,MACpBgC,EAAWkB,EAAIlB,SACfD,EAAWmB,EAAInB,SACfC,IAAahC,KAAK8E,uBACf/C,GAAY/B,KAAKwE,sBACpBC,EAAczE,MACdA,KAAKsD,iBAAiB,SAAUoB,GAAc,IAEhD1E,KAAK8E,oBAAsB9C,EAC3BiB,EAAajD,MAEjB,CAEA,SAAS0E,IACP,IAjN6Ba,EAfChD,EAgO1BiD,EAASxF,KAGbyE,EAAczE,MACVA,KAAKyF,iBArNoBF,EAsNNvF,KAAKyF,eArNvB1E,IACHA,GACEW,OAAOgE,sBACPhE,OAAOiE,4BACPjE,OAAOkE,yBACP,SAAUL,GACRM,aAAaN,EACd,GACD7C,KAAKhB,SAGTX,EAAIwE,IA4MJvF,KAAKyF,gBAvOyBlD,EAuOc,WAC1C,IAvCqBpB,EACnB+B,EACAyB,EACAC,EACA/B,EACAC,EAkCEgD,GAtCF5C,GADmB/B,EAuCQqE,GAtChBtB,gBACXS,EAAQzB,EAAIyB,MACZC,EAAS1B,EAAI0B,OACb/B,EAAc1B,EAAK0B,YACnBC,EAAe3B,EAAK2B,aACpBD,IAAgB8B,GAAS7B,IAAiB8B,EACrC,CACLD,MAAO9B,EACP+B,OAAQ9B,GAGL,MA4BDgD,IACFN,EAAOtB,gBAAkB4B,EACzB7C,EAAauC,GAEnB,EA5OO1E,IACHA,GACEY,OAAOqE,uBACPrE,OAAOsE,6BACPtE,OAAOuE,0BACP,SAAU1D,GACR,OAAO2D,WAAW3D,EAAU,GAC7B,GACDG,KAAKhB,SAEFZ,EAAIyB,GAmOb,CAEA,SAASU,EAAc9B,GAChBA,GAASA,EAAKwB,sBAGnBxB,EAAKwB,qBAAqBpC,SAAQ,SAAUgC,GAC1CA,EAASzC,KAAKqB,EAAMA,EACxB,GACA,CA0CA,SAASsD,EAAetD,GACtB,IAAI+B,EAAM/B,EAAKqD,oBACXH,EAASnB,EAAImB,OACbC,EAAcpB,EAAIoB,YAClBC,EAAWrB,EAAIqB,SAGf4B,EAAM5B,EAAS6B,YACfC,EAAM9B,EAAS+B,aACfC,EAAMlC,EAAOxB,YACb2D,EAAMnC,EAAOvB,aACb2D,EAAMpC,EAAO+B,YACbM,EAAMrC,EAAOiC,aAGjB/B,EAASoC,WAAaR,EACtB5B,EAASqC,UAAYP,EACrB/B,EAAYjC,MAAMsC,MAAQ4B,EAAM,EAAI,KACpCjC,EAAYjC,MAAMuC,OAAS4B,EAAM,EAAI,KACrCnC,EAAOsC,WAAaF,EACpBpC,EAAOuC,UAAYF,CACrB,UCvSgBG,EACdzG,EACA0G,EACAC,GAEA,IAAIC,EAAwC,KAE5CC,QAAM,CAACF,EAAM3G,EAAO0G,IAAa,SAACI,EAA2BC,EAAGC,GAA7B,IAAAL,OAAM3G,EAAK8G,EAAA,GAAEJ,EAAUI,EAAA,GACxD,GAAIH,GAAQ3G,GAAS0G,EAAY,CAC/B,IAAMO,GAAmC,IAAfP,EAAsB,CAAA,EAAKA,EAC7CQ,EAAmCD,EAAfE,SAAVC,OAAI,IAAAF,EAAG,IAAGA,EAAEG,EAAaJ,WAErC9E,EAAW,WACfnC,EAAMsH,SACND,SAAAA,GACF,EAEAT,EAAiBQ,EAAOD,EAAQA,SAAChF,EAAUiF,GAAQjF,EACnDD,EAAYyE,EAAMC,EACnB,CAEDI,GAAQ,WACFL,GAAQC,GDoHlB,SAAyB7F,EAAMoB,GAC7B,IAAIoF,EAAYxG,EAAKwB,qBACrB,GAAKgF,EAAL,CASA,GALIpF,GACFoF,EAAUC,OAAOD,EAAUE,QAAQtF,GAAW,IAI3CoF,EAAUjI,SAAW6C,EAAU,CAElC,GAAIpB,EAAK2G,aAAe3G,EAAK4G,oBAG3B,OAFA5G,EAAK2G,YAAY,WAAY3G,EAAKoC,uCAClCnC,SAAS2G,oBAAoB,qBAAsB5G,EAAKqB,6BAItDrB,EAAKgC,qBACPhC,EAAKgC,oBAAoB6E,UAAU7G,GACnCA,EAAKgC,oBAAoB8E,aACzB9G,EAAKgC,oBAAsB,OAEvBhC,EAAKkE,+BACPlE,EAAKkE,6BAA6B4C,aAClC9G,EAAKkE,6BAA+B,MAEtClE,EAAK4G,oBAAoB,SAAUrD,GACnCvD,EAAK+G,YAAY/G,EAAKqD,oBAAoBL,UAC1ChD,EAAKqD,oBAAsB,MAE7BrD,EAAKwB,qBAAuB,IAC7B,KAEMP,GAASC,GACdA,EAAMH,WAAWgG,YAAY7F,EAhC9B,CAkCH,CCzJQ8F,CAAepB,EAAMC,EAEzB,GACF,GACF,CAEO,IAAMoB,EAAkB,CAC7BtB,WAAY,CAACuB,QAASlJ,SCpClBmJ,EAAO,YACAC,EAAO,SAACjH,GAAyB,OAAAgH,EAAKE,KAAKlH,IAaxC,SAAAmH,EACdC,EACAC,GAEA,IAAMhI,EAAQiI,EAAAA,MAAMF,GAAaG,EAAAA,MAAMH,GAAaA,EAEpD,OAAI/H,GAA0B,iBAAVA,GAAsB,UAAWA,EAC5CA,EAAMA,OAASgI,EAGjBhI,GAASgI,CAClB,CCxBO,IAAMG,EACX,mBA6BK,IAAMC,EAAe,CAC1BC,QAASX,QACTY,eAAgB9J,QCzCL+J,EAAW,gBC4CpBC,QACFA,EAAAA,KAAKC,OAAOC,gBAAgB/D,KAAK4D,GAG5B,IAAMI,EAAY,UACZC,EACX,gBACWC,EACX,kBAGIC,EAAkB,mBAExBC,EAAeC,kBAAgB,CAC7BnJ,KAAM,UACNU,WACE0I,OAAQzK,OACR0K,MAAO,CACLC,KAAM,CAAC3K,OAAQ4K,SAEjBC,YAAa7K,OACb8K,cAAe9K,OACf+K,MAAOH,OACPI,aAAc9B,SACXD,GACAW,GAELqB,MAAO,CAAsB,EAC7BC,cAAc,EACdC,MAAA,SAAMpJ,EAAOgG,GAAE,IAAAqD,EAAKrD,EAAAqD,MACZxD,EAAOyD,EAAAA,aACPC,EAAQD,EAAAA,aACRpK,EAAQoK,EAAAA,aACRE,EAAeF,EAAAA,aACfG,EAAeC,EAAAA,OAAOtB,EAAW,MACjCuB,EAAqBD,EAAAA,OAAOrB,EAAkB,MAC9CuB,EAAuBF,EAAAA,OAAOpB,EAAoB,MAElDlC,EAAwDyD,EAAAA,OAAO7J,GAA7D4F,EAAUQ,EAAAR,WAAEqD,iBAAcnB,YAASC,mBAErC+B,EAAaC,EAAAA,UACjB,WAAM,OAAAP,EAAa/J,OAASO,EAAM0I,QAAU,IAAtC,IAEFsB,EAAYD,EAAAA,UAChB,WAAM,OAAA/J,EAAM2I,OAASpB,EAAekC,EAAc,GAAG,IAEjDQ,EAAkBF,EAAAA,UACtB,WAAM,OAAA/J,EAAM8I,aAAevB,EAAeoC,EAAoB,GAAG,IAE7DO,EAAoBH,EAAAA,UACxB,WAAM,OAAA/J,EAAM+I,eAAiBxB,EAAeqC,EAAsB,GAAG,IAEjEO,EAAgBJ,EAAAA,UAAS,WAAM,OHrFnC,SAAiBV,GACrB,IAAMe,EAAgB,CAAA,EACtB,IAAK,IAAMhK,KAAOiJ,EACXhC,EAAKjH,KACRgK,EAAOhK,GAAOiJ,EAAMjJ,IAIxB,OAAOgK,CACT,CG4EyCC,CAAOhB,EAAP,IAC/BiB,EAA2C,CAAA,EAG3C7D,EAAY8D,EAAAA,qBAAqBC,MAAMC,WACvCC,EAAqC,CAAA,EAqD3C,SAASC,EAAKjC,GACZ,GAAKa,EAAM9J,MAAX,CAIA,IAAMmL,EAAY1L,EAAMO,MAAQoL,EAASF,KACvCpB,EAAM9J,MACNuK,EAAUvK,MACVwK,EAAgBxK,OAGdO,EAAMgJ,QACR4B,EAAS5B,MAAQhJ,EAAMgJ,OAGzB/K,OAAOkC,KAAKuK,GAAerL,SAAQ,SAAAe,GACjC,IAAI0K,EAAUJ,EAActK,GAE5B,GAAK0K,EAAL,CAIA,IAAIC,EAAQ3K,EAAI4K,cACQ,MAApBD,EAAME,OAAO,KACfF,EAAQA,EAAMG,UAAU,GACxBJ,EAAQK,UAAW,GAGrB,IAAIC,EAAsBR,EAM1B,GAL6B,IAAzBG,EAAMpE,QAAQ,SAChByE,EAASR,EAASS,QAClBN,EAAQA,EAAMG,UAAU,IAGtBJ,EAAQK,SAAU,QACbL,EAAQK,SAEf,IAAMG,EAAMR,EAEZA,EAAU,eAAC,IAAcvL,EAAA,GAAAC,EAAA,EAAdA,EAAcjB,UAAAC,OAAdgB,IAAAD,EAAcC,GAAAjB,UAAAiB,GACvB8L,EAAGzM,WAAA,EAAIU,GACP6L,EAAOG,IAAIR,EAAOD,EACpB,CACD,CAKDM,EAAOI,GAAGT,EAAOD,EA5BhB,CA6BH,IAeIlF,EAAWnG,MAGbgM,EAAAA,UAAS,WAfLb,IAAaA,EAASc,cACxBd,EAASpE,SAgBTmF,GACF,IAEAA,GArED,CAsDD,SAASA,IACP,IAAMC,EAAMlD,GAAUoB,EAAWrK,MAC7BmM,GACFhB,EAASiB,UAAUD,EAAK1B,EAAkBzK,MAE7C,CAYF,CAcD,SAASyG,IACHhH,EAAMO,QACRP,EAAMO,MAAMqM,UACZ5M,EAAMO,WAAQsM,EAEjB,CAhJItF,EA0CHxI,OAAOkC,KAAKsG,GAAWpH,SAAQ,SAAAe,GACzBmI,EAAgBjB,KAAKlH,GACvBkK,EAAgBlK,EAAI4L,QAAQzD,EAAiB,OAAS9B,EAAUrG,GAEhEsK,EAActK,GAAOqG,EAAUrG,EAEnC,IAzCAnC,OAAOkC,KAAKkJ,GACT4C,QAAO,SAAA7L,GAAO,OAAAiH,EAAKjH,MACnBf,SAAQ,SAAAe,GAGP,IAAI2K,EAAQ3K,EAAI6K,OAAO,GAAGD,cAAgB5K,EAAI8L,MAAM,GAGpD,GAAiC,IAA7BnB,EAAMpE,QAAQ,WAYwB,SAAtCoE,EAAMG,UAAUH,EAAMvM,OAAS,KACjCuM,EAAQ,IAAAoB,OAAIpB,EAAMG,UAAU,EAAGH,EAAMvM,OAAS,KAGhDkM,EAAcK,GAAS1B,EAAMjJ,OAhB7B,CAEE,IAAMgM,EAAY,KAAKD,OAAApB,EAAME,OAAO,GAAGoB,eAAaF,OAAGpB,EAAMmB,MAC3D,IAGF5B,EAAgB8B,GAAa/C,EAAMjJ,EAEpC,CASH,IAkHJ,IAAIkM,EAAqC,KACzCvG,QACEkD,GACA,SAAAA,GAC+B,mBAAlBqD,IACTA,IACAA,EAAgB,MAGbrD,IACHqD,EAAgBvG,EAAAA,OACd,WAAM,OAAA/F,EAAM0I,MAAM,IAClB,SAACA,EAAQ6D,GACF7D,IAGAxJ,EAAMO,MAGTP,EAAMO,MAAMoM,UAAUnD,EAGpB1K,EAAA,CAAAwO,SAAU9D,IAAW6D,GAClBrC,EAAkBzK,QANvBkL,IASJ,GACA,CAAE8B,MAAM,IAGd,GACA,CACEC,WAAW,IAIf3G,EAAAA,MACE,CAACiE,EAAWC,IACZ,WACE/D,IACAyE,GACF,GACA,CACE8B,MAAM,IAIVE,EAAAA,aAAY,WACN3M,EAAMgJ,OAAS9J,EAAMO,QACvBP,EAAMO,MAAMuJ,MAAQhJ,EAAMgJ,MAE9B,IAEA,IAAM4D,EAAY3N,EAAaC,GAsB/B,gBFrTFA,EACA4I,EACAC,GAEA,IAAM8E,EAAwBnD,EAAAA,OAAO9B,EAAqB,CAAE,GACtDkF,EAAqB/C,YAAS,WAAM,OACrC/L,EAAAA,EAAA,CAAA,EAAAuJ,EAAesF,EAAuB,CAAE,IACxC9E,aAAA,EAAAA,EAAgBtI,MACnB,IAEFkN,EAAAA,aAAY,WACV,IAAM/B,EAAW1L,EAAMO,MAClBmL,IAID9C,EAAQrI,MACVmL,EAASmC,YAAYD,EAAmBrN,OAExCmL,EAASoC,cAEb,GACF,CE2QIC,CAAW/N,EAAO4I,EAASC,GAE3BpC,EAAczG,EAAO0G,EAAY2D,GAEjC2D,EAAAA,WAAU,WACRvC,GACF,IAEAwC,EAAAA,iBAAgB,WAQZjH,GAEJ,IAEAlI,EAAA,CACEkB,MAAKA,EACL2G,KAAIA,EACJ0D,MAAKA,EACLsC,UAlGF,SAAmBnD,EAAgBK,GAC7B/I,EAAMiJ,eACRO,EAAa/J,MAAQiJ,GAGlBxJ,EAAMO,MAGTP,EAAMO,MAAMoM,UAAUnD,EAAQK,GAAiB,CAAE,GAFjD4B,EAAKjC,EAIR,EAyFCyB,cAAaA,EACbG,gBAAeA,GACZsC,EAEN,EACDQ,OAAA,WAGE,IAAM/D,EACJpB,EAAIA,KACA,CAAEoB,MAAOvK,KAAKqL,cAAeqB,GAAI1M,KAAKwL,iBACvCtM,EAAAA,EAAA,CAAA,EAAMc,KAAKqL,eAAkBrL,KAAKwL,iBAIvC,OAFAjB,EAAMrH,IAAM,OACZqH,EAAW,MAAGA,EAAW,MAAG,CAAC,WAAW8C,OAAO9C,EAAW,OAAI,UACvDgE,EAACA,EAACrF,EAAUqB,EAAO,CACxBgE,EAACA,EAAC,MAAO,CAAErL,IAAK,QAASsL,MAAO,uBAEnC,iICzVEtP,EAAAA,EAAA,CAAA,EAAAwK,GACA+E"}