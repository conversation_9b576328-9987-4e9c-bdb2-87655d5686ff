{"version": 3, "file": "index.umd.js", "sources": ["../../node_modules/.pnpm/tslib@2.6.2/node_modules/tslib/tslib.es6.js", "../../src/composables/api.ts", "../../node_modules/.pnpm/resize-detector@0.3.0/node_modules/resize-detector/esm/index.js", "../../src/composables/autoresize.ts", "../../src/utils.ts", "../../src/composables/loading.ts", "../../src/wc.ts", "../../src/ECharts.ts", "../../src/global.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Ref } from \"vue-demi\";\nimport { EChartsType } from \"../types\";\n\nconst METHOD_NAMES = [\n  \"getWidth\",\n  \"getHeight\",\n  \"getDom\",\n  \"getOption\",\n  \"resize\",\n  \"dispatchAction\",\n  \"convertToPixel\",\n  \"convertFromPixel\",\n  \"containPixel\",\n  \"getDataURL\",\n  \"getConnectedDataURL\",\n  \"appendData\",\n  \"clear\",\n  \"isDisposed\",\n  \"dispose\"\n] as const;\n\ntype MethodName = (typeof METHOD_NAMES)[number];\n\ntype PublicMethods = Pick<EChartsType, MethodName>;\n\nexport function usePublicAPI(\n  chart: Ref<EChartsType | undefined>\n): PublicMethods {\n  function makePublicMethod<T extends MethodName>(\n    name: T\n  ): (...args: Parameters<EChartsType[T]>) => ReturnType<EChartsType[T]> {\n    return (...args) => {\n      if (!chart.value) {\n        throw new Error(\"ECharts is not initialized yet.\");\n      }\n      return (chart.value[name] as any).apply(chart.value, args);\n    };\n  }\n\n  function makePublicMethods(): PublicMethods {\n    const methods = Object.create(null);\n    METHOD_NAMES.forEach(name => {\n      methods[name] = makePublicMethod(name);\n    });\n\n    return methods as PublicMethods;\n  }\n\n  return makePublicMethods();\n}\n", "var raf = null;\nfunction requestAnimationFrame (callback) {\n  if (!raf) {\n    raf = (\n      window.requestAnimationFrame ||\n      window.webkitRequestAnimationFrame ||\n      window.mozRequestAnimationFrame ||\n      function (callback) {\n        return setTimeout(callback, 16)\n      }\n    ).bind(window);\n  }\n  return raf(callback)\n}\n\nvar caf = null;\nfunction cancelAnimationFrame (id) {\n  if (!caf) {\n    caf = (\n      window.cancelAnimationFrame ||\n      window.webkitCancelAnimationFrame ||\n      window.mozCancelAnimationFrame ||\n      function (id) {\n        clearTimeout(id);\n      }\n    ).bind(window);\n  }\n\n  caf(id);\n}\n\nfunction createStyles (styleText) {\n  var style = document.createElement('style');\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = styleText;\n  } else {\n    style.appendChild(document.createTextNode(styleText));\n  }\n  (document.querySelector('head') || document.body).appendChild(style);\n  return style\n}\n\nfunction createElement (tagName, props) {\n  if ( props === void 0 ) props = {};\n\n  var elem = document.createElement(tagName);\n  Object.keys(props).forEach(function (key) {\n    elem[key] = props[key];\n  });\n  return elem\n}\n\nfunction getComputedStyle (elem, prop, pseudo) {\n  // for older versions of Firefox, `getComputedStyle` required\n  // the second argument and may return `null` for some elements\n  // when `display: none`\n  var computedStyle = window.getComputedStyle(elem, pseudo || null) || {\n    display: 'none'\n  };\n\n  return computedStyle[prop]\n}\n\nfunction getRenderInfo (elem) {\n  if (!document.documentElement.contains(elem)) {\n    return {\n      detached: true,\n      rendered: false\n    }\n  }\n\n  var current = elem;\n  while (current !== document) {\n    if (getComputedStyle(current, 'display') === 'none') {\n      return {\n        detached: false,\n        rendered: false\n      }\n    }\n    current = current.parentNode;\n  }\n\n  return {\n    detached: false,\n    rendered: true\n  }\n}\n\nvar css_248z = \".resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:\\\"\\\";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}\";\n\nvar total = 0;\nvar style = null;\n\nfunction addListener (elem, callback) {\n  if (!elem.__resize_mutation_handler__) {\n    elem.__resize_mutation_handler__ = handleMutation.bind(elem);\n  }\n\n  var listeners = elem.__resize_listeners__;\n\n  if (!listeners) {\n    elem.__resize_listeners__ = [];\n    if (window.ResizeObserver) {\n      var offsetWidth = elem.offsetWidth;\n      var offsetHeight = elem.offsetHeight;\n      var ro = new ResizeObserver(function () {\n        if (!elem.__resize_observer_triggered__) {\n          elem.__resize_observer_triggered__ = true;\n          if (elem.offsetWidth === offsetWidth && elem.offsetHeight === offsetHeight) {\n            return\n          }\n        }\n        runCallbacks(elem);\n      });\n\n      // initially display none won't trigger ResizeObserver callback\n      var ref = getRenderInfo(elem);\n      var detached = ref.detached;\n      var rendered = ref.rendered;\n      elem.__resize_observer_triggered__ = detached === false && rendered === false;\n      elem.__resize_observer__ = ro;\n      ro.observe(elem);\n    } else if (elem.attachEvent && elem.addEventListener) {\n      // targeting IE9/10\n      elem.__resize_legacy_resize_handler__ = function handleLegacyResize () {\n        runCallbacks(elem);\n      };\n      elem.attachEvent('onresize', elem.__resize_legacy_resize_handler__);\n      document.addEventListener('DOMSubtreeModified', elem.__resize_mutation_handler__);\n    } else {\n      if (!total) {\n        style = createStyles(css_248z);\n      }\n      initTriggers(elem);\n\n      elem.__resize_rendered__ = getRenderInfo(elem).rendered;\n      if (window.MutationObserver) {\n        var mo = new MutationObserver(elem.__resize_mutation_handler__);\n        mo.observe(document, {\n          attributes: true,\n          childList: true,\n          characterData: true,\n          subtree: true\n        });\n        elem.__resize_mutation_observer__ = mo;\n      }\n    }\n  }\n\n  elem.__resize_listeners__.push(callback);\n  total++;\n}\n\nfunction removeListener (elem, callback) {\n  var listeners = elem.__resize_listeners__;\n  if (!listeners) {\n    return\n  }\n\n  if (callback) {\n    listeners.splice(listeners.indexOf(callback), 1);\n  }\n\n  // no listeners exist, or removing all listeners\n  if (!listeners.length || !callback) {\n    // targeting IE9/10\n    if (elem.detachEvent && elem.removeEventListener) {\n      elem.detachEvent('onresize', elem.__resize_legacy_resize_handler__);\n      document.removeEventListener('DOMSubtreeModified', elem.__resize_mutation_handler__);\n      return\n    }\n\n    if (elem.__resize_observer__) {\n      elem.__resize_observer__.unobserve(elem);\n      elem.__resize_observer__.disconnect();\n      elem.__resize_observer__ = null;\n    } else {\n      if (elem.__resize_mutation_observer__) {\n        elem.__resize_mutation_observer__.disconnect();\n        elem.__resize_mutation_observer__ = null;\n      }\n      elem.removeEventListener('scroll', handleScroll);\n      elem.removeChild(elem.__resize_triggers__.triggers);\n      elem.__resize_triggers__ = null;\n    }\n    elem.__resize_listeners__ = null;\n  }\n\n  if (!--total && style) {\n    style.parentNode.removeChild(style);\n  }\n}\n\nfunction getUpdatedSize (elem) {\n  var ref = elem.__resize_last__;\n  var width = ref.width;\n  var height = ref.height;\n  var offsetWidth = elem.offsetWidth;\n  var offsetHeight = elem.offsetHeight;\n  if (offsetWidth !== width || offsetHeight !== height) {\n    return {\n      width: offsetWidth,\n      height: offsetHeight\n    }\n  }\n  return null\n}\n\nfunction handleMutation () {\n  // `this` denotes the scrolling element\n  var ref = getRenderInfo(this);\n  var rendered = ref.rendered;\n  var detached = ref.detached;\n  if (rendered !== this.__resize_rendered__) {\n    if (!detached && this.__resize_triggers__) {\n      resetTriggers(this);\n      this.addEventListener('scroll', handleScroll, true);\n    }\n    this.__resize_rendered__ = rendered;\n    runCallbacks(this);\n  }\n}\n\nfunction handleScroll () {\n  var this$1 = this;\n\n  // `this` denotes the scrolling element\n  resetTriggers(this);\n  if (this.__resize_raf__) {\n    cancelAnimationFrame(this.__resize_raf__);\n  }\n  this.__resize_raf__ = requestAnimationFrame(function () {\n    var updated = getUpdatedSize(this$1);\n    if (updated) {\n      this$1.__resize_last__ = updated;\n      runCallbacks(this$1);\n    }\n  });\n}\n\nfunction runCallbacks (elem) {\n  if (!elem || !elem.__resize_listeners__) {\n    return\n  }\n  elem.__resize_listeners__.forEach(function (callback) {\n    callback.call(elem, elem);\n  });\n}\n\nfunction initTriggers (elem) {\n  var position = getComputedStyle(elem, 'position');\n  if (!position || position === 'static') {\n    elem.style.position = 'relative';\n  }\n\n  elem.__resize_old_position__ = position;\n  elem.__resize_last__ = {};\n\n  var triggers = createElement('div', {\n    className: 'resize-triggers'\n  });\n  var expand = createElement('div', {\n    className: 'resize-expand-trigger'\n  });\n  var expandChild = createElement('div');\n  var contract = createElement('div', {\n    className: 'resize-contract-trigger'\n  });\n  expand.appendChild(expandChild);\n  triggers.appendChild(expand);\n  triggers.appendChild(contract);\n  elem.appendChild(triggers);\n\n  elem.__resize_triggers__ = {\n    triggers: triggers,\n    expand: expand,\n    expandChild: expandChild,\n    contract: contract\n  };\n\n  resetTriggers(elem);\n  elem.addEventListener('scroll', handleScroll, true);\n\n  elem.__resize_last__ = {\n    width: elem.offsetWidth,\n    height: elem.offsetHeight\n  };\n}\n\nfunction resetTriggers (elem) {\n  var ref = elem.__resize_triggers__;\n  var expand = ref.expand;\n  var expandChild = ref.expandChild;\n  var contract = ref.contract;\n\n  // batch read\n  var csw = contract.scrollWidth;\n  var csh = contract.scrollHeight;\n  var eow = expand.offsetWidth;\n  var eoh = expand.offsetHeight;\n  var esw = expand.scrollWidth;\n  var esh = expand.scrollHeight;\n\n  // batch write\n  contract.scrollLeft = csw;\n  contract.scrollTop = csh;\n  expandChild.style.width = eow + 1 + 'px';\n  expandChild.style.height = eoh + 1 + 'px';\n  expand.scrollLeft = esw;\n  expand.scrollTop = esh;\n}\n\nexport { addListener, removeListener };\n", "import { watch, type Ref, type PropType } from \"vue-demi\";\nimport { throttle } from \"echarts/core\";\nimport {\n  addListener,\n  removeListener,\n  type ResizeCallback\n} from \"resize-detector\";\nimport { type EChartsType } from \"../types\";\n\ntype AutoresizeProp =\n  | boolean\n  | {\n      throttle?: number;\n      onResize?: () => void;\n    };\n\nexport function useAutoresize(\n  chart: Ref<EChartsType | undefined>,\n  autoresize: Ref<AutoresizeProp | undefined>,\n  root: Ref<HTMLElement | undefined>\n): void {\n  let resizeListener: ResizeCallback | null = null;\n\n  watch([root, chart, autoresize], ([root, chart, autoresize], _, cleanup) => {\n    if (root && chart && autoresize) {\n      const autoresizeOptions = autoresize === true ? {} : autoresize;\n      const { throttle: wait = 100, onResize } = autoresizeOptions;\n\n      const callback = () => {\n        chart.resize();\n        onResize?.();\n      };\n\n      resizeListener = wait ? throttle(callback, wait) : callback;\n      addListener(root, resizeListener);\n    }\n\n    cleanup(() => {\n      if (root && resizeListener) {\n        removeListener(root, resizeListener);\n      }\n    });\n  });\n}\n\nexport const autoresizeProps = {\n  autoresize: [Boolean, Object] as PropType<AutoresizeProp>\n};\n", "import { unref, isRef } from \"vue-demi\";\nimport type { Injection } from \"./types\";\n\ntype Attrs = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key: string]: any;\n};\n\n// Copied from\n// https://github.com/vuejs/vue-next/blob/5a7a1b8293822219283d6e267496bec02234b0bc/packages/shared/src/index.ts#L40-L41\nconst onRE = /^on[^a-z]/;\nexport const isOn = (key: string): boolean => onRE.test(key);\n\nexport function omitOn(attrs: Attrs): Attrs {\n  const result: Attrs = {};\n  for (const key in attrs) {\n    if (!isOn(key)) {\n      result[key] = attrs[key];\n    }\n  }\n\n  return result;\n}\n\nexport function unwrapInjected<T, V>(\n  injection: Injection<T>,\n  defaultValue: V\n): T | V {\n  const value = isRef(injection) ? unref(injection) : injection;\n\n  if (value && typeof value === \"object\" && \"value\" in value) {\n    return value.value || defaultValue;\n  }\n\n  return value || defaultValue;\n}\n", "import { unwrapInjected } from \"../utils\";\nimport {\n  inject,\n  computed,\n  watchEffect,\n  type Ref,\n  type InjectionKey,\n  type PropType\n} from \"vue-demi\";\nimport type { EChartsType, LoadingOptions } from \"../types\";\n\nexport const LOADING_OPTIONS_KEY =\n  \"ecLoadingOptions\" as unknown as InjectionKey<\n    LoadingOptions | Ref<LoadingOptions>\n  >;\n\nexport function useLoading(\n  chart: Ref<EChartsType | undefined>,\n  loading: Ref<boolean>,\n  loadingOptions: Ref<LoadingOptions | undefined>\n): void {\n  const defaultLoadingOptions = inject(LOADING_OPTIONS_KEY, {});\n  const realLoadingOptions = computed(() => ({\n    ...unwrapInjected(defaultLoadingOptions, {}),\n    ...loadingOptions?.value\n  }));\n\n  watchEffect(() => {\n    const instance = chart.value;\n    if (!instance) {\n      return;\n    }\n\n    if (loading.value) {\n      instance.showLoading(realLoadingOptions.value);\n    } else {\n      instance.hideLoading();\n    }\n  });\n}\n\nexport const loadingProps = {\n  loading: Boolean,\n  loadingOptions: Object as PropType<LoadingOptions>\n};\n", "let registered: boolean | null = null;\n\nexport const TAG_NAME = \"x-vue-echarts\";\n\nexport interface EChartsElement extends HTMLElement {\n  __dispose: (() => void) | null;\n}\n\nexport function register(): boolean {\n  if (registered != null) {\n    return registered;\n  }\n\n  if (\n    typeof HTMLElement === \"undefined\" ||\n    typeof customElements === \"undefined\"\n  ) {\n    return (registered = false);\n  }\n\n  try {\n    // Class definitions cannot be transpiled to ES5\n    // so we are doing a little trick here to ensure\n    // we are using native classes. As we use this as\n    // a progressive enhancement, it will be fine even\n    // if the browser doesn't support native classes.\n    const reg = new Function(\n      \"tag\",\n      `class EChartsElement extends HTMLElement {\n  __dispose = null;\n\n  disconnectedCallback() {\n    if (this.__dispose) {\n      this.__dispose();\n      this.__dispose = null;\n    }\n  }\n}\n\nif (customElements.get(tag) == null) {\n  customElements.define(tag, EChartsElement);\n}\n`\n    );\n    reg(TAG_NAME);\n  } catch (e) {\n    return (registered = false);\n  }\n\n  return (registered = true);\n}\n", "/* eslint-disable vue/multi-word-component-names */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n  defineComponent,\n  shallowRef,\n  toRefs,\n  watch,\n  computed,\n  inject,\n  onMounted,\n  onBeforeUnmount,\n  h,\n  nextTick,\n  watchEffect,\n  getCurrentInstance,\n  Vue2,\n  type PropType,\n  type InjectionKey\n} from \"vue-demi\";\nimport { init as initChart } from \"echarts/core\";\nimport type {\n  EChartsType,\n  EventTarget,\n  Option,\n  Theme,\n  ThemeInjection,\n  InitOptions,\n  InitOptionsInjection,\n  UpdateOptions,\n  UpdateOptionsInjection,\n  Emits\n} from \"./types\";\nimport {\n  usePublicAPI,\n  useAutoresize,\n  autoresizeProps,\n  useLoading,\n  loadingProps\n} from \"./composables\";\nimport { isOn, omitOn, unwrapInjected } from \"./utils\";\nimport { register, TAG_NAME, type EChartsElement } from \"./wc\";\nimport \"./style.css\";\n\nconst __CSP__ = false;\nconst wcRegistered = __CSP__ ? false : register();\n\nif (Vue2) {\n  Vue2.config.ignoredElements.push(TAG_NAME);\n}\n\nexport const THEME_KEY = \"ecTheme\" as unknown as InjectionKey<ThemeInjection>;\nexport const INIT_OPTIONS_KEY =\n  \"ecInitOptions\" as unknown as InjectionKey<InitOptionsInjection>;\nexport const UPDATE_OPTIONS_KEY =\n  \"ecUpdateOptions\" as unknown as InjectionKey<UpdateOptionsInjection>;\nexport { LOADING_OPTIONS_KEY } from \"./composables\";\n\nconst NATIVE_EVENT_RE = /(^&?~?!?)native:/;\n\nexport default defineComponent({\n  name: \"echarts\",\n  props: {\n    option: Object as PropType<Option>,\n    theme: {\n      type: [Object, String] as PropType<Theme>\n    },\n    initOptions: Object as PropType<InitOptions>,\n    updateOptions: Object as PropType<UpdateOptions>,\n    group: String,\n    manualUpdate: Boolean,\n    ...autoresizeProps,\n    ...loadingProps\n  },\n  emits: {} as unknown as Emits,\n  inheritAttrs: false,\n  setup(props, { attrs }) {\n    const root = shallowRef<EChartsElement>();\n    const inner = shallowRef<HTMLElement>();\n    const chart = shallowRef<EChartsType>();\n    const manualOption = shallowRef<Option>();\n    const defaultTheme = inject(THEME_KEY, null);\n    const defaultInitOptions = inject(INIT_OPTIONS_KEY, null);\n    const defaultUpdateOptions = inject(UPDATE_OPTIONS_KEY, null);\n\n    const { autoresize, manualUpdate, loading, loadingOptions } = toRefs(props);\n\n    const realOption = computed(\n      () => manualOption.value || props.option || null\n    );\n    const realTheme = computed(\n      () => props.theme || unwrapInjected(defaultTheme, {})\n    );\n    const realInitOptions = computed(\n      () => props.initOptions || unwrapInjected(defaultInitOptions, {})\n    );\n    const realUpdateOptions = computed(\n      () => props.updateOptions || unwrapInjected(defaultUpdateOptions, {})\n    );\n    const nonEventAttrs = computed(() => omitOn(attrs));\n    const nativeListeners: Record<string, unknown> = {};\n\n    // @ts-expect-error listeners for Vue 2 compatibility\n    const listeners = getCurrentInstance().proxy.$listeners;\n    const realListeners: Record<string, any> = {};\n\n    if (!listeners) {\n      // This is for Vue 3.\n      // We are converting all `on<Event>` props to event listeners compatible with Vue 2\n      // and collect them into `realListeners` so that we can bind them to the chart instance\n      // later in the same way.\n      // For `onNative:<event>` props, we just strip the `Native:` part and collect them into\n      // `nativeListeners` so that we can bind them to the root element directly.\n      Object.keys(attrs)\n        .filter(key => isOn(key))\n        .forEach(key => {\n          // onClick    -> c + lick\n          // onZr:click -> z + r:click\n          let event = key.charAt(2).toLowerCase() + key.slice(3);\n\n          // Collect native DOM events\n          if (event.indexOf(\"native:\") === 0) {\n            // native:click -> onClick\n            const nativeKey = `on${event.charAt(7).toUpperCase()}${event.slice(\n              8\n            )}`;\n\n            nativeListeners[nativeKey] = attrs[key];\n            return;\n          }\n\n          // clickOnce    -> ~click\n          // zr:clickOnce -> ~zr:click\n          if (event.substring(event.length - 4) === \"Once\") {\n            event = `~${event.substring(0, event.length - 4)}`;\n          }\n\n          realListeners[event] = attrs[key];\n        });\n    } else {\n      // This is for Vue 2.\n      // We just need to distinguish normal events and `native:<event>` events and\n      // collect them into `realListeners` and `nativeListeners` respectively.\n      // For `native:<event>` events, we just strip the `native:` part and collect them\n      // into `nativeListeners` so that we can bind them to the root element directly.\n      // native:click   -> click\n      // ~native:click  -> ~click\n      // &~!native:click -> &~!click\n      Object.keys(listeners).forEach(key => {\n        if (NATIVE_EVENT_RE.test(key)) {\n          nativeListeners[key.replace(NATIVE_EVENT_RE, \"$1\")] = listeners[key];\n        } else {\n          realListeners[key] = listeners[key];\n        }\n      });\n    }\n\n    function init(option?: Option) {\n      if (!inner.value) {\n        return;\n      }\n\n      const instance = (chart.value = initChart(\n        inner.value,\n        realTheme.value,\n        realInitOptions.value\n      ));\n\n      if (props.group) {\n        instance.group = props.group;\n      }\n\n      Object.keys(realListeners).forEach(key => {\n        let handler = realListeners[key];\n\n        if (!handler) {\n          return;\n        }\n\n        let event = key.toLowerCase();\n        if (event.charAt(0) === \"~\") {\n          event = event.substring(1);\n          handler.__once__ = true;\n        }\n\n        let target: EventTarget = instance;\n        if (event.indexOf(\"zr:\") === 0) {\n          target = instance.getZr();\n          event = event.substring(3);\n        }\n\n        if (handler.__once__) {\n          delete handler.__once__;\n\n          const raw = handler;\n\n          handler = (...args: any[]) => {\n            raw(...args);\n            target.off(event, handler);\n          };\n        }\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore EChartsType[\"on\"] is not compatible with ZRenderType[\"on\"]\n        // but it's okay here\n        target.on(event, handler);\n      });\n\n      function resize() {\n        if (instance && !instance.isDisposed()) {\n          instance.resize();\n        }\n      }\n\n      function commit() {\n        const opt = option || realOption.value;\n        if (opt) {\n          instance.setOption(opt, realUpdateOptions.value);\n        }\n      }\n\n      if (autoresize.value) {\n        // Try to make chart fit to container in case container size\n        // is changed synchronously or in already queued microtasks\n        nextTick(() => {\n          resize();\n          commit();\n        });\n      } else {\n        commit();\n      }\n    }\n\n    function setOption(option: Option, updateOptions?: UpdateOptions) {\n      if (props.manualUpdate) {\n        manualOption.value = option;\n      }\n\n      if (!chart.value) {\n        init(option);\n      } else {\n        chart.value.setOption(option, updateOptions || {});\n      }\n    }\n\n    function cleanup() {\n      if (chart.value) {\n        chart.value.dispose();\n        chart.value = undefined;\n      }\n    }\n\n    let unwatchOption: (() => void) | null = null;\n    watch(\n      manualUpdate,\n      manualUpdate => {\n        if (typeof unwatchOption === \"function\") {\n          unwatchOption();\n          unwatchOption = null;\n        }\n\n        if (!manualUpdate) {\n          unwatchOption = watch(\n            () => props.option,\n            (option, oldOption) => {\n              if (!option) {\n                return;\n              }\n              if (!chart.value) {\n                init();\n              } else {\n                chart.value.setOption(option, {\n                  // mutating `option` will lead to `notMerge: false` and\n                  // replacing it with new reference will lead to `notMerge: true`\n                  notMerge: option !== oldOption,\n                  ...realUpdateOptions.value\n                });\n              }\n            },\n            { deep: true }\n          );\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      [realTheme, realInitOptions],\n      () => {\n        cleanup();\n        init();\n      },\n      {\n        deep: true\n      }\n    );\n\n    watchEffect(() => {\n      if (props.group && chart.value) {\n        chart.value.group = props.group;\n      }\n    });\n\n    const publicApi = usePublicAPI(chart);\n\n    useLoading(chart, loading, loadingOptions);\n\n    useAutoresize(chart, autoresize, inner);\n\n    onMounted(() => {\n      init();\n    });\n\n    onBeforeUnmount(() => {\n      if (wcRegistered && root.value) {\n        // For registered web component, we can leverage the\n        // `disconnectedCallback` to dispose the chart instance\n        // so that we can delay the cleanup after exsiting leaving\n        // transition.\n        root.value.__dispose = cleanup;\n      } else {\n        cleanup();\n      }\n    });\n\n    return {\n      chart,\n      root,\n      inner,\n      setOption,\n      nonEventAttrs,\n      nativeListeners,\n      ...publicApi\n    };\n  },\n  render() {\n    // Vue 3 and Vue 2 have different vnode props format:\n    // See https://v3-migration.vuejs.org/breaking-changes/render-function-api.html#vnode-props-format\n    const attrs = (\n      Vue2\n        ? { attrs: this.nonEventAttrs, on: this.nativeListeners }\n        : { ...this.nonEventAttrs, ...this.nativeListeners }\n    ) as any;\n    attrs.ref = \"root\";\n    attrs.class = attrs.class ? [\"echarts\"].concat(attrs.class) : \"echarts\";\n    return h(TAG_NAME, attrs, [\n      h(\"div\", { ref: \"inner\", class: \"vue-echarts-inner\" })\n    ]);\n  }\n});\n", "import \"echarts\";\nimport ECharts, * as exported from \"./index\";\n\nexport default {\n  ...ECharts,\n  ...exported\n};\n"], "names": ["this$1", "watch", "throttle", "isRef", "unref", "inject", "computed", "watchEffect", "Vue2", "defineComponent", "shallowRef", "toRefs", "getCurrentInstance", "initChart", "nextTick", "onMounted", "onBeforeUnmount", "h"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AAiBA;IACO,IAAI,QAAQ,GAAG,WAAW;IACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,EAAE;IACrD,QAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7D,YAAY,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAC7B,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,SAAS;IACT,QAAQ,OAAO,CAAC,CAAC;IACjB,MAAK;IACL,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,EAAC;AAkRD;IACuB,OAAO,eAAe,KAAK,UAAU,GAAG,eAAe,GAAG,UAAU,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE;IACvH,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/B,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,iBAAiB,EAAE,CAAC,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,UAAU,GAAG,UAAU,EAAE,CAAC,CAAC;IACrF;;IC1TA,IAAM,YAAY,GAAG;QACnB,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,cAAc;QACd,YAAY;QACZ,qBAAqB;QACrB,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,SAAS;KACD,CAAC;IAML,SAAU,YAAY,CAC1B,KAAmC,EAAA;QAEnC,SAAS,gBAAgB,CACvB,IAAO,EAAA;YAEP,OAAO,YAAA;gBAAC,IAAO,IAAA,GAAA,EAAA,CAAA;qBAAP,IAAO,EAAA,GAAA,CAAA,EAAP,EAAO,GAAA,SAAA,CAAA,MAAA,EAAP,EAAO,EAAA,EAAA;oBAAP,IAAO,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;IACb,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;IAChB,gBAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACpD,aAAA;IACD,YAAA,OAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAS,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC7D,SAAC,CAAC;SACH;IAED,IAAA,SAAS,iBAAiB,GAAA;YACxB,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpC,QAAA,YAAY,CAAC,OAAO,CAAC,UAAA,IAAI,EAAA;gBACvB,OAAO,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACzC,SAAC,CAAC,CAAC;IAEH,QAAA,OAAO,OAAwB,CAAC;SACjC;QAED,OAAO,iBAAiB,EAAE,CAAC;IAC7B;;IClDA,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,SAAS,qBAAqB,EAAE,QAAQ,EAAE;IAC1C,EAAE,IAAI,CAAC,GAAG,EAAE;IACZ,IAAI,GAAG,GAAG;IACV,MAAM,MAAM,CAAC,qBAAqB;IAClC,MAAM,MAAM,CAAC,2BAA2B;IACxC,MAAM,MAAM,CAAC,wBAAwB;IACrC,MAAM,UAAU,QAAQ,EAAE;IAC1B,QAAQ,OAAO,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC;IACvC,OAAO;IACP,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;IACnB,GAAG;IACH,EAAE,OAAO,GAAG,CAAC,QAAQ,CAAC;IACtB,CAAC;AACD;IACA,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,SAAS,oBAAoB,EAAE,EAAE,EAAE;IACnC,EAAE,IAAI,CAAC,GAAG,EAAE;IACZ,IAAI,GAAG,GAAG;IACV,MAAM,MAAM,CAAC,oBAAoB;IACjC,MAAM,MAAM,CAAC,0BAA0B;IACvC,MAAM,MAAM,CAAC,uBAAuB;IACpC,MAAM,UAAU,EAAE,EAAE;IACpB,QAAQ,YAAY,CAAC,EAAE,CAAC,CAAC;IACzB,OAAO;IACP,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;IACnB,GAAG;AACH;IACA,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IACV,CAAC;AACD;IACA,SAAS,YAAY,EAAE,SAAS,EAAE;IAClC,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC9C;IACA,EAAE,IAAI,KAAK,CAAC,UAAU,EAAE;IACxB,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;IACzC,GAAG,MAAM;IACT,IAAI,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D,GAAG;IACH,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;IACvE,EAAE,OAAO,KAAK;IACd,CAAC;AACD;IACA,SAAS,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE;IACxC,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACrC;IACA,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC7C,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;IAC5C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,GAAG,CAAC,CAAC;IACL,EAAE,OAAO,IAAI;IACb,CAAC;AACD;IACA,SAAS,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;IAC/C;IACA;IACA;IACA,EAAE,IAAI,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI,CAAC,IAAI;IACvE,IAAI,OAAO,EAAE,MAAM;IACnB,GAAG,CAAC;AACJ;IACA,EAAE,OAAO,aAAa,CAAC,IAAI,CAAC;IAC5B,CAAC;AACD;IACA,SAAS,aAAa,EAAE,IAAI,EAAE;IAC9B,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAChD,IAAI,OAAO;IACX,MAAM,QAAQ,EAAE,IAAI;IACpB,MAAM,QAAQ,EAAE,KAAK;IACrB,KAAK;IACL,GAAG;AACH;IACA,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC;IACrB,EAAE,OAAO,OAAO,KAAK,QAAQ,EAAE;IAC/B,IAAI,IAAI,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK,MAAM,EAAE;IACzD,MAAM,OAAO;IACb,QAAQ,QAAQ,EAAE,KAAK;IACvB,QAAQ,QAAQ,EAAE,KAAK;IACvB,OAAO;IACP,KAAK;IACL,IAAI,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC;IACjC,GAAG;AACH;IACA,EAAE,OAAO;IACT,IAAI,QAAQ,EAAE,KAAK;IACnB,IAAI,QAAQ,EAAE,IAAI;IAClB,GAAG;IACH,CAAC;AACD;IACA,IAAI,QAAQ,GAAG,4XAA4X,CAAC;AAC5Y;IACA,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB;IACA,SAAS,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;IACtC,EAAE,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;IACzC,IAAI,IAAI,CAAC,2BAA2B,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjE,GAAG;AACH;IACA,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC5C;IACA,EAAE,IAAI,CAAC,SAAS,EAAE;IAClB,IAAI,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;IACnC,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IACzC,MAAM,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;IAC3C,MAAM,IAAI,EAAE,GAAG,IAAI,cAAc,CAAC,YAAY;IAC9C,QAAQ,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;IACjD,UAAU,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;IACpD,UAAU,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW,IAAI,IAAI,CAAC,YAAY,KAAK,YAAY,EAAE;IACtF,YAAY,MAAM;IAClB,WAAW;IACX,SAAS;IACT,QAAQ,YAAY,CAAC,IAAI,CAAC,CAAC;IAC3B,OAAO,CAAC,CAAC;AACT;IACA;IACA,MAAM,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAClC,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAClC,MAAM,IAAI,CAAC,6BAA6B,GAAG,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,KAAK,CAAC;IACpF,MAAM,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IACpC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE;IAC1D;IACA,MAAM,IAAI,CAAC,gCAAgC,GAAG,SAAS,kBAAkB,IAAI;IAC7E,QAAQ,YAAY,CAAC,IAAI,CAAC,CAAC;IAC3B,OAAO,CAAC;IACR,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAC1E,MAAM,QAAQ,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;IACxF,KAAK,MAAM;IACX,MAAM,IAAI,CAAC,KAAK,EAAE;IAClB,QAAQ,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;IACvC,OAAO;IACP,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AACzB;IACA,MAAM,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;IAC9D,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE;IACnC,QAAQ,IAAI,EAAE,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IACxE,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE;IAC7B,UAAU,UAAU,EAAE,IAAI;IAC1B,UAAU,SAAS,EAAE,IAAI;IACzB,UAAU,aAAa,EAAE,IAAI;IAC7B,UAAU,OAAO,EAAE,IAAI;IACvB,SAAS,CAAC,CAAC;IACX,QAAQ,IAAI,CAAC,4BAA4B,GAAG,EAAE,CAAC;IAC/C,OAAO;IACP,KAAK;IACL,GAAG;AACH;IACA,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3C,EAAE,KAAK,EAAE,CAAC;IACV,CAAC;AACD;IACA,SAAS,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE;IACzC,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC;IAC5C,EAAE,IAAI,CAAC,SAAS,EAAE;IAClB,IAAI,MAAM;IACV,GAAG;AACH;IACA,EAAE,IAAI,QAAQ,EAAE;IAChB,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACrD,GAAG;AACH;IACA;IACA,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;IACtC;IACA,IAAI,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,mBAAmB,EAAE;IACtD,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAC1E,MAAM,QAAQ,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC3F,MAAM,MAAM;IACZ,KAAK;AACL;IACA,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE;IAClC,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;IAC5C,MAAM,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACtC,KAAK,MAAM;IACX,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE;IAC7C,QAAQ,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,CAAC;IACvD,QAAQ,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;IACjD,OAAO;IACP,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IACvD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC1D,MAAM,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACtC,KAAK;IACL,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACrC,GAAG;AACH;IACA,EAAE,IAAI,CAAC,EAAE,KAAK,IAAI,KAAK,EAAE;IACzB,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACxC,GAAG;IACH,CAAC;AACD;IACA,SAAS,cAAc,EAAE,IAAI,EAAE;IAC/B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;IACjC,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IACxB,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IACrC,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;IACvC,EAAE,IAAI,WAAW,KAAK,KAAK,IAAI,YAAY,KAAK,MAAM,EAAE;IACxD,IAAI,OAAO;IACX,MAAM,KAAK,EAAE,WAAW;IACxB,MAAM,MAAM,EAAE,YAAY;IAC1B,KAAK;IACL,GAAG;IACH,EAAE,OAAO,IAAI;IACb,CAAC;AACD;IACA,SAAS,cAAc,IAAI;IAC3B;IACA,EAAE,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IAChC,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,EAAE,IAAI,QAAQ,KAAK,IAAI,CAAC,mBAAmB,EAAE;IAC7C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,EAAE;IAC/C,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC;IAC1B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;IAC1D,KAAK;IACL,IAAI,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;IACxC,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;IACvB,GAAG;IACH,CAAC;AACD;IACA,SAAS,YAAY,IAAI;IACzB,EAAE,IAAIA,QAAM,GAAG,IAAI,CAAC;AACpB;IACA;IACA,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;IACtB,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE;IAC3B,IAAI,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC9C,GAAG;IACH,EAAE,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC,YAAY;IAC1D,IAAI,IAAI,OAAO,GAAG,cAAc,CAACA,QAAM,CAAC,CAAC;IACzC,IAAI,IAAI,OAAO,EAAE;IACjB,MAAMA,QAAM,CAAC,eAAe,GAAG,OAAO,CAAC;IACvC,MAAM,YAAY,CAACA,QAAM,CAAC,CAAC;IAC3B,KAAK;IACL,GAAG,CAAC,CAAC;IACL,CAAC;AACD;IACA,SAAS,YAAY,EAAE,IAAI,EAAE;IAC7B,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;IAC3C,IAAI,MAAM;IACV,GAAG;IACH,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;IACxD,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,GAAG,CAAC,CAAC;IACL,CAAC;AACD;IACA,SAAS,YAAY,EAAE,IAAI,EAAE;IAC7B,EAAE,IAAI,QAAQ,GAAG,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACpD,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,QAAQ,EAAE;IAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;IACrC,GAAG;AACH;IACA,EAAE,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC;IAC1C,EAAE,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC5B;IACA,EAAE,IAAI,QAAQ,GAAG,aAAa,CAAC,KAAK,EAAE;IACtC,IAAI,SAAS,EAAE,iBAAiB;IAChC,GAAG,CAAC,CAAC;IACL,EAAE,IAAI,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE;IACpC,IAAI,SAAS,EAAE,uBAAuB;IACtC,GAAG,CAAC,CAAC;IACL,EAAE,IAAI,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IACzC,EAAE,IAAI,QAAQ,GAAG,aAAa,CAAC,KAAK,EAAE;IACtC,IAAI,SAAS,EAAE,yBAAyB;IACxC,GAAG,CAAC,CAAC;IACL,EAAE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAClC,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC/B,EAAE,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACjC,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC7B;IACA,EAAE,IAAI,CAAC,mBAAmB,GAAG;IAC7B,IAAI,QAAQ,EAAE,QAAQ;IACtB,IAAI,MAAM,EAAE,MAAM;IAClB,IAAI,WAAW,EAAE,WAAW;IAC5B,IAAI,QAAQ,EAAE,QAAQ;IACtB,GAAG,CAAC;AACJ;IACA,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;IACtB,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;AACtD;IACA,EAAE,IAAI,CAAC,eAAe,GAAG;IACzB,IAAI,KAAK,EAAE,IAAI,CAAC,WAAW;IAC3B,IAAI,MAAM,EAAE,IAAI,CAAC,YAAY;IAC7B,GAAG,CAAC;IACJ,CAAC;AACD;IACA,SAAS,aAAa,EAAE,IAAI,EAAE;IAC9B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC;IACrC,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,EAAE,IAAI,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;IACpC,EAAE,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAC9B;IACA;IACA,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC;IACjC,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC,YAAY,CAAC;IAClC,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;IAC/B,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC;IAChC,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;IAC/B,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC;AAChC;IACA;IACA,EAAE,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;IAC5B,EAAE,QAAQ,CAAC,SAAS,GAAG,GAAG,CAAC;IAC3B,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;IAC3C,EAAE,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;IAC5C,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC;IAC1B,EAAE,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC;IACzB;;aCvSgB,aAAa,CAC3B,KAAmC,EACnC,UAA2C,EAC3C,IAAkC,EAAA;QAElC,IAAI,cAAc,GAA0B,IAAI,CAAC;IAEjD,IAAAC,aAAK,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,UAAC,EAAyB,EAAE,CAAC,EAAE,OAAO,EAAA;IAApC,QAAA,IAAA,IAAI,QAAA,EAAE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,UAAU,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACxD,QAAA,IAAI,IAAI,IAAI,KAAK,IAAI,UAAU,EAAE;IAC/B,YAAA,IAAM,iBAAiB,GAAG,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,UAAU,CAAC;IACxD,YAAA,IAAA,EAAmC,GAAA,iBAAiB,CAAhC,QAAA,EAAV,IAAI,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,GAAG,GAAA,EAAA,EAAE,UAAQ,GAAK,iBAAiB,SAAtB,CAAuB;IAE7D,YAAA,IAAM,QAAQ,GAAG,YAAA;oBACf,KAAK,CAAC,MAAM,EAAE,CAAC;IACf,gBAAA,UAAQ,KAAR,IAAA,IAAA,UAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAQ,EAAI,CAAC;IACf,aAAC,CAAC;IAEF,YAAA,cAAc,GAAG,IAAI,GAAGC,aAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC;IAC5D,YAAA,WAAW,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACnC,SAAA;IAED,QAAA,OAAO,CAAC,YAAA;gBACN,IAAI,IAAI,IAAI,cAAc,EAAE;IAC1B,gBAAA,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACtC,aAAA;IACH,SAAC,CAAC,CAAC;IACL,KAAC,CAAC,CAAC;IACL,CAAC;IAEM,IAAM,eAAe,GAAG;IAC7B,IAAA,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,CAA6B;KAC1D;;ICrCD,IAAM,IAAI,GAAG,WAAW,CAAC;IAClB,IAAM,IAAI,GAAG,UAAC,GAAW,EAAc,EAAA,OAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,EAAA,CAAC;IAEvD,SAAU,MAAM,CAAC,KAAY,EAAA;QACjC,IAAM,MAAM,GAAU,EAAE,CAAC;IACzB,IAAA,KAAK,IAAM,GAAG,IAAI,KAAK,EAAE;IACvB,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACd,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1B,SAAA;IACF,KAAA;IAED,IAAA,OAAO,MAAM,CAAC;IAChB,CAAC;IAEe,SAAA,cAAc,CAC5B,SAAuB,EACvB,YAAe,EAAA;IAEf,IAAA,IAAM,KAAK,GAAGC,aAAK,CAAC,SAAS,CAAC,GAAGC,aAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;QAE9D,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,EAAE;IAC1D,QAAA,OAAO,KAAK,CAAC,KAAK,IAAI,YAAY,CAAC;IACpC,KAAA;QAED,OAAO,KAAK,IAAI,YAAY,CAAC;IAC/B;;ICxBO,IAAM,mBAAmB,GAC9B,kBAEC,CAAC;aAEY,UAAU,CACxB,KAAmC,EACnC,OAAqB,EACrB,cAA+C,EAAA;QAE/C,IAAM,qBAAqB,GAAGC,cAAM,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QAC9D,IAAM,kBAAkB,GAAGC,gBAAQ,CAAC,YAAA,EAAM,QACrC,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,cAAc,CAAC,qBAAqB,EAAE,EAAE,CAAC,CACzC,EAAA,cAAc,KAAd,IAAA,IAAA,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAE,KAAK,CACxB,EAAA,EAAA,CAAC,CAAC;IAEJ,IAAAC,mBAAW,CAAC,YAAA;IACV,QAAA,IAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC;YAC7B,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO;IACR,SAAA;YAED,IAAI,OAAO,CAAC,KAAK,EAAE;IACjB,YAAA,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAChD,SAAA;IAAM,aAAA;gBACL,QAAQ,CAAC,WAAW,EAAE,CAAC;IACxB,SAAA;IACH,KAAC,CAAC,CAAC;IACL,CAAC;IAEM,IAAM,YAAY,GAAG;IAC1B,IAAA,OAAO,EAAE,OAAO;IAChB,IAAA,cAAc,EAAE,MAAkC;KACnD;;IC1CM,IAAM,QAAQ,GAAG,eAAe;;IC4CvC,IAAIC,YAAI,EAAE;QACRA,YAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAA;IAEM,IAAM,SAAS,GAAG,SAAoD,CAAC;IACvE,IAAM,gBAAgB,GAC3B,eAAgE,CAAC;IAC5D,IAAM,kBAAkB,GAC7B,iBAAoE,CAAC;IAGvE,IAAM,eAAe,GAAG,kBAAkB,CAAC;AAE3C,kBAAeC,uBAAe,CAAC;IAC7B,IAAA,IAAI,EAAE,SAAS;IACf,IAAA,KAAK,sBACH,MAAM,EAAE,MAA0B,EAClC,KAAK,EAAE;IACL,YAAA,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAoB;IAC1C,SAAA,EACD,WAAW,EAAE,MAA+B,EAC5C,aAAa,EAAE,MAAiC,EAChD,KAAK,EAAE,MAAM,EACb,YAAY,EAAE,OAAO,IAClB,eAAe,CAAA,EACf,YAAY,CAChB;IACD,IAAA,KAAK,EAAE,EAAsB;IAC7B,IAAA,YAAY,EAAE,KAAK;IACnB,IAAA,KAAK,EAAL,UAAM,KAAK,EAAE,EAAS,EAAA;IAAP,QAAA,IAAA,KAAK,GAAA,EAAA,CAAA,KAAA,CAAA;IAClB,QAAA,IAAM,IAAI,GAAGC,kBAAU,EAAkB,CAAC;IAC1C,QAAA,IAAM,KAAK,GAAGA,kBAAU,EAAe,CAAC;IACxC,QAAA,IAAM,KAAK,GAAGA,kBAAU,EAAe,CAAC;IACxC,QAAA,IAAM,YAAY,GAAGA,kBAAU,EAAU,CAAC;YAC1C,IAAM,YAAY,GAAGL,cAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC7C,IAAM,kBAAkB,GAAGA,cAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAC1D,IAAM,oBAAoB,GAAGA,cAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;IAExD,QAAA,IAAA,KAAwDM,cAAM,CAAC,KAAK,CAAC,EAAnE,UAAU,GAAA,EAAA,CAAA,UAAA,EAAE,YAAY,kBAAA,EAAE,OAAO,aAAA,EAAE,cAAc,oBAAkB,CAAC;IAE5E,QAAA,IAAM,UAAU,GAAGL,gBAAQ,CACzB,YAAM,EAAA,OAAA,YAAY,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAA1C,EAA0C,CACjD,CAAC;IACF,QAAA,IAAM,SAAS,GAAGA,gBAAQ,CACxB,YAAM,EAAA,OAAA,KAAK,CAAC,KAAK,IAAI,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC,CAA/C,EAA+C,CACtD,CAAC;IACF,QAAA,IAAM,eAAe,GAAGA,gBAAQ,CAC9B,YAAM,EAAA,OAAA,KAAK,CAAC,WAAW,IAAI,cAAc,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAA3D,EAA2D,CAClE,CAAC;IACF,QAAA,IAAM,iBAAiB,GAAGA,gBAAQ,CAChC,YAAM,EAAA,OAAA,KAAK,CAAC,aAAa,IAAI,cAAc,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAA/D,EAA+D,CACtE,CAAC;IACF,QAAA,IAAM,aAAa,GAAGA,gBAAQ,CAAC,YAAM,EAAA,OAAA,MAAM,CAAC,KAAK,CAAC,CAAb,EAAa,CAAC,CAAC;YACpD,IAAM,eAAe,GAA4B,EAAE,CAAC;YAGpD,IAAM,SAAS,GAAGM,0BAAkB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;YACxD,IAAM,aAAa,GAAwB,EAAE,CAAC;YAE9C,IAAI,CAAC,SAAS,EAAE;IAOd,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;qBACf,MAAM,CAAC,UAAA,GAAG,EAAI,EAAA,OAAA,IAAI,CAAC,GAAG,CAAC,CAAT,EAAS,CAAC;qBACxB,OAAO,CAAC,UAAA,GAAG,EAAA;IAGV,gBAAA,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAGvD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;wBAElC,IAAM,SAAS,GAAG,IAAK,CAAA,MAAA,CAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA,CAAA,MAAA,CAAG,KAAK,CAAC,KAAK,CAChE,CAAC,CACF,CAAE,CAAC;wBAEJ,eAAe,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;wBACxC,OAAO;IACR,iBAAA;IAID,gBAAA,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE;IAChD,oBAAA,KAAK,GAAG,GAAA,CAAA,MAAA,CAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAE,CAAC;IACpD,iBAAA;oBAED,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IACpC,aAAC,CAAC,CAAC;IACN,SAAA;IAAM,aAAA;gBASL,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG,EAAA;IAChC,gBAAA,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IAC7B,oBAAA,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IACtE,iBAAA;IAAM,qBAAA;wBACL,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IACrC,iBAAA;IACH,aAAC,CAAC,CAAC;IACJ,SAAA;YAED,SAAS,IAAI,CAAC,MAAe,EAAA;IAC3B,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;oBAChB,OAAO;IACR,aAAA;gBAED,IAAM,QAAQ,IAAI,KAAK,CAAC,KAAK,GAAGC,SAAS,CACvC,KAAK,CAAC,KAAK,EACX,SAAS,CAAC,KAAK,EACf,eAAe,CAAC,KAAK,CACtB,CAAC,CAAC;gBAEH,IAAI,KAAK,CAAC,KAAK,EAAE;IACf,gBAAA,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC9B,aAAA;gBAED,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG,EAAA;IACpC,gBAAA,IAAI,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;oBAEjC,IAAI,CAAC,OAAO,EAAE;wBACZ,OAAO;IACR,iBAAA;IAED,gBAAA,IAAI,KAAK,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;oBAC9B,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3B,oBAAA,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3B,oBAAA,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,iBAAA;oBAED,IAAI,MAAM,GAAgB,QAAQ,CAAC;oBACnC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IAC9B,oBAAA,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;IAC1B,oBAAA,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5B,iBAAA;oBAED,IAAI,OAAO,CAAC,QAAQ,EAAE;wBACpB,OAAO,OAAO,CAAC,QAAQ,CAAC;wBAExB,IAAM,KAAG,GAAG,OAAO,CAAC;IAEpB,oBAAA,OAAO,GAAG,YAAA;4BAAC,IAAc,IAAA,GAAA,EAAA,CAAA;iCAAd,IAAc,EAAA,GAAA,CAAA,EAAd,EAAc,GAAA,SAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAA;gCAAd,IAAc,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;4BACvB,KAAG,CAAA,KAAA,CAAA,KAAA,CAAA,EAAI,IAAI,CAAE,CAAA;IACb,wBAAA,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7B,qBAAC,CAAC;IACH,iBAAA;IAKD,gBAAA,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC5B,aAAC,CAAC,CAAC;IAEH,YAAA,SAAS,MAAM,GAAA;IACb,gBAAA,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE;wBACtC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACnB,iBAAA;iBACF;IAED,YAAA,SAAS,MAAM,GAAA;IACb,gBAAA,IAAM,GAAG,GAAG,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC;IACvC,gBAAA,IAAI,GAAG,EAAE;wBACP,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAClD,iBAAA;iBACF;gBAED,IAAI,UAAU,CAAC,KAAK,EAAE;IAGpB,gBAAAC,gBAAQ,CAAC,YAAA;IACP,oBAAA,MAAM,EAAE,CAAC;IACT,oBAAA,MAAM,EAAE,CAAC;IACX,iBAAC,CAAC,CAAC;IACJ,aAAA;IAAM,iBAAA;IACL,gBAAA,MAAM,EAAE,CAAC;IACV,aAAA;aACF;IAED,QAAA,SAAS,SAAS,CAAC,MAAc,EAAE,aAA6B,EAAA;gBAC9D,IAAI,KAAK,CAAC,YAAY,EAAE;IACtB,gBAAA,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;IAC7B,aAAA;IAED,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;oBAChB,IAAI,CAAC,MAAM,CAAC,CAAC;IACd,aAAA;IAAM,iBAAA;oBACL,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,IAAI,EAAE,CAAC,CAAC;IACpD,aAAA;aACF;IAED,QAAA,SAAS,OAAO,GAAA;gBACd,IAAI,KAAK,CAAC,KAAK,EAAE;IACf,gBAAA,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACtB,gBAAA,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;IACzB,aAAA;aACF;YAED,IAAI,aAAa,GAAwB,IAAI,CAAC;IAC9C,QAAAb,aAAK,CACH,YAAY,EACZ,UAAA,YAAY,EAAA;IACV,YAAA,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;IACvC,gBAAA,aAAa,EAAE,CAAC;oBAChB,aAAa,GAAG,IAAI,CAAC;IACtB,aAAA;gBAED,IAAI,CAAC,YAAY,EAAE;IACjB,gBAAA,aAAa,GAAGA,aAAK,CACnB,YAAA,EAAM,OAAA,KAAK,CAAC,MAAM,CAAA,EAAA,EAClB,UAAC,MAAM,EAAE,SAAS,EAAA;wBAChB,IAAI,CAAC,MAAM,EAAE;4BACX,OAAO;IACR,qBAAA;IACD,oBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;IAChB,wBAAA,IAAI,EAAE,CAAC;IACR,qBAAA;IAAM,yBAAA;IACL,wBAAA,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAG1B,QAAA,CAAA,EAAA,QAAQ,EAAE,MAAM,KAAK,SAAS,EAAA,EAC3B,iBAAiB,CAAC,KAAK,EAC1B,CAAC;IACJ,qBAAA;IACH,iBAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;IACH,aAAA;IACH,SAAC,EACD;IACE,YAAA,SAAS,EAAE,IAAI;IAChB,SAAA,CACF,CAAC;IAEF,QAAAA,aAAK,CACH,CAAC,SAAS,EAAE,eAAe,CAAC,EAC5B,YAAA;IACE,YAAA,OAAO,EAAE,CAAC;IACV,YAAA,IAAI,EAAE,CAAC;IACT,SAAC,EACD;IACE,YAAA,IAAI,EAAE,IAAI;IACX,SAAA,CACF,CAAC;IAEF,QAAAM,mBAAW,CAAC,YAAA;IACV,YAAA,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;oBAC9B,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IACjC,aAAA;IACH,SAAC,CAAC,CAAC;IAEH,QAAA,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IAEtC,QAAA,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;IAE3C,QAAA,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IAExC,QAAAQ,iBAAS,CAAC,YAAA;IACR,YAAA,IAAI,EAAE,CAAC;IACT,SAAC,CAAC,CAAC;IAEH,QAAAC,uBAAe,CAAC,YAAA;IACd,YAMO;IACL,gBAAA,OAAO,EAAE,CAAC;IACX,aAAA;IACH,SAAC,CAAC,CAAC;IAEH,QAAA,OAAA,QAAA,CAAA,EACE,KAAK,EAAA,KAAA,EACL,IAAI,EAAA,IAAA,EACJ,KAAK,EAAA,KAAA,EACL,SAAS,EAAA,SAAA,EACT,aAAa,EAAA,aAAA,EACb,eAAe,EAAA,eAAA,EAAA,EACZ,SAAS,CACZ,CAAA;SACH;IACD,IAAA,MAAM,EAAN,YAAA;YAGE,IAAM,KAAK,IACTR,YAAI;IACF,cAAE,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE;kBACxD,QAAA,CAAA,QAAA,CAAA,EAAA,EAAM,IAAI,CAAC,aAAa,CAAA,EAAK,IAAI,CAAC,eAAe,CAAE,CAChD,CAAC;IACT,QAAA,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC;YACnB,KAAK,CAAC,OAAK,CAAA,GAAG,KAAK,CAAC,OAAK,CAAA,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAK,CAAA,CAAC,GAAG,SAAS,CAAC;IACxE,QAAA,OAAOS,SAAC,CAAC,QAAQ,EAAE,KAAK,EAAE;IACxB,YAAAA,SAAC,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,OAAK,EAAE,mBAAmB,EAAE,CAAC;IACvD,SAAA,CAAC,CAAC;SACJ;IACF,CAAA,CAAC;;;;;;;;;;;AC3VF,iBACK,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,OAAO,CACP,EAAA,QAAQ,CACX;;;;;;;;"}