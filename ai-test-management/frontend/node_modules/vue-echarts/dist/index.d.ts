import { PropType, InjectionKey } from "vue-demi";
import { Ref as Ref$0 } from "vue-demi";
import { init } from "echarts/core";
import { SetOptionOpts, ECElementEvent, ElementEvent } from "echarts";
import { Ref } from "vue";
type Injection<T> = T | null | Ref<T | null> | {
    value: T | null;
};
type InitType = typeof init;
type InitParameters = Parameters<InitType>;
type Theme = NonNullable<InitParameters[1]>;
type ThemeInjection = Injection<Theme>;
type InitOptions = NonNullable<InitParameters[2]>;
type InitOptionsInjection = Injection<InitOptions>;
type UpdateOptions = SetOptionOpts;
type UpdateOptionsInjection = Injection<UpdateOptions>;
type EChartsType = ReturnType<InitType>;
type SetOptionType = EChartsType["setOption"];
type Option = Parameters<SetOptionType>[0];
type LoadingOptions = {
    text?: string;
    textColor?: string;
    fontSize?: number | string;
    fontWeight?: number | string;
    fontStyle?: string;
    fontFamily?: string;
    maskColor?: string;
    showSpinner?: boolean;
    color?: string;
    spinnerRadius?: number;
    lineWidth?: number;
    zlevel?: number;
};
interface EChartsElement extends HTMLElement {
    __dispose: (() => void) | null;
}
declare const THEME_KEY: InjectionKey<ThemeInjection>;
declare const INIT_OPTIONS_KEY: InjectionKey<InitOptionsInjection>;
declare const UPDATE_OPTIONS_KEY: InjectionKey<UpdateOptionsInjection>;
declare const LOADING_OPTIONS_KEY: InjectionKey<LoadingOptions | Ref$0<LoadingOptions>>;
declare const _default: import("vue-demi").DefineComponent<{
    loading: BooleanConstructor;
    loadingOptions: PropType<LoadingOptions>;
    autoresize: PropType<boolean | {
        throttle?: number | undefined;
        onResize?: (() => void) | undefined;
    }>;
    option: PropType<import("echarts/types/dist/shared").ECBasicOption>;
    theme: {
        type: PropType<Theme>;
    };
    initOptions: PropType<import("echarts/types/dist/shared").EChartsInitOpts>;
    updateOptions: PropType<import("echarts/types/dist/echarts").SetOptionOpts>;
    group: StringConstructor;
    manualUpdate: BooleanConstructor;
}, {
    getWidth: () => number;
    getHeight: () => number;
    getDom: () => HTMLElement;
    getOption: () => import("echarts/types/dist/shared").ECBasicOption;
    resize: (opts?: import("echarts/types/dist/shared").ResizeOpts | undefined) => void;
    dispatchAction: (payload: import("echarts/types/dist/shared").Payload, opt?: boolean | {
        silent?: boolean | undefined;
        flush?: boolean | undefined;
    } | undefined) => void;
    convertToPixel: {
        (finder: string | {
            seriesIndex?: (number | false | number[] | "all" | "none") | undefined;
            seriesId?: ((string | number) | (string | number)[]) | undefined;
            seriesName?: ((string | number) | (string | number)[]) | undefined;
            geoIndex?: (number | false | number[] | "all" | "none") | undefined;
            geoId?: ((string | number) | (string | number)[]) | undefined;
            geoName?: ((string | number) | (string | number)[]) | undefined;
            bmapIndex?: (number | false | number[] | "all" | "none") | undefined;
            bmapId?: ((string | number) | (string | number)[]) | undefined;
            bmapName?: ((string | number) | (string | number)[]) | undefined;
            xAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            xAxisId?: ((string | number) | (string | number)[]) | undefined;
            xAxisName?: ((string | number) | (string | number)[]) | undefined;
            yAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            yAxisId?: ((string | number) | (string | number)[]) | undefined;
            yAxisName?: ((string | number) | (string | number)[]) | undefined;
            gridIndex?: (number | false | number[] | "all" | "none") | undefined;
            gridId?: ((string | number) | (string | number)[]) | undefined;
            gridName?: ((string | number) | (string | number)[]) | undefined;
            dataIndex?: number | undefined;
            dataIndexInside?: number | undefined;
        }, value: (string | number) | Date): number;
        (finder: string | {
            seriesIndex?: (number | false | number[] | "all" | "none") | undefined;
            seriesId?: ((string | number) | (string | number)[]) | undefined;
            seriesName?: ((string | number) | (string | number)[]) | undefined;
            geoIndex?: (number | false | number[] | "all" | "none") | undefined;
            geoId?: ((string | number) | (string | number)[]) | undefined;
            geoName?: ((string | number) | (string | number)[]) | undefined;
            bmapIndex?: (number | false | number[] | "all" | "none") | undefined;
            bmapId?: ((string | number) | (string | number)[]) | undefined;
            bmapName?: ((string | number) | (string | number)[]) | undefined;
            xAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            xAxisId?: ((string | number) | (string | number)[]) | undefined;
            xAxisName?: ((string | number) | (string | number)[]) | undefined;
            yAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            yAxisId?: ((string | number) | (string | number)[]) | undefined;
            yAxisName?: ((string | number) | (string | number)[]) | undefined;
            gridIndex?: (number | false | number[] | "all" | "none") | undefined;
            gridId?: ((string | number) | (string | number)[]) | undefined;
            gridName?: ((string | number) | (string | number)[]) | undefined;
            dataIndex?: number | undefined;
            dataIndexInside?: number | undefined;
        }, value: ((string | number) | Date)[]): number[];
    };
    convertFromPixel: {
        (finder: string | {
            seriesIndex?: (number | false | number[] | "all" | "none") | undefined;
            seriesId?: ((string | number) | (string | number)[]) | undefined;
            seriesName?: ((string | number) | (string | number)[]) | undefined;
            geoIndex?: (number | false | number[] | "all" | "none") | undefined;
            geoId?: ((string | number) | (string | number)[]) | undefined;
            geoName?: ((string | number) | (string | number)[]) | undefined;
            bmapIndex?: (number | false | number[] | "all" | "none") | undefined;
            bmapId?: ((string | number) | (string | number)[]) | undefined;
            bmapName?: ((string | number) | (string | number)[]) | undefined;
            xAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            xAxisId?: ((string | number) | (string | number)[]) | undefined;
            xAxisName?: ((string | number) | (string | number)[]) | undefined;
            yAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            yAxisId?: ((string | number) | (string | number)[]) | undefined;
            yAxisName?: ((string | number) | (string | number)[]) | undefined;
            gridIndex?: (number | false | number[] | "all" | "none") | undefined;
            gridId?: ((string | number) | (string | number)[]) | undefined;
            gridName?: ((string | number) | (string | number)[]) | undefined;
            dataIndex?: number | undefined;
            dataIndexInside?: number | undefined;
        }, value: number): number;
        (finder: string | {
            seriesIndex?: (number | false | number[] | "all" | "none") | undefined;
            seriesId?: ((string | number) | (string | number)[]) | undefined;
            seriesName?: ((string | number) | (string | number)[]) | undefined;
            geoIndex?: (number | false | number[] | "all" | "none") | undefined;
            geoId?: ((string | number) | (string | number)[]) | undefined;
            geoName?: ((string | number) | (string | number)[]) | undefined;
            bmapIndex?: (number | false | number[] | "all" | "none") | undefined;
            bmapId?: ((string | number) | (string | number)[]) | undefined;
            bmapName?: ((string | number) | (string | number)[]) | undefined;
            xAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            xAxisId?: ((string | number) | (string | number)[]) | undefined;
            xAxisName?: ((string | number) | (string | number)[]) | undefined;
            yAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
            yAxisId?: ((string | number) | (string | number)[]) | undefined;
            yAxisName?: ((string | number) | (string | number)[]) | undefined;
            gridIndex?: (number | false | number[] | "all" | "none") | undefined;
            gridId?: ((string | number) | (string | number)[]) | undefined;
            gridName?: ((string | number) | (string | number)[]) | undefined;
            dataIndex?: number | undefined;
            dataIndexInside?: number | undefined;
        }, value: number[]): number[];
    };
    containPixel: (finder: string | {
        seriesIndex?: (number | false | number[] | "all" | "none") | undefined;
        seriesId?: ((string | number) | (string | number)[]) | undefined;
        seriesName?: ((string | number) | (string | number)[]) | undefined;
        geoIndex?: (number | false | number[] | "all" | "none") | undefined;
        geoId?: ((string | number) | (string | number)[]) | undefined;
        geoName?: ((string | number) | (string | number)[]) | undefined;
        bmapIndex?: (number | false | number[] | "all" | "none") | undefined;
        bmapId?: ((string | number) | (string | number)[]) | undefined;
        bmapName?: ((string | number) | (string | number)[]) | undefined;
        xAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
        xAxisId?: ((string | number) | (string | number)[]) | undefined;
        xAxisName?: ((string | number) | (string | number)[]) | undefined;
        yAxisIndex?: (number | false | number[] | "all" | "none") | undefined;
        yAxisId?: ((string | number) | (string | number)[]) | undefined;
        yAxisName?: ((string | number) | (string | number)[]) | undefined;
        gridIndex?: (number | false | number[] | "all" | "none") | undefined;
        gridId?: ((string | number) | (string | number)[]) | undefined;
        gridName?: ((string | number) | (string | number)[]) | undefined;
        dataIndex?: number | undefined;
        dataIndexInside?: number | undefined;
    }, value: number[]) => boolean;
    getDataURL: (opts?: {
        type?: "svg" | "png" | "jpeg" | undefined;
        pixelRatio?: number | undefined;
        backgroundColor?: import("echarts/types/dist/shared").ZRColor | undefined;
        excludeComponents?: string[] | undefined;
    } | undefined) => string;
    getConnectedDataURL: (opts?: {
        type?: "svg" | "png" | "jpeg" | undefined;
        pixelRatio?: number | undefined;
        backgroundColor?: import("echarts/types/dist/shared").ZRColor | undefined;
        connectedBackgroundColor?: import("echarts/types/dist/shared").ZRColor | undefined;
        excludeComponents?: string[] | undefined;
    } | undefined) => string;
    appendData: (params: {
        seriesIndex: number;
        data: any;
    }) => void;
    clear: () => void;
    isDisposed: () => boolean;
    dispose: () => void;
    chart: import("vue-demi").ShallowRef<import("echarts/types/dist/shared").EChartsType | undefined>;
    root: import("vue-demi").ShallowRef<EChartsElement | undefined>;
    inner: import("vue-demi").ShallowRef<HTMLElement | undefined>;
    setOption: (option: Option, updateOptions?: import("echarts/types/dist/echarts").SetOptionOpts | undefined) => void;
    nonEventAttrs: import("vue-demi").ComputedRef<{
        [key: string]: any;
    }>;
    nativeListeners: Record<string, unknown>;
}, unknown, {}, {}, import("vue-demi").ComponentOptionsMixin, import("vue-demi").ComponentOptionsMixin, {
    click: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    dblclick: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    mouseout: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    mouseover: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    mouseup: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    mousedown: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    mousemove: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    contextmenu: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
    globalout: (params: import("echarts/types/dist/echarts").ECElementEvent) => boolean;
} & {
    highlight: null;
    downplay: null;
    selectchanged: null;
    legendselectchanged: null;
    legendselected: null;
    legendunselected: null;
    legendselectall: null;
    legendinverseselect: null;
    legendscroll: null;
    datazoom: null;
    datarangeselected: null;
    graphroam: null;
    georoam: null;
    treeroam: null;
    timelinechanged: null;
    timelineplaychanged: null;
    restore: null;
    dataviewchanged: null;
    magictypechanged: null;
    geoselectchanged: null;
    geoselected: null;
    geounselected: null;
    axisareaselected: null;
    brush: null;
    brushEnd: null;
    brushselected: null;
    globalcursortaken: null;
} & {
    rendered: (params: {
        elapsedTime: number;
    }) => boolean;
    finished: () => boolean;
} & {
    "zr:mousewheel": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:drag": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:dragstart": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:dragend": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:dragenter": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:dragleave": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:dragover": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:drop": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:click": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:dblclick": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:mouseout": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:mouseover": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:mouseup": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:mousedown": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:mousemove": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:contextmenu": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
    "zr:globalout": (params: import("echarts/types/dist/echarts").ElementEvent) => boolean;
}, string, import("vue-demi").PublicProps, Readonly<import("vue-demi").ExtractPropTypes<{
    loading: BooleanConstructor;
    loadingOptions: PropType<LoadingOptions>;
    autoresize: PropType<boolean | {
        throttle?: number | undefined;
        onResize?: (() => void) | undefined;
    }>;
    option: PropType<import("echarts/types/dist/shared").ECBasicOption>;
    theme: {
        type: PropType<Theme>;
    };
    initOptions: PropType<import("echarts/types/dist/shared").EChartsInitOpts>;
    updateOptions: PropType<import("echarts/types/dist/echarts").SetOptionOpts>;
    group: StringConstructor;
    manualUpdate: BooleanConstructor;
}>> & {
    onClick?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onDblclick?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onMouseout?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onMouseover?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onMouseup?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onMousedown?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onMousemove?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onContextmenu?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    onGlobalout?: ((params: import("echarts/types/dist/echarts").ECElementEvent) => any) | undefined;
    "onZr:mousewheel"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:drag"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:dragstart"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:dragend"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:dragenter"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:dragleave"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:dragover"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:drop"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:click"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:dblclick"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:mouseout"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:mouseover"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:mouseup"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:mousedown"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:mousemove"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:contextmenu"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    "onZr:globalout"?: ((params: import("echarts/types/dist/echarts").ElementEvent) => any) | undefined;
    onHighlight?: ((...args: any[]) => any) | undefined;
    onDownplay?: ((...args: any[]) => any) | undefined;
    onSelectchanged?: ((...args: any[]) => any) | undefined;
    onLegendselectchanged?: ((...args: any[]) => any) | undefined;
    onLegendselected?: ((...args: any[]) => any) | undefined;
    onLegendunselected?: ((...args: any[]) => any) | undefined;
    onLegendselectall?: ((...args: any[]) => any) | undefined;
    onLegendinverseselect?: ((...args: any[]) => any) | undefined;
    onLegendscroll?: ((...args: any[]) => any) | undefined;
    onDatazoom?: ((...args: any[]) => any) | undefined;
    onDatarangeselected?: ((...args: any[]) => any) | undefined;
    onGraphroam?: ((...args: any[]) => any) | undefined;
    onGeoroam?: ((...args: any[]) => any) | undefined;
    onTreeroam?: ((...args: any[]) => any) | undefined;
    onTimelinechanged?: ((...args: any[]) => any) | undefined;
    onTimelineplaychanged?: ((...args: any[]) => any) | undefined;
    onRestore?: ((...args: any[]) => any) | undefined;
    onDataviewchanged?: ((...args: any[]) => any) | undefined;
    onMagictypechanged?: ((...args: any[]) => any) | undefined;
    onGeoselectchanged?: ((...args: any[]) => any) | undefined;
    onGeoselected?: ((...args: any[]) => any) | undefined;
    onGeounselected?: ((...args: any[]) => any) | undefined;
    onAxisareaselected?: ((...args: any[]) => any) | undefined;
    onBrush?: ((...args: any[]) => any) | undefined;
    onBrushEnd?: ((...args: any[]) => any) | undefined;
    onBrushselected?: ((...args: any[]) => any) | undefined;
    onGlobalcursortaken?: ((...args: any[]) => any) | undefined;
    onRendered?: ((params: {
        elapsedTime: number;
    }) => any) | undefined;
    onFinished?: (() => any) | undefined;
}, {
    manualUpdate: boolean;
    loading: boolean;
}, {}>;
declare const ECharts: typeof _default;
export { ECharts as default, THEME_KEY, INIT_OPTIONS_KEY, UPDATE_OPTIONS_KEY, LOADING_OPTIONS_KEY, _default };
