"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("vue-demi"),t=require("echarts/core"),n=require("resize-detector"),r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var o=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function i(e){return t=Object.create(null),o.forEach((function(n){t[n]=function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[t].apply(e.value,n)}}(n)})),t;var t}var a={autoresize:[Boolean,Object]},u=/^on[^a-z]/,s=function(e){return u.test(e)};function c(t,n){var r=e.isRef(t)?e.unref(t):t;return r&&"object"==typeof r&&"value"in r?r.value||n:r||n}var l="ecLoadingOptions";var f={loading:Boolean,loadingOptions:Object},p=null,d="x-vue-echarts";var v=[],h=[];!function(e,t){if(e&&"undefined"!=typeof document){var n,r=!0===t.prepend?"prepend":"append",o=!0===t.singleTag,i="string"==typeof t.container?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(o){var a=v.indexOf(i);-1===a&&(a=v.push(i)-1,h[a]={}),n=h[a]&&h[a][r]?h[a][r]:h[a][r]=u()}else n=u();65279===e.charCodeAt(0)&&(e=e.substring(1)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(document.createTextNode(e))}function u(){var e=document.createElement("style");if(e.setAttribute("type","text/css"),t.attributes)for(var n=Object.keys(t.attributes),o=0;o<n.length;o++)e.setAttribute(n[o],t.attributes[n[o]]);var a="prepend"===r?"afterbegin":"beforeend";return i.insertAdjacentElement(a,e),e}}("x-vue-echarts{display:flex;flex-direction:column;width:100%;height:100%;min-width:0}\n.vue-echarts-inner{flex-grow:1;min-width:0;width:auto!important;height:auto!important}\n",{});var g=function(){if(null!=p)return p;if("undefined"==typeof HTMLElement||"undefined"==typeof customElements)return p=!1;try{new Function("tag","class EChartsElement extends HTMLElement {\n  __dispose = null;\n\n  disconnectedCallback() {\n    if (this.__dispose) {\n      this.__dispose();\n      this.__dispose = null;\n    }\n  }\n}\n\nif (customElements.get(tag) == null) {\n  customElements.define(tag, EChartsElement);\n}\n")(d)}catch(e){return p=!1}return p=!0}();e.Vue2&&e.Vue2.config.ignoredElements.push(d);var m="ecTheme",O="ecInitOptions",E="ecUpdateOptions",_=/(^&?~?!?)native:/,b=e.defineComponent({name:"echarts",props:r(r({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},a),f),emits:{},inheritAttrs:!1,setup:function(o,a){var u=a.attrs,f=e.shallowRef(),p=e.shallowRef(),d=e.shallowRef(),v=e.shallowRef(),h=e.inject(m,null),b=e.inject(O,null),y=e.inject(E,null),x=e.toRefs(o),w=x.autoresize,j=x.manualUpdate,L=x.loading,T=x.loadingOptions,A=e.computed((function(){return v.value||o.option||null})),C=e.computed((function(){return o.theme||c(h,{})})),z=e.computed((function(){return o.initOptions||c(b,{})})),S=e.computed((function(){return o.updateOptions||c(y,{})})),P=e.computed((function(){return function(e){var t={};for(var n in e)s(n)||(t[n]=e[n]);return t}(u)})),R={},U=e.getCurrentInstance().proxy.$listeners,D={};function I(n){if(p.value){var r=d.value=t.init(p.value,C.value,z.value);o.group&&(r.group=o.group),Object.keys(D).forEach((function(e){var t=D[e];if(t){var n=e.toLowerCase();"~"===n.charAt(0)&&(n=n.substring(1),t.__once__=!0);var o=r;if(0===n.indexOf("zr:")&&(o=r.getZr(),n=n.substring(3)),t.__once__){delete t.__once__;var i=t;t=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];i.apply(void 0,e),o.off(n,t)}}o.on(n,t)}})),w.value?e.nextTick((function(){r&&!r.isDisposed()&&r.resize(),i()})):i()}function i(){var e=n||A.value;e&&r.setOption(e,S.value)}}function N(){d.value&&(d.value.dispose(),d.value=void 0)}U?Object.keys(U).forEach((function(e){_.test(e)?R[e.replace(_,"$1")]=U[e]:D[e]=U[e]})):Object.keys(u).filter((function(e){return s(e)})).forEach((function(e){var t=e.charAt(2).toLowerCase()+e.slice(3);if(0!==t.indexOf("native:"))"Once"===t.substring(t.length-4)&&(t="~".concat(t.substring(0,t.length-4))),D[t]=u[e];else{var n="on".concat(t.charAt(7).toUpperCase()).concat(t.slice(8));R[n]=u[e]}}));var k=null;e.watch(j,(function(t){"function"==typeof k&&(k(),k=null),t||(k=e.watch((function(){return o.option}),(function(e,t){e&&(d.value?d.value.setOption(e,r({notMerge:e!==t},S.value)):I())}),{deep:!0}))}),{immediate:!0}),e.watch([C,z],(function(){N(),I()}),{deep:!0}),e.watchEffect((function(){o.group&&d.value&&(d.value.group=o.group)}));var M=i(d);return function(t,n,o){var i=e.inject(l,{}),a=e.computed((function(){return r(r({},c(i,{})),null==o?void 0:o.value)}));e.watchEffect((function(){var e=t.value;e&&(n.value?e.showLoading(a.value):e.hideLoading())}))}(d,L,T),function(r,o,i){var a=null;e.watch([i,r,o],(function(e,r,o){var i=e[0],u=e[1],s=e[2];if(i&&u&&s){var c=!0===s?{}:s,l=c.throttle,f=void 0===l?100:l,p=c.onResize,d=function(){u.resize(),null==p||p()};a=f?t.throttle(d,f):d,n.addListener(i,a)}o((function(){i&&a&&n.removeListener(i,a)}))}))}(d,w,p),e.onMounted((function(){I()})),e.onBeforeUnmount((function(){g&&f.value?f.value.__dispose=N:N()})),r({chart:d,root:f,inner:p,setOption:function(e,t){o.manualUpdate&&(v.value=e),d.value?d.value.setOption(e,t||{}):I(e)},nonEventAttrs:P,nativeListeners:R},M)},render:function(){var t=e.Vue2?{attrs:this.nonEventAttrs,on:this.nativeListeners}:r(r({},this.nonEventAttrs),this.nativeListeners);return t.ref="root",t.class=t.class?["echarts"].concat(t.class):"echarts",e.h(d,t,[e.h("div",{ref:"inner",class:"vue-echarts-inner"})])}});exports.INIT_OPTIONS_KEY=O,exports.LOADING_OPTIONS_KEY=l,exports.THEME_KEY=m,exports.UPDATE_OPTIONS_KEY=E,exports.default=b;
//# sourceMappingURL=index.cjs.min.js.map
