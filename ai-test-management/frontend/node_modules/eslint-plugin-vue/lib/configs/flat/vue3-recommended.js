/*
 * IMPORTANT!
 * This file has been automatically generated,
 * in order to update its content execute "npm run update"
 */
'use strict'
const config = require('./vue3-strongly-recommended.js')

module.exports = [
  ...config,
  {
    name: 'vue/recommended/rules',
    rules: {
      'vue/attributes-order': 'warn',
      'vue/component-tags-order': 'warn',
      'vue/no-lone-template': 'warn',
      'vue/no-multiple-slot-args': 'warn',
      'vue/no-v-html': 'warn',
      'vue/order-in-components': 'warn',
      'vue/this-in-template': 'warn'
    }
  }
]
