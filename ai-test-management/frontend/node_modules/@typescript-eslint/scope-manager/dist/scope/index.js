"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
__exportStar(require("./BlockScope"), exports);
__exportStar(require("./CatchScope"), exports);
__exportStar(require("./ClassFieldInitializerScope"), exports);
__exportStar(require("./ClassScope"), exports);
__exportStar(require("./ConditionalTypeScope"), exports);
__exportStar(require("./ForScope"), exports);
__exportStar(require("./FunctionExpressionNameScope"), exports);
__exportStar(require("./FunctionScope"), exports);
__exportStar(require("./FunctionTypeScope"), exports);
__exportStar(require("./GlobalScope"), exports);
__exportStar(require("./MappedTypeScope"), exports);
__exportStar(require("./ModuleScope"), exports);
__exportStar(require("./Scope"), exports);
__exportStar(require("./ScopeType"), exports);
__exportStar(require("./SwitchScope"), exports);
__exportStar(require("./TSEnumScope"), exports);
__exportStar(require("./TSModuleScope"), exports);
__exportStar(require("./TypeScope"), exports);
__exportStar(require("./WithScope"), exports);
//# sourceMappingURL=index.js.map