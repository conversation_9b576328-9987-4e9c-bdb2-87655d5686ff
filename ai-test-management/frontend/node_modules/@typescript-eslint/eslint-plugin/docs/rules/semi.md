---
description: 'Require or disallow semicolons instead of ASI.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/semi** for documentation.

This rule extends the base [`eslint/semi`](https://eslint.org/docs/rules/semi) rule.
It adds support for TypeScript features that require semicolons.

See also the [`@typescript-eslint/member-delimiter-style`](member-delimiter-style.md) rule, which allows you to specify the delimiter for `type` and `interface` members.
