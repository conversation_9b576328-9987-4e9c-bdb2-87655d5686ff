{"version": 3, "file": "no-confusing-non-null-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-confusing-non-null-assertion.ts"], "names": [], "mappings": ";;AACA,oDAA2E;AAC3E,wEAAsE;AAEtE,kCAAqC;AAErC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;YAClE,WAAW,EAAE,WAAW;SACzB;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,cAAc,EACZ,8HAA8H;YAChI,eAAe,EACb,4HAA4H;YAC9H,kBAAkB,EAAE,mDAAmD;YACvE,eAAe,EACb,6DAA6D;YAC/D,UAAU,EACR,6EAA6E;SAChF;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,OAAO;YACL,wCAAwC,CACtC,IAA+D;gBAE/D,SAAS,2BAA2B,CAClC,IAAsD;oBAEtD,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBAC1D,CAAC;gBAED,IACE,IAAI,CAAC,QAAQ,KAAK,IAAI;oBACtB,IAAI,CAAC,QAAQ,KAAK,KAAK;oBACvB,IAAI,CAAC,QAAQ,KAAK,GAAG,EACrB,CAAC;oBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;oBACvC,MAAM,kBAAkB,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC9D,MAAM,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3D,IACE,kBAAkB,EAAE,IAAI,KAAK,uBAAe,CAAC,UAAU;wBACvD,kBAAkB,CAAC,KAAK,KAAK,GAAG;wBAChC,cAAc,EAAE,KAAK,KAAK,GAAG,EAC7B,CAAC;wBACD,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;4BAC3C,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI;gCACJ,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB;gCAC1D,OAAO,EAAE;oCACP;wCACE,SAAS,EAAE,QAAQ;4CACjB,CAAC,CAAC,iBAAiB;4CACnB,CAAC,CAAC,oBAAoB;wCACxB,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE,CAAC;4CAClC,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC;yCACjC;qCACF;iCACF;6BACF,CAAC,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACN,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI;gCACJ,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB;gCAC1D,OAAO,EAAE;oCACP;wCACE,SAAS,EAAE,YAAY;wCACvB,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE,CAAC;4CAClC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;4CACtC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;yCACtC;qCACF;iCACF;6BACF,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}