{"version": 3, "file": "no-require-imports.js", "sourceRoot": "", "sources": ["../../src/rules/no-require-imports.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAAoE;AACpE,wEAAiE;AAEjE,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,oCAAoC;SAClD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACzB,WAAW,EAAE,mDAAmD;qBACjE;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,gBAAgB,EAAE,0CAA0C;SAC7D;KACF;IACD,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAC/B,MAAM,CAAC,OAAO,EAAE,OAAO;QACrB,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CACxC,OAAO,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CACpC,CAAC;QACF,SAAS,mBAAmB,CAAC,UAAkB;YAC7C,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAClE,CAAC;QACD,OAAO;YACL,uCAAuC,CACrC,IAA6B;gBAE7B,IACE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAClD,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ;oBAC3C,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAC5C,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,MAAM,QAAQ,GAAG,gBAAQ,CAAC,YAAY,CAAC,IAAA,uBAAQ,EAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC;gBAErE,6EAA6E;gBAC7E,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;oBAClC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,kBAAkB;qBAC9B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,yBAAyB,CAAC,IAAI;gBAC5B,IACE,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAC/C,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,QAAQ;oBACzC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAC1C,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,kBAAkB;iBAC9B,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}