{"version": 3, "file": "no-redeclare.js", "sourceRoot": "", "sources": ["../../src/rules/no-redeclare.ts"], "names": [], "mappings": ";;AAAA,oEAA6D;AAE7D,oDAA0D;AAC1D,wEAAgF;AAEhF,kCAA8E;AAU9E,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,iCAAiC;YAC9C,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,cAAc,EAAE;wBACd,IAAI,EAAE,SAAS;qBAChB;oBACD,sBAAsB,EAAE;wBACtB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,8BAA8B;YAC1C,mBAAmB,EACjB,4DAA4D;YAC9D,kBAAkB,EAChB,wDAAwD;SAC3D;KACF;IACD,cAAc,EAAE;QACd;YACE,cAAc,EAAE,IAAI;YACpB,sBAAsB,EAAE,IAAI;SAC7B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C,MAAM,6BAA6B,GAAG,IAAI,GAAG,CAAiB;YAC5D,sBAAc,CAAC,sBAAsB;YACrC,sBAAc,CAAC,mBAAmB;YAClC,sBAAc,CAAC,gBAAgB;SAChC,CAAC,CAAC;QACH,MAAM,gCAAgC,GAAG,IAAI,GAAG,CAAiB;YAC/D,sBAAc,CAAC,mBAAmB;YAClC,sBAAc,CAAC,mBAAmB;SACnC,CAAC,CAAC;QACH,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAAiB;YAC3D,sBAAc,CAAC,iBAAiB;YAChC,sBAAc,CAAC,mBAAmB;SACnC,CAAC,CAAC;QAEH,QAAQ,CAAC,CAAC,mBAAmB,CAAC,QAAiC;YAQ7D,IACE,OAAO,CAAC,cAAc;gBACtB,6BAA6B,IAAI,QAAQ;gBACzC,CAAC,QAAQ,CAAC,2BAA2B,KAAK,UAAU;oBAClD,QAAQ,CAAC,2BAA2B,KAAK,UAAU,CAAC,EACtD,CAAC;gBACD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YAC5B,CAAC;YAED,IACE,8BAA8B,IAAI,QAAQ;gBAC1C,QAAQ,CAAC,4BAA4B,EACrC,CAAC;gBACD,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,4BAA4B,EAAE,CAAC;oBAC5D,MAAM;wBACJ,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,OAAO;wBACb,GAAG,EAAE,IAAA,8CAAuC,EAC1C,UAAU,EACV,OAAO,EACP,QAAQ,CAAC,IAAI,CACd;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW;iBACrC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACV,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,EAAE,CAAC,MAAM;aAClB,CAAC,CAAC;gBACH,yEAAyE;iBACxE,MAAM,CACL,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,CACjE,CAAC;YAEJ,IAAI,OAAO,CAAC,sBAAsB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7D;gBACE,qBAAqB;gBACrB,WAAW,CAAC,KAAK,CACf,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CACb,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,CACxD,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED;gBACE,2BAA2B;gBAC3B,WAAW,CAAC,KAAK,CACf,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CACnE,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED;gBACE,sCAAsC;gBACtC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAC/B,6BAA6B,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAC/C,EACD,CAAC;oBACD,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CACnC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAChE,CAAC;oBACF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC5B,2BAA2B;wBAC3B,OAAO;oBACT,CAAC;oBAED,sEAAsE;oBACtE,KAAK,MAAM,EAAE,UAAU,EAAE,IAAI,UAAU,EAAE,CAAC;wBACxC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC;oBAClE,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED;gBACE,sCAAsC;gBACtC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAC/B,gCAAgC,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAClD,EACD,CAAC;oBACD,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CACtC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CACnE,CAAC;oBACF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC/B,2BAA2B;wBAC3B,OAAO;oBACT,CAAC;oBAED,yEAAyE;oBACzE,KAAK,MAAM,EAAE,UAAU,EAAE,IAAI,aAAa,EAAE,CAAC;wBAC3C,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC;oBAClE,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED;gBACE,2BAA2B;gBAC3B,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAC/B,4BAA4B,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,EACD,CAAC;oBACD,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAClC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,CACjE,CAAC;oBACF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC3B,2BAA2B;wBAC3B,OAAO;oBACT,CAAC;oBAED,qEAAqE;oBACrE,KAAK,MAAM,EAAE,UAAU,EAAE,IAAI,SAAS,EAAE,CAAC;wBACvC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC;oBAClE,CAAC;oBACD,OAAO;gBACT,CAAC;YACH,CAAC;YAED,KAAK,MAAM,EAAE,UAAU,EAAE,IAAI,WAAW,EAAE,CAAC;gBACzC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC;YAClE,CAAC;QACH,CAAC;QAED,SAAS,oBAAoB,CAAC,KAA2B;YACvD,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACvC,MAAM,CAAC,WAAW,EAAE,GAAG,iBAAiB,CAAC,GACvC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAEhC,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,SAAS;gBACX,CAAC;gBAED;;;;mBAIG;gBACH,MAAM,eAAe,GACnB,WAAW,CAAC,IAAI,KAAK,SAAS;oBAC5B,CAAC,CAAC,qBAAqB;oBACvB,CAAC,CAAC,oBAAoB,CAAC;gBAC3B,MAAM,IAAI,GAAG,EAAE,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAEnC,6BAA6B;gBAC7B,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,iBAAiB,EAAE,CAAC;oBACpD,MAAM,SAAS,GACb,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC;oBAE7D,IAAI,IAAI,EAAE,CAAC;wBACT,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBACjD,CAAC;yBAAM,IAAI,GAAG,EAAE,CAAC;wBACf,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED;;WAEG;QACH,SAAS,aAAa,CAAC,IAAmB;YACxC,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC;YAEhC;;;eAGG;YACH,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBACzB,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO;gBACL,MAAM,KAAK,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC;gBAEhC,oBAAoB,CAAC,KAAK,CAAC,CAAC;gBAE5B,6CAA6C;gBAC7C,IACE,KAAK,CAAC,IAAI,KAAK,yBAAS,CAAC,MAAM;oBAC/B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBACpB,iDAAiD;oBACjD,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAC1C,CAAC;oBACD,oBAAoB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;YAED,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YAEtC,cAAc,EAAE,aAAa;YAC7B,YAAY,EAAE,aAAa;YAC3B,cAAc,EAAE,aAAa;YAC7B,cAAc,EAAE,aAAa;YAC7B,eAAe,EAAE,aAAa;SAC/B,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}