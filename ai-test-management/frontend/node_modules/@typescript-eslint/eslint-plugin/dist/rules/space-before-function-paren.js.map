{"version": 3, "file": "space-before-function-paren.js", "sourceRoot": "", "sources": ["../../src/rules/space-before-function-paren.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAA0D;AAe1D,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,6BAA6B;IACnC,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,2CAA2C,CAAC;QACzD,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,wDAAwD;YACrE,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,SAAS,EAAE;gCACT,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;6BACpC;4BACD,KAAK,EAAE;gCACL,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;6BACpC;4BACD,UAAU,EAAE;gCACV,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;6BACpC;yBACF;wBACD,oBAAoB,EAAE,KAAK;qBAC5B;iBACF;aACF;SACF;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,+CAA+C;YAC3D,OAAO,EAAE,4CAA4C;SACtD;KACF;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAE1B,MAAM,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC;QAC3B,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC5E,MAAM,cAAc,GAAG,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;QAE1E;;;;WAIG;QACH,SAAS,eAAe,CACtB,IAK0C;YAE1C,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B;gBACzD,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;oBACtC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CACrE,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,oBAAoB,CAC3B,IAK0C;YAE1C,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE,CAAC;gBACzD,8FAA8F;gBAC9F,IACE,IAAI,CAAC,KAAK;oBACV,IAAA,0BAAmB,EAAC,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,CAAE,CAAC,EACjE,CAAC;oBACD,OAAO,cAAc,CAAC,UAAU,IAAI,UAAU,CAAC;gBACjD,CAAC;YACH,CAAC;iBAAM,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,OAAO,cAAc,CAAC,KAAK,IAAI,UAAU,CAAC;gBAE1C,oFAAoF;YACtF,CAAC;iBAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC3B,OAAO,cAAc,CAAC,SAAS,IAAI,UAAU,CAAC;YAChD,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED;;;WAGG;QACH,SAAS,aAAa,CACpB,IAK0C;YAE1C,MAAM,cAAc,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAElD,IAAI,cAAc,KAAK,QAAQ,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,IAAI,SAAyB,CAAC;YAC9B,IAAI,UAA0B,CAAC;YAC/B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAE,CAAC;gBAC1D,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAE,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,0BAAmB,CAAE,CAAC;gBAClE,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC;YACrD,CAAC;YAED,yGAAyG;YACzG,MAAM,UAAU,GAAG,UAAU,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAE1E,IAAI,UAAU,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;gBAC7C,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE;wBACH,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG;wBACxB,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK;qBAC1B;oBACD,SAAS,EAAE,YAAY;oBACvB,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,KAAK,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC/D,CAAC,CAAC;YACL,CAAC;iBAAM,IACL,CAAC,UAAU;gBACX,cAAc,KAAK,QAAQ;gBAC3B,CAAC,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,EAAE,CAAC,EACjC,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE,UAAU,CAAC,GAAG;oBACnB,SAAS,EAAE,SAAS;oBACpB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,GAAG,CAAC;iBACpD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,uBAAuB,EAAE,aAAa;YACtC,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,6BAA6B,EAAE,aAAa;YAC5C,iBAAiB,EAAE,aAAa;SACjC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}