{"version": 3, "file": "no-unsafe-return.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-return.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAaiB;AAEjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;YACzE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,YAAY,EAAE,6CAA6C;YAC3D,gBAAgB,EAAE;gBAChB,uEAAuE;gBACvE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,sBAAsB,EACpB,mFAAmF;SACtF;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,SAAS,qBAAqB,CAC5B,IAAmB;YAMnB,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,OAAO,OAAO,EAAE,CAAC;gBACf,IACE,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;oBACvD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;oBACnD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAClD,CAAC;oBACD,OAAO,OAAO,CAAC;gBACjB,CAAC;gBAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,mFAAmF;YACnF,qDAAqD;YACrD,0BAA0B,CAAC,OAAO,IAAI,CAAC;QACzC,CAAC;QAED,SAAS,WAAW,CAClB,UAAyB,EACzB,gBAA+B,UAAU;YAEzC,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAG,IAAA,uCAAgC,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAClE,MAAM,YAAY,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;YACvD,wBAAwB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC3C,OAAO;YACT,CAAC;YAED,qEAAqE;YACrE,MAAM,cAAc,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC1E,MAAM,cAAc,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAExE,yFAAyF;YACzF,+DAA+D;YAC/D,wDAAwD;YACxD,qGAAqG;YACrG,IAAI,YAAY,GACd,EAAE,CAAC,oBAAoB,CAAC,cAAc,CAAC;gBACvC,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC;gBAChC,CAAC,CAAC,IAAA,wBAAiB,EAAC,OAAO,EAAE,cAAc,CAAC;gBAC5C,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC/C,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC1D,CAAC;YAED,6EAA6E;YAC7E,iFAAiF;YACjF,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;gBACxB,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;oBACzD,IACE,cAAc,KAAK,SAAS,CAAC,aAAa,EAAE;wBAC5C,IAAA,oBAAa,EACX,SAAS,CAAC,aAAa,EAAE,EACzB,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CACxC,EACD,CAAC;wBACD,OAAO;oBACT,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,OAAO,KAAK,cAAO,CAAC,IAAI,EAAE,CAAC;gBAC7B,2FAA2F;gBAC3F,8CAA8C;gBAC9C,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;oBACzD,MAAM,kBAAkB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;oBACrD,IACE,OAAO,KAAK,cAAO,CAAC,GAAG;wBACvB,IAAA,wBAAiB,EAAC,kBAAkB,CAAC,EACrC,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,IACE,OAAO,KAAK,cAAO,CAAC,QAAQ;wBAC5B,IAAA,6BAAsB,EAAC,kBAAkB,EAAE,OAAO,CAAC,EACnD,CAAC;wBACD,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,IAAI,SAAS,GAAwC,cAAc,CAAC;gBAEpE,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,gBAAgB;oBAChB,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,UAAU,CAAC,CAAC;oBACrD,IACE,cAAc;wBACd,IAAA,oBAAa,EACX,IAAA,mCAA4B,EAAC,QAAQ,EAAE,cAAc,CAAC,CACvD,EACD,CAAC;wBACD,SAAS,GAAG,kBAAkB,CAAC;oBACjC,CAAC;gBACH,CAAC;gBAED,qFAAqF;gBACrF,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,SAAS;oBACT,IAAI,EAAE;wBACJ,IAAI,EAAE,OAAO,KAAK,cAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO;qBAChD;iBACF,CAAC,CAAC;YACL,CAAC;YAED,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBACzD,MAAM,kBAAkB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;gBACrD,MAAM,MAAM,GAAG,IAAA,yBAAkB,EAC/B,cAAc,EACd,kBAAkB,EAClB,OAAO,EACP,UAAU,CACX,CAAC;gBACF,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO;gBACT,CAAC;gBAED,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;gBACpC,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,wBAAwB;oBACnC,IAAI,EAAE;wBACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;wBACpC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC;qBACzC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;gBACT,CAAC;gBAED,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC;YACD,qDAAqD,EAAE,WAAW;SACnE,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}