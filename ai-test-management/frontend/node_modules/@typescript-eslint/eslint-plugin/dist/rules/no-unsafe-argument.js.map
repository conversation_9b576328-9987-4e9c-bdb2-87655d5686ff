{"version": 3, "file": "no-unsafe-argument.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-argument.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,kCAMiB;AA8BjB,MAAM,iBAAiB;IAGd,MAAM,CAAC,MAAM,CAClB,OAAuB,EACvB,MAA6B;QAE7B,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAc,EAAE,CAAC;QACjC,IAAI,QAAQ,GAAoB,IAAI,CAAC;QAErC,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE9D,MAAM,IAAI,GAAG,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxD,kBAAkB;gBAClB,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9B,QAAQ,GAAG;wBACT,IAAI,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACvC,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;gBACJ,CAAC;qBAAM,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,QAAQ,GAAG;wBACT,aAAa,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC;wBAC7C,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG;wBACT,IAAI;wBACJ,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,CAAC;YAED,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAID,YACU,UAAqB,EACrB,QAAyB;QADzB,eAAU,GAAV,UAAU,CAAW;QACrB,aAAQ,GAAR,QAAQ,CAAiB;QAtD3B,uBAAkB,GAAG,CAAC,CAAC;QAkDvB,yBAAoB,GAAG,KAAK,CAAC;IAKlC,CAAC;IAEG,oBAAoB;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACtC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAE7B,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACjE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC3B,+BAAuB,CAAC,CAAC,CAAC;oBACxB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAClD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC9B,gEAAgE;wBAChE,uEAAuE;wBACvE,6CAA6C;wBAC7C,wDAAwD;wBACxD,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACjD,CAAC;oBAED,MAAM,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAC9C,IAAI,SAAS,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;wBACtC,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACjD,CAAC;oBAED,OAAO,aAAa,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC;gBAED,gCAAwB;gBACxB;oBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC9B,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAEM,yBAAyB;QAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACnC,CAAC;CACF;AAED,kBAAe,IAAA,iBAAU,EAAiB;IACxC,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,cAAc,EACZ,sFAAsF;YACxF,iBAAiB,EACf,4HAA4H;YAC9H,iBAAiB,EAAE,uCAAuC;YAC1D,YAAY,EAAE,iCAAiC;SAChD;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,OAAO;YACL,+BAA+B,CAC7B,IAAsD;gBAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,OAAO;gBACT,CAAC;gBAED,+DAA+D;gBAC/D,IAAI,IAAA,oBAAa,EAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;oBAC3D,OAAO;gBACT,CAAC;gBAED,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACxD,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC5D,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,OAAO;gBACT,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACtC,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;wBACtB,kBAAkB;wBAClB,KAAK,sBAAc,CAAC,aAAa,CAAC,CAAC,CAAC;4BAClC,MAAM,aAAa,GAAG,QAAQ,CAAC,iBAAiB,CAC9C,QAAQ,CAAC,QAAQ,CAClB,CAAC;4BAEF,IAAI,IAAA,oBAAa,EAAC,aAAa,CAAC,EAAE,CAAC;gCACjC,cAAc;gCACd,OAAO,CAAC,MAAM,CAAC;oCACb,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,cAAc;iCAC1B,CAAC,CAAC;4BACL,CAAC;iCAAM,IAAI,IAAA,yBAAkB,EAAC,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC;gCACtD,gBAAgB;gCAEhB,yFAAyF;gCACzF,OAAO,CAAC,MAAM,CAAC;oCACb,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,mBAAmB;iCAC/B,CAAC,CAAC;4BACL,CAAC;iCAAM,IAAI,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;gCAC9C,2BAA2B;gCAC3B,MAAM,mBAAmB,GACvB,OAAO,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gCAC1C,KAAK,MAAM,SAAS,IAAI,mBAAmB,EAAE,CAAC;oCAC5C,MAAM,aAAa,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;oCACvD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;wCAC1B,SAAS;oCACX,CAAC;oCACD,MAAM,MAAM,GAAG,IAAA,yBAAkB,EAC/B,SAAS,EACT,aAAa,EACb,OAAO;oCACP,mGAAmG;oCACnG,qBAAqB;oCACrB,IAAI,CACL,CAAC;oCACF,IAAI,MAAM,EAAE,CAAC;wCACX,OAAO,CAAC,MAAM,CAAC;4CACb,IAAI,EAAE,QAAQ;4CACd,SAAS,EAAE,mBAAmB;4CAC9B,IAAI,EAAE;gDACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC;gDACvC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;6CAC9C;yCACF,CAAC,CAAC;oCACL,CAAC;gCACH,CAAC;gCACD,IAAI,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;oCACxC,gGAAgG;oCAChG,mFAAmF;oCACnF,SAAS,CAAC,yBAAyB,EAAE,CAAC;gCACxC,CAAC;4BACH,CAAC;iCAAM,CAAC;gCACN,4BAA4B;gCAC5B,iEAAiE;gCACjE,sCAAsC;4BACxC,CAAC;4BACD,MAAM;wBACR,CAAC;wBAED,OAAO,CAAC,CAAC,CAAC;4BACR,MAAM,aAAa,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;4BACvD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;gCAC1B,SAAS;4BACX,CAAC;4BAED,MAAM,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;4BAC1D,MAAM,MAAM,GAAG,IAAA,yBAAkB,EAC/B,YAAY,EACZ,aAAa,EACb,OAAO,EACP,QAAQ,CACT,CAAC;4BACF,IAAI,MAAM,EAAE,CAAC;gCACX,OAAO,CAAC,MAAM,CAAC;oCACb,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,gBAAgB;oCAC3B,IAAI,EAAE;wCACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;wCAC1C,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;qCAC9C;iCACF,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}