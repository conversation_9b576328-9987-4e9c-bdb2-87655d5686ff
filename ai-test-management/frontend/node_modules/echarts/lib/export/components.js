
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */

/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/
export { install as GridSimpleComponent } from '../component/grid/installSimple.js';
export { install as GridComponent } from '../component/grid/install.js';
export { install as PolarComponent } from '../component/polar/install.js';
export { install as RadarComponent } from '../component/radar/install.js';
export { install as GeoComponent } from '../component/geo/install.js';
export { install as SingleAxisComponent } from '../component/singleAxis/install.js';
export { install as ParallelComponent } from '../component/parallel/install.js';
export { install as CalendarComponent } from '../component/calendar/install.js';
export { install as GraphicComponent } from '../component/graphic/install.js';
export { install as ToolboxComponent } from '../component/toolbox/install.js';
export { install as TooltipComponent } from '../component/tooltip/install.js';
export { install as AxisPointerComponent } from '../component/axisPointer/install.js';
export { install as BrushComponent } from '../component/brush/install.js';
export { install as TitleComponent } from '../component/title/install.js';
export { install as TimelineComponent } from '../component/timeline/install.js';
export { install as MarkPointComponent } from '../component/marker/installMarkPoint.js';
export { install as MarkLineComponent } from '../component/marker/installMarkLine.js';
export { install as MarkAreaComponent } from '../component/marker/installMarkArea.js';
export { install as LegendComponent } from '../component/legend/install.js';
export { install as LegendScrollComponent } from '../component/legend/installLegendScroll.js';
export { install as LegendPlainComponent } from '../component/legend/installLegendPlain.js';
export { install as DataZoomComponent } from '../component/dataZoom/install.js';
export { install as DataZoomInsideComponent } from '../component/dataZoom/installDataZoomInside.js';
export { install as DataZoomSliderComponent } from '../component/dataZoom/installDataZoomSlider.js';
export { install as VisualMapComponent } from '../component/visualMap/install.js';
export { install as VisualMapContinuousComponent } from '../component/visualMap/installVisualMapContinuous.js';
export { install as VisualMapPiecewiseComponent } from '../component/visualMap/installVisualMapPiecewise.js';
export { install as AriaComponent } from '../component/aria/install.js';
export { install as TransformComponent } from '../component/transform/install.js';
export { install as DatasetComponent } from '../component/dataset/install.js';