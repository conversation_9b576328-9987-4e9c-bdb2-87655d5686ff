{"name": "scule", "version": "1.3.0", "description": "String case utils", "repository": "unjs/scule", "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest dev --typecheck", "lint": "eslint --cache --ext .ts,.js,.mjs,.cjs . && prettier -c src test", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src test -w", "prepack": "pnpm run build", "release": "pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --typecheck --coverage"}, "devDependencies": {"@types/node": "^20.11.3", "@vitest/coverage-v8": "^1.2.0", "changelogen": "^0.5.5", "eslint": "^8.56.0", "eslint-config-unjs": "^0.2.1", "prettier": "^3.2.2", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vitest": "^1.2.0"}, "packageManager": "pnpm@8.14.1"}