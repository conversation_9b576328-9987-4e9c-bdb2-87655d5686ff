{"version": 3, "file": "Referencer.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint-scope/Referencer.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,KAAK,EACV,sBAAsB,EACtB,qBAAqB,EACrB,OAAO,EACR,MAAM,WAAW,CAAC;AACnB,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACrC,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAEnD,UAAU,UAAU,CAAC,EAAE,SAAS,YAAY,CAAE,SAAQ,OAAO;IAC3D,uBAAuB,EAAE,OAAO,CAAC;IACjC,OAAO,EAAE,GAAG,CAAC;IACb,YAAY,EAAE,EAAE,CAAC;IACjB,MAAM,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC;IAEvB,YAAY,IAAI,KAAK,CAAC;IACtB,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACjC,yBAAyB,CAAC,uBAAuB,EAAE,OAAO,GAAG,OAAO,CAAC;IACrE,wBAAwB,CAAC,uBAAuB,EAAE,OAAO,GAAG,IAAI,CAAC;IAEjE,uBAAuB,CACrB,OAAO,EAAE,GAAG,EACZ,WAAW,EAAE,GAAG,EAChB,mBAAmB,EAAE,GAAG,EACxB,IAAI,EAAE,OAAO,GACZ,IAAI,CAAC;IACR,YAAY,CACV,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,OAAO,EAAE,qBAAqB,EAC9B,QAAQ,EAAE,sBAAsB,GAC/B,IAAI,CAAC;IACR,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACzC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACtC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACzC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACtC,wBAAwB,CACtB,mBAAmB,EAAE,GAAG,EACxB,IAAI,EAAE,GAAG,EACT,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,KAAK,EAAE,GAAG,GACT,IAAI,CAAC;IAER,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAChD,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACvC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;IACtC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;IAC5C,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC5C,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC5C,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACpC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC5C,cAAc,IAAI,IAAI,CAAC;IACvB,iBAAiB,IAAI,IAAI,CAAC;IAC1B,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC5C,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACxC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC3C,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC5C,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC1C,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC1C,cAAc,IAAI,IAAI,CAAC;IACvB,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACzC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC/C,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC3C,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC/C,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC9C,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC1C,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC1C,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACnD,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC7C,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAClD,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC7C,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAClD,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IAC3C,YAAY,IAAI,IAAI,CAAC;CACtB;AACD,QAAA,MAAM,UAAU,yCACyB,GAAG,qCAC3C,CAAC;AAEF,OAAO,EAAE,UAAU,EAAE,CAAC"}