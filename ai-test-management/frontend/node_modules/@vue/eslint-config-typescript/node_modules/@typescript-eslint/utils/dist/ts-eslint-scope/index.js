"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.version = void 0;
const eslint_scope_1 = require("eslint-scope");
__exportStar(require("./analyze"), exports);
__exportStar(require("./Definition"), exports);
__exportStar(require("./Options"), exports);
__exportStar(require("./PatternVisitor"), exports);
__exportStar(require("./Reference"), exports);
__exportStar(require("./Referencer"), exports);
__exportStar(require("./Scope"), exports);
__exportStar(require("./ScopeManager"), exports);
__exportStar(require("./Variable"), exports);
exports.version = eslint_scope_1.version;
//# sourceMappingURL=index.js.map