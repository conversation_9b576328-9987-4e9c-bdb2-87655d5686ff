{"version": 3, "file": "deepMerge.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/deepMerge.ts"], "names": [], "mappings": ";;;AAEA;;;;GAIG;AACH,SAAS,gBAAgB,CACvB,GAAwB;IAExB,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACxD,CAAC;AAwCQ,4CAAgB;AAtCzB;;;;;;GAMG;AACH,SAAgB,SAAS,CACvB,QAAoB,EAAE,EACtB,SAAqB,EAAE;IAEvB,iDAAiD;IACjD,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAErE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAa,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,MAAM,WAAW,GAAG,GAAG,IAAI,KAAK,CAAC;QACjC,MAAM,YAAY,GAAG,GAAG,IAAI,MAAM,CAAC;QACnC,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAEhC,IAAI,WAAW,IAAI,YAAY,EAAE;YAC/B,IAAI,gBAAgB,CAAC,UAAU,CAAC,IAAI,gBAAgB,CAAC,WAAW,CAAC,EAAE;gBACjE,cAAc;gBACd,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;aAC/C;iBAAM;gBACL,aAAa;gBACb,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;aACxB;SACF;aAAM,IAAI,WAAW,EAAE;YACtB,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;SACvB;aAAM;YACL,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;SACxB;QAED,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AA7BD,8BA6BC"}