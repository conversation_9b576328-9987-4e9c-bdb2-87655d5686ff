{"version": 3, "file": "return-await.js", "sourceRoot": "", "sources": ["../../src/rules/return-await.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,qCAA6C;AAC7C,+CAAiC;AAEjC,8CAAgC;AAChC,yEAAsE;AAYtE,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,gDAAgD;YAC7D,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,iBAAiB;SACnC;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE;YACR,eAAe,EACb,kEAAkE;YACpE,sBAAsB,EACpB,8DAA8D;YAChE,oBAAoB,EAClB,2DAA2D;SAC9D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,CAAC,cAAc,EAAE,QAAQ,EAAE,OAAO,CAAC;aAC1C;SACF;KACF;IACD,cAAc,EAAE,CAAC,cAAc,CAAC;IAEhC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,MAAM,cAAc,GAAgB,EAAE,CAAC;QAEvC,SAAS,aAAa,CAAC,IAAkB;YACvC,cAAc,CAAC,IAAI,CAAC;gBAClB,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;QACL,CAAC;QAED,SAAS,YAAY;YACnB,cAAc,CAAC,GAAG,EAAE,CAAC;QACvB,CAAC;QAED,SAAS,KAAK,CAAC,IAAa;YAC1B,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC/C,IAAI,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;oBAC/B,OAAO,IAAI,CAAC;iBACb;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,OAAO,CAAC,IAAa;YAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC/C,IAAI,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;oBAC9B,OAAO,IAAI,CAAC;iBACb;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAa;YAC7C,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC/C,IACE,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAClC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;oBACpB,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,EACpC;oBACA,OAAO,IAAI,CAAC;iBACb;gBACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,IAAa;YACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC/C,IAAI,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;oBAC/B,OAAO,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC;iBAChC;gBACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;aAC5B;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gCAAgC;QAEhC,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB;YAEzB,qDAAqD;YACrD,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;gBAC1D,OAAO,IAAI,CAAC;aACb;YAED,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACvE,gDAAgD;YAChD,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE;gBACxC,OAAO,IAAI,CAAC;aACb;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,wEAAwE;YACxE,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,EAAE;gBACrD,eAAe,EAAE,IAAI;aACtB,CAAC,CAAC;YACH,IAAI,SAAS,EAAE;gBACb,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB,EACzB,iBAA0B;YAE1B,IAAI,iBAAiB,EAAE;gBACrB,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aAC/C;iBAAM;gBACL,OAAO;oBACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;oBACvC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC;iBACjC,CAAC;aACH;QACH,CAAC;QAED,SAAS,2BAA2B,CAAC,IAAa;YAChD,MAAM,QAAQ,GAAG,IAAA,4BAAkB,EAAC,IAAI,CAAC;gBACvC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;gBACzB,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1B,MAAM,cAAc,GAAG,IAAA,6CAAqB,EAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,eAAe,GAAG,IAAA,6CAAqB,EAC3C,EAAE,CAAC,UAAU,CAAC,eAAe,EAC7B,EAAE,CAAC,UAAU,CAAC,OAAO,CACtB,CAAC;YACF,OAAO,cAAc,GAAG,eAAe,CAAC;QAC1C,CAAC;QAED,SAAS,IAAI,CAAC,IAAyB,EAAE,UAAmB;YAC1D,IAAI,KAAc,CAAC;YAEnB,MAAM,OAAO,GAAG,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEjD,IAAI,OAAO,EAAE;gBACX,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aAClC;iBAAM;gBACL,KAAK,GAAG,UAAU,CAAC;aACpB;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAErE,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE;gBAC3B,OAAO;aACR;YAED,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;gBAC1B,iDAAiD;gBACjD,MAAM,UAAU,GAAG,CAAC,CAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CACzD,CAAC;gBACF,MAAM,GAAG,GAAG,CAAC,KAAyB,EAA2B,EAAE,CACjE,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAE3B,OAAO,CAAC,MAAM,iBACZ,SAAS,EAAE,iBAAiB,EAC5B,IAAI,IACD,CAAC,UAAU;oBACZ,CAAC,CAAC,EAAE,GAAG,EAAE;oBACT,CAAC,CAAC;wBACE,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,iBAAiB;gCAC5B,GAAG;6BACJ;yBACF;qBACF,CAAC,EACN,CAAC;gBACH,OAAO;aACR;YAED,IAAI,MAAM,KAAK,QAAQ,EAAE;gBACvB,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE;oBAC1B,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,sBAAsB;wBACjC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,2BAA2B,CAAC,UAAU,CAAC,CAAC;qBACpE,CAAC,CAAC;iBACJ;gBAED,OAAO;aACR;YAED,IAAI,MAAM,KAAK,OAAO,EAAE;gBACtB,IAAI,OAAO,EAAE;oBACX,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,wBAAwB;wBACnC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;iBACJ;gBAED,OAAO;aACR;YAED,IAAI,MAAM,KAAK,cAAc,EAAE;gBAC7B,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC9D,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;oBAC5B,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,wBAAwB;wBACnC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;qBACvC,CAAC,CAAC;iBACJ;qBAAM,IAAI,CAAC,OAAO,IAAI,YAAY,EAAE;oBACnC,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;wBACvD,OAAO;qBACR;oBAED,IAAI,wBAAwB,CAAC,UAAU,CAAC,EAAE;wBACxC,OAAO;qBACR;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,sBAAsB;wBACjC,IAAI;wBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,2BAA2B,CAAC,UAAU,CAAC,CAAC;qBACpE,CAAC,CAAC;iBACJ;gBAED,OAAO;aACR;QACH,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAyB;YAEzB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE;gBACtD,OAAO;oBACL,GAAG,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC;oBAC5C,GAAG,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC9C,CAAC;aACH;YACD,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YAEtC,0BAA0B,EAAE,YAAY;YACxC,yBAAyB,EAAE,YAAY;YACvC,8BAA8B,EAAE,YAAY;YAE5C,kEAAkE;YAClE,4CAA4C,CAC1C,IAAsC;gBAEtC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;oBACpD,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBAClD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC9D,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,MAAM,SAAS,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC5D,IAAI,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,QAAQ,CAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAC1C,OAAO;iBACR;gBACD,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACtD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC9D,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}