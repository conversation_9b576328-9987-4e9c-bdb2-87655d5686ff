{"version": 3, "file": "comma-dangle.js", "sourceRoot": "", "sources": ["../../src/rules/comma-dangle.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,cAAc,CAAC,CAAC;AAUnD,MAAM,mBAAmB,GAAG;IAC1B,kBAAkB;IAClB,QAAQ;IACR,OAAO;IACP,gBAAgB;CACjB,CAAC;AAEF,MAAM,oBAAoB,GAAG,OAAO,CAAC;AAErC,SAAS,gBAAgB,CAAC,OAAe;;IACvC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO;YACL,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,OAAO;YACjB,MAAM,EAAE,OAAO;SAChB,CAAC;KACH;IACD,OAAO;QACL,KAAK,EAAE,MAAA,OAAO,CAAC,KAAK,mCAAI,oBAAoB;QAC5C,QAAQ,EAAE,MAAA,OAAO,CAAC,QAAQ,mCAAI,oBAAoB;QAClD,MAAM,EAAE,MAAA,OAAO,CAAC,MAAM,mCAAI,oBAAoB;KAC/C,CAAC;AACJ,CAAC;AAED,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,qCAAqC;YAClD,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE;YACN,KAAK,EAAE;gBACL,KAAK,EAAE;oBACL,IAAI,EAAE,mBAAmB;iBAC1B;gBACD,eAAe,EAAE;oBACf,IAAI,EAAE,CAAC,GAAG,mBAAmB,EAAE,QAAQ,CAAC;iBACzC;aACF;YACD,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL;oBACE,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,eAAe;yBACtB;wBACD;4BACE,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,MAAM,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC3C,OAAO,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC5C,OAAO,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC5C,OAAO,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC5C,SAAS,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC9C,KAAK,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC1C,QAAQ,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC7C,MAAM,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;6BAC5C;4BACD,oBAAoB,EAAE,KAAK;yBAC5B;qBACF;iBACF;aACF;YACD,oBAAoB,EAAE,KAAK;SAC5B;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,OAAO,CAAC;IACzB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEpD,MAAM,SAAS,GAAG;YAChB,MAAM,EAAE,UAAU;YAClB,kBAAkB,EAAE,qBAAqB;YACzC,gBAAgB,EAAE,qBAAqB;YACvC,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,GAAS,EAAE,GAAE,CAAC;SACvB,CAAC;QAEF,SAAS,IAAI,CAAC,KAAsB;;YAClC,OAAO,MAAA,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,mCAAI,IAAI,CAAC;QACzC,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,sBAAc,CAAC,iBAAiB;oBACnC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,KAAK,sBAAc,CAAC,0BAA0B;oBAC5C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,KAAK,sBAAc,CAAC,WAAW;oBAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjC;oBACE,OAAO,IAAI,CAAC;aACf;QACH,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAmB;YAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,IAAI,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACxD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAChD,OAAO,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,CAAC,GAAG,CAAC,IAAI,OAAK,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAA,CAAC;QACxD,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;gBACnD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,YAAY;oBACvB,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAChC,CAAC;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,UAAU,CAAC,IAAmB;YACrC,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;gBACpD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,SAAS;oBACpB,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBAC1C,CAAC;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAmB;YAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;gBACtB,WAAW,CAAC,IAAI,CAAC,CAAC;aACnB;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAmB;YAChD,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;gBACrB,UAAU,CAAC,IAAI,CAAC,CAAC;aAClB;iBAAM;gBACL,WAAW,CAAC,IAAI,CAAC,CAAC;aACnB;QACH,CAAC;QAED,uCACK,KAAK,KACR,iBAAiB,EAAE,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,EACrD,0BAA0B,EAAE,SAAS,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EACjE,WAAW,EAAE,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAChD;IACJ,CAAC;CACF,CAAC,CAAC"}