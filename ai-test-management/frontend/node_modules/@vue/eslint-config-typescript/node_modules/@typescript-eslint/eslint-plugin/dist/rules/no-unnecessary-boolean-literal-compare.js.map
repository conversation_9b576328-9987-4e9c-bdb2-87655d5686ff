{"version": 3, "file": "no-unnecessary-boolean-literal-compare.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-boolean-literal-compare.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AA0BhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,wCAAwC;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,oEAAoE;YACtE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,MAAM,EACJ,mGAAmG;YACrG,OAAO,EACL,6FAA6F;YAC/F,6BAA6B,EAC3B,uGAAuG;YACzG,8BAA8B,EAC5B,iGAAiG;YACnG,wBAAwB,EACtB,iIAAiI;SACpI;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,oCAAoC,EAAE;wBACpC,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;oBACD,qCAAqC,EAAE;wBACrC,WAAW,EACT,8EAA8E;wBAChF,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE;QACd;YACE,oCAAoC,EAAE,IAAI;YAC1C,qCAAqC,EAAE,IAAI;SAC5C;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,oBAAoB,CAC3B,IAA+B;YAE/B,MAAM,UAAU,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,SAAS,CAAC;aAClB;YAED,MAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,CAC9C,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAChE,CAAC;YAEF,IAAI,aAAa,CAAC,cAAc,CAAC,EAAE;gBACjC,uCACK,UAAU,KACb,2BAA2B,EAAE,KAAK,IAClC;aACH;YAED,IAAI,iBAAiB,CAAC,cAAc,CAAC,EAAE;gBACrC,uCACK,UAAU,KACb,2BAA2B,EAAE,IAAI,IACjC;aACH;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,aAAa,CAAC,cAAuB;YAC5C,OAAO,OAAO,CAAC,aAAa,CAC1B,cAAc,EACd,EAAE,CAAC,SAAS,CAAC,OAAO,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,CACnD,CAAC;QACJ,CAAC;QAED;;;;;WAKG;QACH,SAAS,iBAAiB,CAAC,cAAuB;YAChD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE;gBAC7B,OAAO,KAAK,CAAC;aACd;YAED,MAAM,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC;YAEjC,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAClC,IAAI,CAAC,EAAE,CACL,CAAC,OAAO,CAAC,aAAa,CACpB,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAC3C,CACJ,CAAC;YAEF,MAAM,iBAAiB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;YACrD,IAAI,CAAC,iBAAiB,EAAE;gBACtB,OAAO,KAAK,CAAC;aACd;YAED,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC9D,IAAI,CAAC,eAAe,EAAE;gBACpB,OAAO,KAAK,CAAC;aACd;YAED,MAAM,4BAA4B,GAAG,eAAe,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC1E,IAAI,CAAC,4BAA4B,EAAE;gBACjC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,qBAAqB,CAC5B,IAA+B;YAE/B,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,cAAc,EAAE;gBACnB,OAAO,SAAS,CAAC;aAClB;YAED,KAAK,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI;gBAClC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;gBACvB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;aACxB,EAAE;gBACD,IACE,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACvC,OAAO,OAAO,CAAC,KAAK,KAAK,SAAS,EAClC;oBACA,SAAS;iBACV;gBAED,MAAM,EAAE,KAAK,EAAE,0BAA0B,EAAE,GAAG,OAAO,CAAC;gBACtD,MAAM,OAAO,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC;gBAE3C,OAAO;oBACL,0BAA0B;oBAC1B,UAAU;oBACV,OAAO;iBACR,CAAC;aACH;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,mBAAmB,CAAC,IAAmB;YAC9C,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,QAAQ,KAAK,GAAG,CACtB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAAI;gBACnB,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAC9C,IAAI,UAAU,KAAK,SAAS,EAAE;oBAC5B,OAAO;iBACR;gBAED,IAAI,UAAU,CAAC,2BAA2B,EAAE;oBAC1C,IACE,UAAU,CAAC,0BAA0B;wBACrC,OAAO,CAAC,oCAAoC,EAC5C;wBACA,OAAO;qBACR;oBACD,IACE,CAAC,UAAU,CAAC,0BAA0B;wBACtC,OAAO,CAAC,qCAAqC,EAC7C;wBACA,OAAO;qBACR;iBACF;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,GAAG,EAAE,QAAQ,CAAC,EAAE,KAAK;wBACnB,uCAAuC;wBACvC,iEAAiE;wBACjE,qCAAqC;wBAErC,MAAM,eAAe,GACnB,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAE1D,MAAM,YAAY,GAChB,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC,0BAA0B,CAAC;wBAE/D,MAAM,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,MAAO,CAAC,CAAC,CAAC,IAAI,CAAC;wBAE1D,MAAM,KAAK,CAAC,WAAW,CACrB,WAAW,EACX,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAC1C,CAAC;wBAEF,mGAAmG;wBACnG,IAAI,YAAY,KAAK,eAAe,EAAE;4BACpC,MAAM,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;4BAE/C,kFAAkF;4BAClF,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gCACvD,MAAM,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gCAC/C,MAAM,KAAK,CAAC,eAAe,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;6BAC/C;yBACF;wBAED,2FAA2F;wBAC3F,IACE,UAAU,CAAC,2BAA2B;4BACtC,CAAC,UAAU,CAAC,0BAA0B,EACtC;4BACA,6BAA6B;4BAC7B,MAAM,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;4BAC/C,MAAM,KAAK,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;yBACvD;oBACH,CAAC;oBACD,SAAS,EAAE,UAAU,CAAC,2BAA2B;wBAC/C,CAAC,CAAC,UAAU,CAAC,0BAA0B;4BACrC,CAAC,CAAC,UAAU,CAAC,OAAO;gCAClB,CAAC,CAAC,gCAAgC;gCAClC,CAAC,CAAC,+BAA+B;4BACnC,CAAC,CAAC,0BAA0B;wBAC9B,CAAC,CAAC,UAAU,CAAC,OAAO;4BACpB,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,QAAQ;oBACZ,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAOH,SAAS,aAAa,CAAC,QAAgB;IACrC,QAAQ,QAAQ,EAAE;QAChB,KAAK,IAAI;YACP,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,KAAK;aAChB,CAAC;QAEJ,KAAK,KAAK;YACR,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,IAAI;aACf,CAAC;QAEJ,KAAK,IAAI;YACP,OAAO;gBACL,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;aAChB,CAAC;QAEJ,KAAK,KAAK;YACR,OAAO;gBACL,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,IAAI;aACf,CAAC;QAEJ;YACE,OAAO,SAAS,CAAC;KACpB;AACH,CAAC"}