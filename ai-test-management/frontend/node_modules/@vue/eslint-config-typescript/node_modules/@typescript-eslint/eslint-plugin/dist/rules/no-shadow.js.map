{"version": 3, "file": "no-shadow.js", "sourceRoot": "", "sources": ["../../src/rules/no-shadow.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAIA,oEAA6E;AAE7E,oDAAoE;AAEpE,8CAAgC;AAchC,MAAM,+BAA+B,GAAG,IAAI,GAAG,CAAC;IAC9C,sBAAc,CAAC,0BAA0B;IACzC,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,iBAAiB;CACjC,CAAC,CAAC;AAEH,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,WAAW;IACjB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,qFAAqF;YACvF,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,cAAc,EAAE;wBACd,IAAI,EAAE,SAAS;qBAChB;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC;qBACpC;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;oBACD,sBAAsB,EAAE;wBACtB,IAAI,EAAE,SAAS;qBAChB;oBACD,qBAAqB,EAAE;wBACrB,IAAI,EAAE,SAAS;qBAChB;oBACD,0CAA0C,EAAE;wBAC1C,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,QAAQ,EACN,uGAAuG;YACzG,cAAc,EAAE,0CAA0C;SAC3D;KACF;IACD,cAAc,EAAE;QACd;YACE,KAAK,EAAE,EAAE;YACT,cAAc,EAAE,KAAK;YACrB,KAAK,EAAE,WAAW;YAClB,sBAAsB,EAAE,KAAK;YAC7B,qBAAqB,EAAE,IAAI;YAC3B,0CAA0C,EAAE,IAAI;SACjD;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB;;WAEG;QACH,SAAS,oBAAoB,CAAC,KAA2B;YACvD,OAAO,CACL,CAAC,KAAK,CAAC,IAAI,KAAK,yBAAS,CAAC,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;gBAC3D,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CACrD,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,WAAW,CAAC,QAAiC;YACpD,OAAO,CACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,SAAS;gBAClD,QAAQ,CAAC,IAAI,KAAK,MAAM,CACzB,CAAC;QACJ,CAAC;QAED,SAAS,YAAY,CACnB,UAAuB;YAEvB,OAAO,CACL,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,MAAK,8BAAc,CAAC,aAAa;gBACjD,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,KAAK,MAAM;oBACtC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBACtD,UAAU,CAAC,IAAI,CAAC,UAAU,KAAK,MAAM,CAAC,CAAC,CAC5C,CAAC;QACJ,CAAC;QAED,SAAS,iBAAiB,CACxB,QAAiC,EACjC,QAAiC;YAEjC,IAAI,OAAO,CAAC,qBAAqB,KAAK,IAAI,EAAE;gBAC1C,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,CAAC,iBAAiB,IAAI,QAAQ,CAAC,EAAE;gBACpC,2BAA2B;gBAC3B,OAAO,KAAK,CAAC;aACd;YAED,MAAM,CAAC,eAAe,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,eAAe,GACnB,CAAC,CAAC,iBAAiB,IAAI,QAAQ,CAAC;gBAChC,CAAC,eAAe;gBAChB,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC;YAC/D,OAAO,QAAQ,CAAC,eAAe,KAAK,eAAe,CAAC;QACtD,CAAC;QAED,SAAS,sCAAsC,CAC7C,QAAiC,EACjC,QAAiC;YAEjC,IAAI,OAAO,CAAC,0CAA0C,KAAK,IAAI,EAAE;gBAC/D,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,CAAC,iBAAiB,IAAI,QAAQ,CAAC,EAAE;gBACpC,2BAA2B;gBAC3B,OAAO,KAAK,CAAC;aACd;YAED,MAAM,eAAe,GACnB,iBAAiB,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;YAClE,IAAI,CAAC,eAAe,EAAE;gBACpB,OAAO,KAAK,CAAC;aACd;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAC/B,+BAA+B,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CACnD,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAC9B,QAAiC;YAEjC,IAAI,CAAC,CAAC,gBAAgB,IAAI,QAAQ,CAAC,EAAE;gBACnC,2BAA2B;gBAC3B,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAC5B,OAAO,KAAK,CAAC;aACd;YAED,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACrD,IAAI,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe,EAAE;gBAC1D,OAAO,KAAK,CAAC;aACd;YACD,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAAC;YAC/C,IACE,CAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,IAAI,MAAK,sBAAc,CAAC,0BAA0B,EACrE;gBACA,OAAO,KAAK,CAAC;aACd;YACD,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;YAC9C,IACE,CAAC,YAAY;gBACb,CAAC,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;oBACtD,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,6BAA6B,CAAC,EACrE;gBACA,OAAO,KAAK,CAAC;aACd;YACD,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC;YAC7C,IAAI,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB,EAAE;gBAC9D,OAAO,KAAK,CAAC;aACd;YACD,OAAO,gBAAgB,CAAC,MAAM,CAAC;QACjC,CAAC;QAED,SAAS,oBAAoB,CAAC,QAAiC;YAC7D,IAAI,CAAC,CAAC,gBAAgB,IAAI,QAAQ,CAAC,EAAE;gBACnC,2BAA2B;gBAC3B,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAC5B,OAAO,KAAK,CAAC;aACd;YAED,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACrD,IAAI,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe,EAAE;gBAC1D,OAAO,KAAK,CAAC;aACd;YACD,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAAC;YAC/C,IACE,CAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,IAAI,MAAK,sBAAc,CAAC,0BAA0B,EACrE;gBACA,OAAO,KAAK,CAAC;aACd;YACD,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC;YAC3C,OAAO,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB,CAAC;QAC7D,CAAC;QAED,SAAS,8BAA8B,CACrC,QAAiC,EACjC,QAAiC;YAEjC,OAAO,CACL,uBAAuB,CAAC,QAAQ,CAAC,IAAI,oBAAoB,CAAC,QAAQ,CAAC,CACpE,CAAC;QACJ,CAAC;QAED,SAAS,mBAAmB,CAC1B,UAEsC;YAEtC,OAAO,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QAC9D,CAAC;QAED,SAAS,mCAAmC,CAC1C,KAA2B,EAC3B,IAAY;YAEZ,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,yBAAS,CAAC,QAAQ;gBACjC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBACvD,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBAC9C,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,CAC9B,CAAC;QACJ,CAAC;QAED,SAAS,4BAA4B,CACnC,KAA2B,EAC3B,QAAiC,EACjC,QAAiC;;YAEjC,MAAM,CAAC,eAAe,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;YACxC,MAAM,CAAC,gBAAgB,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;YAEzC,OAAO,CACL,YAAY,CAAC,eAAe,CAAC;gBAC7B,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC;gBAC3C,mCAAmC,CACjC,KAAK,EACL,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CACpC;gBACD,gBAAgB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;gBACpE,CAAA,MAAA,gBAAgB,CAAC,IAAI,CAAC,MAAM,0CAAE,IAAI;oBAChC,sBAAc,CAAC,sBAAsB,CACxC,CAAC;QACJ,CAAC;QAED;;;;WAIG;QACH,SAAS,SAAS,CAAC,QAAiC;YAClD,OAAO,OAAO,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACtD,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,6BAA6B,CACpC,QAAiC;YAEjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;YAEnC,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC9C,KAAK,CAAC,EAAE,KAAK,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CACrC,CAAC;QACJ,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,4BAA4B,CACnC,QAAiC;YAEjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;YAEnC,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;gBAC/C,KAAK,CAAC,EAAE,KAAK,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CACrC,CAAC;QACJ,CAAC;QAED;;;;;WAKG;QACH,SAAS,SAAS,CAChB,IAA0B,EAC1B,QAAgB;YAEhB,OAAO,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC;QAED;;;;;WAKG;QACH,SAAS,kBAAkB,CACzB,IAA+B,EAC/B,KAAuC;YAEvC,IAAI,WAAW,GAAG,IAAI,CAAC;YAEvB,OAAO,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;gBACzC,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;aAClC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QAED;;;;WAIG;QACH,SAAS,aAAa,CACpB,KAA2B;YAE3B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAE1B,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,0BAA0B,EAAE;gBAC9C,OAAO,KAAK,CAAC,KAAK,CAAC;aACpB;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;;;;WAKG;QACH,SAAS,iBAAiB,CACxB,QAAiC,EACjC,gBAAyC;;YAEzC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1C,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,KAAK,CAAC;aACd;YAED,MAAM,EAAE,aAAa,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;YAEzC,IACE,CAAC,CACC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI;gBACvB,sBAAc,CAAC,uBAAuB;gBACtC,aAAa,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBACjE,aAAa,CAAC,aAAa,CAAC,KAAK,gBAAgB,CAAC,KAAK,CACxD,EACD;gBACA,OAAO,KAAK,CAAC;aACd;YAED,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC;YAChC,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC;YAEvB,MAAM,cAAc,GAAG,kBAAkB,CACvC,MAAM,EACN,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CACpD,CAAC;YAEF,IAAI,CAAC,cAAc,EAAE;gBACnB,OAAO,KAAK,CAAC;aACd;YAED,IAAI,IAAI,GAA8B,QAAQ,CAAC,IAAI,CAAC;YACpD,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEzC,OAAO,IAAI,EAAE;gBACX,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE;oBACnD,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;wBAClC,OAAO,IAAI,CAAC;qBACb;oBACD,IACE,CAAC,CAAA,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc;wBAC1D,CAAA,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc,CAAC;wBAC9D,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,EAC7C;wBACA,OAAO,IAAI,CAAC;qBACb;oBACD,MAAM;iBACP;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;oBACzD,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;wBACnC,OAAO,IAAI,CAAC;qBACb;iBACF;qBAAM,IACL;oBACE,sBAAc,CAAC,mBAAmB;oBAClC,sBAAc,CAAC,gBAAgB;oBAC/B,sBAAc,CAAC,kBAAkB;oBACjC,sBAAc,CAAC,eAAe;oBAC9B,sBAAc,CAAC,uBAAuB;oBACtC,sBAAc,CAAC,WAAW;oBAC1B,sBAAc,CAAC,iBAAiB;oBAChC,sBAAc,CAAC,sBAAsB;iBACtC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EACrB;oBACA,MAAM;iBACP;gBAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;aACpB;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;;;;;;;WAQG;QACH,SAAS,eAAe,CACtB,QAAiC,EACjC,QAAiC;;YAEjC,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;YAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,KAAK,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,0CAAE,KAAK,CAAC;YACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;YAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC,KAAK,CAAC;YAEnC,OAAO,CAAC,CAAC,CACP,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBACnB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,QAAQ,CAAC,IAAI,KAAK,8BAAc,CAAC,YAAY;oBAC7C,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC;oBACzD,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAAC;gBACxD,UAAU,KAAK,UAAU,CAAC,KAAK,CAChC,CAAC;QACJ,CAAC;QAED;;;;WAIG;QACH,SAAS,YAAY,CACnB,QAAiC;YAEjC,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,OAAO,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,CAAC,KAAK,CAAC;QACzB,CAAC;QAED;;;;;WAKG;QACH,SAAS,OAAO,CACd,QAAiC,EACjC,QAAiC;YAEjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;YAErC,OAAO,CAAC,CAAC,CACP,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBACnB,0DAA0D;gBAC1D,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;oBAC5B,CAAC,QAAQ;oBACT,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CAAC,CAC7D,CAAC;QACJ,CAAC;QAED;;;;WAIG;QACH,SAAS,mBAAmB,CAC1B,QAAiC;YAEjC,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAI,UAAU,EAAE;gBACd,OAAO;oBACL,MAAM,EAAE,KAAK;oBACb,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;oBAC/B,MAAM,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;iBACxC,CAAC;aACH;iBAAM;gBACL,OAAO;oBACL,MAAM,EAAE,IAAI;iBACb,CAAC;aACH;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,eAAe,CAAC,KAA2B;YAClD,6BAA6B;YAC7B,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE;gBAC/B,OAAO;aACR;YAED,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;YAElC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,qBAAqB;gBACrB,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrC,SAAS;iBACV;gBAED,wDAAwD;gBACxD,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;oBACzB,SAAS;iBACV;gBAED,0EAA0E;gBAC1E,IAAI,6BAA6B,CAAC,QAAQ,CAAC,EAAE;oBAC3C,SAAS;iBACV;gBAED,0EAA0E;gBAC1E,IAAI,4BAA4B,CAAC,QAAQ,CAAC,EAAE;oBAC1C,SAAS;iBACV;gBAED,kCAAkC;gBAClC,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE;oBACvB,SAAS;iBACV;gBAED,0BAA0B;gBAC1B,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK;oBAC1B,CAAC,CAAC,gBAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC;oBACnD,CAAC,CAAC,IAAI,CAAC;gBACT,IAAI,CAAC,QAAQ,EAAE;oBACb,SAAS;iBACV;gBAED,qDAAqD;gBACrD,IAAI,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;oBACzC,SAAS;iBACV;gBAED,8DAA8D;gBAC9D,IAAI,sCAAsC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;oBAC9D,SAAS;iBACV;gBAED,6DAA6D;gBAC7D,0DAA0D;gBAC1D,kDAAkD;gBAClD,IAAI,8BAA8B,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;oBACtD,SAAS;iBACV;gBAED,IAAI,4BAA4B,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;oBAC3D,SAAS;iBACV;gBAED,MAAM,cAAc,GAAG,WAAW,IAAI,QAAQ,CAAC;gBAC/C,IACE,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;oBAC9B,CAAC,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,CAAC;oBAC7C,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC;oBACpC,CAAC,CACC,OAAO,CAAC,sBAAsB;wBAC9B,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CACtC;oBACD,CAAC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,EACzD;oBACA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;oBAE/C,OAAO,CAAC,MAAM,iBACZ,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAC1B,CAAC,QAAQ,CAAC,MAAM;wBACjB,CAAC,CAAC;4BACE,SAAS,EAAE,gBAAgB;4BAC3B,IAAI,EAAE;gCACJ,IAAI,EAAE,QAAQ,CAAC,IAAI;6BACpB;yBACF;wBACH,CAAC,CAAC;4BACE,SAAS,EAAE,UAAU;4BACrB,IAAI,EAAE;gCACJ,IAAI,EAAE,QAAQ,CAAC,IAAI;gCACnB,YAAY,EAAE,QAAQ,CAAC,IAAI;gCAC3B,cAAc,EAAE,QAAQ,CAAC,MAAM;6BAChC;yBACF,CAAC,EACN,CAAC;iBACJ;aACF;QACH,CAAC;QAED,OAAO;YACL,cAAc;gBACZ,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACvC,MAAM,KAAK,GAAG,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;gBAE9C,OAAO,KAAK,CAAC,MAAM,EAAE;oBACnB,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;oBAE3B,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;oBACjC,eAAe,CAAC,KAAK,CAAC,CAAC;iBACxB;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}