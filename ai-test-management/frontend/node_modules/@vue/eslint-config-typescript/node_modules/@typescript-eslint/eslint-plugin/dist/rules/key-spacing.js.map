{"version": 3, "file": "key-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/key-spacing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,aAAa,CAAC,CAAC;AAKlD,mEAAmE;AACnE,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AAEzB;;GAEG;AACH,SAAS,EAAE,CAAI,GAAQ,EAAE,QAAgB;IACvC,IAAI,QAAQ,GAAG,CAAC,EAAE;QAChB,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;KACnC;IACD,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;AACvB,CAAC;AAED,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,aAAa;IACnB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EACT,gGAAgG;YAClG,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,CAAC,UAAU,CAAC;QACpB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IAEpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE3C;;WAEG;QACH,SAAS,cAAc,CAAC,QAA2B;YACjD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,6BAA6B;YAC7D,OAAO,IAAI,CAAC,eAAe,CACzB,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAE,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CACtD,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,uBAAuB,CAAC,IAAmB;YAClD,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAE,CAAC;YAEtE,OAAO,UAAU,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC;QAChD,CAAC;QAWD,SAAS,aAAa,CACpB,IAAmB;YAEnB,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBAClD,CAAC,CAAC,IAAI,CAAC,cAAc,CACtB,CAAC;QACJ,CAAC;QAED,SAAS,YAAY,CACnB,IAAmB;YAEnB,OAAO,CACL,aAAa,CAAC,IAAI,CAAC;gBACnB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CACzD,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,UAAU,CAAC,IAAmC;YACrD,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;gBACjD,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrC;YAED,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC,KAAK,CACf,CAAC,EACD,UAAU,CAAC,aAAa,CACtB,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAE,EACxB,IAAI,CAAC,qBAAqB,CAC1B,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAC5B,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,YAAY,CACnB,IAAmC;YAEnC,OAAO,uBAAuB,CAC5B,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC3C,CAAC,CAAC,IAAI,CAAC,GAAG;gBACV,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAE,CAC7B,CAAC,GAAG,CAAC,GAAG,CAAC;QACZ,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAmC,EACnC,6BAAqC,EACrC,IAA0B;YAE1B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;YAChC,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9C,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,6BAA6B,CAAC;YACzE,IAAI,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE;gBACnD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY;oBACrD,GAAG,EAAE,KAAK,CAAC,EAAE;wBACX,IAAI,UAAU,GAAG,CAAC,EAAE;4BAClB,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;gCACpC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;6BACxB,CAAC,CAAC;yBACJ;6BAAM;4BACL,OAAO,KAAK,CAAC,gBAAgB,CAC3B,cAAc,EACd,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CACxB,CAAC;yBACH;oBACH,CAAC;oBACD,IAAI,EAAE;wBACJ,QAAQ,EAAE,EAAE;wBACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;qBACtB;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,eAAe,CACtB,IAAmC,EACnC,4BAAoC,EACpC,IAA0B;YAE1B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;YAChC,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9C,MAAM,SAAS,GAAG,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;YACjE,MAAM,UAAU,GAAG,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG,4BAA4B,CAAC;YACxE,IAAI,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE;gBACnD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc;oBACzD,GAAG,EAAE,KAAK,CAAC,EAAE;wBACX,IAAI,UAAU,GAAG,CAAC,EAAE;4BAClB,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;gCACnD,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;6BACvC,CAAC,CAAC;yBACJ;6BAAM;4BACL,OAAO,KAAK,CAAC,gBAAgB,CAC3B,cAAc,CAAC,cAAc,EAC7B,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CACxB,CAAC;yBACH;oBACH,CAAC;oBACD,IAAI,EAAE;wBACJ,QAAQ,EAAE,EAAE;wBACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;qBACtB;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,6HAA6H;QAC7H,SAAS,mBAAmB,CAC1B,UAAyB,EACzB,SAAwB;YAExB,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;YAC/C,MAAM,uBAAuB,GAAG,CAC9B,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAChE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;YAEjB,IAAI,uBAAuB,KAAK,YAAY,EAAE;gBAC5C,OAAO,KAAK,CAAC;aACd;YAED,IAAI,uBAAuB,GAAG,YAAY,KAAK,CAAC,EAAE;gBAChD,OAAO,IAAI,CAAC;aACb;YAED;;;;eAIG;YACH,MAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhE,IACE,eAAe,CAAC,MAAM;gBACtB,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,YAAY,IAAI,CAAC;gBACrD,uBAAuB,GAAG,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC,CAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,EACpE;gBACA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC/C,IACE,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;wBAC/B,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;wBACrC,CAAC,EACD;wBACA,OAAO,KAAK,CAAC;qBACd;iBACF;gBACD,OAAO,IAAI,CAAC;aACb;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,KAAsB;;YAC7C,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,MAAM,KAAK,GACT,MAAA,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAClB,CAAC,CAAC,OAAO,CAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,KAAK,CAAA,KAAK,QAAQ;oBAC9C,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;oBAC5B,CAAC,CAAC,MAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,KAAK,mCAAI,OAAO,CAAC,KAAK,CAAC,mCAAI,OAAO,CAAC;YAC5D,MAAM,WAAW,GACf,MAAA,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW;gBAC3B,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK,QAAQ;wBAC3C,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW;wBACrC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW;oBACjC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,mCAAI,KAAK,CAAC;YACpC,MAAM,6BAA6B,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,UAAU,GACd,MAAA,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU;gBAC1B,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK,QAAQ;wBAC3C,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU;wBACpC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU;oBAChC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,mCAAI,IAAI,CAAC;YAClC,MAAM,4BAA4B,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,IAAI,GACR,MAAA,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI;gBACpB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK,QAAQ;wBAC3C,CAAC,CAAC,oCAAoC;4BACpC,MAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,mCAAI,OAAO,CAAC,SAAS,CAAC,IAAI;wBACxD,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI;oBAC1B,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mCAAI,QAAQ,CAAC;YAEhC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;oBACvB,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;oBAClD,WAAW,GAAG,IAAI,CAAC,GAAG,CACpB,WAAW,EACX,KAAK,KAAK,OAAO;wBACf,CAAC,CAAC,MAAM,GAAG,6BAA6B;wBACxC,CAAC,CAAC,MAAM;4BACJ,GAAG,CAAC,MAAM;4BACV,4BAA4B;4BAC5B,6BAA6B,CACpC,CAAC;iBACH;aACF;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;oBACvB,SAAS;iBACV;gBACD,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;gBAChC,MAAM,OAAO,GACX,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC;gBACrE,MAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;gBAEnE,IAAI,UAAU,EAAE;oBACd,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EACP,UAAU,GAAG,CAAC;4BACZ,CAAC,CAAC,KAAK,KAAK,OAAO;gCACjB,CAAC,CAAC,UAAU;gCACZ,CAAC,CAAC,YAAY;4BAChB,CAAC,CAAC,KAAK,KAAK,OAAO;gCACnB,CAAC,CAAC,YAAY;gCACd,CAAC,CAAC,cAAc;wBACpB,GAAG,EAAE,KAAK,CAAC,EAAE;4BACX,IAAI,UAAU,GAAG,CAAC,EAAE;gCAClB,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;oCAC7B,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;iCACjB,CAAC,CAAC;6BACJ;iCAAM;gCACL,OAAO,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;6BACjE;wBACH,CAAC;wBACD,IAAI,EAAE;4BACJ,QAAQ,EAAE,EAAE;4BACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;yBACtB;qBACF,CAAC,CAAC;iBACJ;gBAED,IAAI,KAAK,KAAK,OAAO,EAAE;oBACrB,eAAe,CAAC,IAAI,EAAE,4BAA4B,EAAE,IAAI,CAAC,CAAC;iBAC3D;qBAAM;oBACL,gBAAgB,CAAC,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC,CAAC;iBAC7D;aACF;QACH,CAAC;QAED,SAAS,mBAAmB,CAC1B,IAAmB,EACnB,EAAE,UAAU,EAA2B;;YAEvC,MAAM,WAAW,GACf,MAAA,CAAC,UAAU;gBACT,CAAC,CAAC,OAAO,CAAC,UAAU;oBAClB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW;oBAChC,CAAC,CAAC,OAAO,CAAC,WAAW;gBACvB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW;oBAC/B,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,mCAAI,KAAK,CAAC;YACpC,MAAM,6BAA6B,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,UAAU,GACd,MAAA,CAAC,UAAU;gBACT,CAAC,CAAC,OAAO,CAAC,UAAU;oBAClB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU;oBAC/B,CAAC,CAAC,OAAO,CAAC,UAAU;gBACtB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU;oBAC9B,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,mCAAI,IAAI,CAAC;YAClC,MAAM,4BAA4B,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,IAAI,GACR,MAAA,CAAC,UAAU;gBACT,CAAC,CAAC,OAAO,CAAC,UAAU;oBAClB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI;oBACzB,CAAC,CAAC,OAAO,CAAC,IAAI;gBAChB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI;oBACxB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mCAAI,QAAQ,CAAC;YAEhC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;gBACtB,gBAAgB,CAAC,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC,CAAC;gBAC5D,eAAe,CAAC,IAAI,EAAE,4BAA4B,EAAE,IAAI,CAAC,CAAC;aAC3D;QACH,CAAC;QAED,SAAS,YAAY,CACnB,IAGsB;;YAEtB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;YAE/D,MAAM,OAAO,GACX,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAExE,IAAI,WAAW,GAAsB,EAAE,CAAC;YACxC,IAAI,iBAAiB,GAAoB,EAAE,CAAC;YAE5C,IAAI,OAAO,CAAC,KAAK,KAAI,MAAA,OAAO,CAAC,SAAS,0CAAE,KAAK,CAAA,EAAE;gBAC7C,IAAI,iBAAiB,GAAoB,EAAE,CAAC;gBAC5C,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAEpC,IAAI,QAAQ,GAA8B,SAAS,CAAC;gBAEpD,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;oBAC1B,IAAI,eAAe,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC;oBAChD,IAAI,eAAe,KAAK,QAAQ,EAAE;wBAChC,eAAe,GAAG,SAAS,CAAC;qBAC7B;oBAED,IAAI,eAAe,IAAI,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE;wBACjE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBAC9B;yBAAM,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,MAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;wBAC3D,IAAI,eAAe,EAAE;4BACnB,kEAAkE;4BAClE,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;4BACxC,iBAAiB,CAAC,GAAG,EAAE,CAAC;yBACzB;wBACD,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBAC9B;yBAAM;wBACL,iBAAiB,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC3B,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;qBACrC;oBAED,QAAQ,GAAG,IAAI,CAAC;iBACjB;gBAED,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAC1C,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CACnD,CAAC;gBACF,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;aAC9D;iBAAM;gBACL,iBAAiB,GAAG,OAAO,CAAC;aAC7B;YAED,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;gBAC/B,eAAe,CAAC,KAAK,CAAC,CAAC;aACxB;YAED,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE;gBACpC,mBAAmB,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC,CAAC;aACzD;QACH,CAAC;QACD,uCACK,SAAS,KACZ,aAAa,EAAE,YAAY,EAC3B,eAAe,EAAE,YAAY,EAC7B,SAAS,EAAE,YAAY,IACvB;IACJ,CAAC;CACF,CAAC,CAAC"}