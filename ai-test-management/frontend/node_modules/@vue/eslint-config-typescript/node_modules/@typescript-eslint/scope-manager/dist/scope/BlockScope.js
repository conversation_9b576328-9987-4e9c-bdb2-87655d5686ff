"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class BlockScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON>anager, ScopeType_1.ScopeType.block, upperScope, block, false);
    }
}
exports.BlockScope = BlockScope;
//# sourceMappingURL=BlockScope.js.map