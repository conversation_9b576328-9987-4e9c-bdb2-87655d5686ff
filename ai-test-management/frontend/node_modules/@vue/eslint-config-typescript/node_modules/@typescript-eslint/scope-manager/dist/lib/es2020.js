"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2020 = void 0;
const es2019_1 = require("./es2019");
const es2020_bigint_1 = require("./es2020.bigint");
const es2020_date_1 = require("./es2020.date");
const es2020_intl_1 = require("./es2020.intl");
const es2020_number_1 = require("./es2020.number");
const es2020_promise_1 = require("./es2020.promise");
const es2020_sharedmemory_1 = require("./es2020.sharedmemory");
const es2020_string_1 = require("./es2020.string");
const es2020_symbol_wellknown_1 = require("./es2020.symbol.wellknown");
exports.es2020 = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, es2019_1.es2019), es2020_bigint_1.es2020_bigint), es2020_date_1.es2020_date), es2020_number_1.es2020_number), es2020_promise_1.es2020_promise), es2020_sharedmemory_1.es2020_sharedmemory), es2020_string_1.es2020_string), es2020_symbol_wellknown_1.es2020_symbol_wellknown), es2020_intl_1.es2020_intl);
//# sourceMappingURL=es2020.js.map