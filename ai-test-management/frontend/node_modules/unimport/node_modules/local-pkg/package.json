{"name": "local-pkg", "type": "module", "version": "1.1.1", "packageManager": "pnpm@10.5.2", "description": "Get information on local packages.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/local-pkg#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/local-pkg.git"}, "bugs": {"url": "https://github.com/antfu/local-pkg/issues"}, "keywords": ["package"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14"}, "scripts": {"prepublishOnly": "nr build", "build": "unbuild", "lint": "eslint .", "release": "bumpp && npm publish", "typecheck": "tsc --noEmit", "test": "vitest run && node ./test/cjs.cjs && node ./test/esm.mjs"}, "dependencies": {"mlly": "^1.7.4", "pkg-types": "^2.0.1", "quansync": "^0.2.8"}, "devDependencies": {"@antfu/eslint-config": "^4.4.0", "@antfu/ni": "^23.3.1", "@antfu/utils": "^9.1.0", "@types/chai": "^5.0.1", "@types/node": "^22.13.8", "bumpp": "^10.0.3", "chai": "^5.2.0", "eslint": "^9.21.0", "esno": "^4.8.0", "find-up-simple": "^1.0.1", "typescript": "^5.8.2", "unbuild": "^3.5.0", "unplugin-quansync": "^0.3.3", "vitest": "^3.0.7"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}