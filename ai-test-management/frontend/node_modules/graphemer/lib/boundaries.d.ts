/**
 * The Grapheme_Cluster_Break property value
 * @see https://www.unicode.org/reports/tr29/#Default_Grapheme_Cluster_Table
 */
export declare enum CLUSTER_BREAK {
    CR = 0,
    LF = 1,
    CONTROL = 2,
    EXTEND = 3,
    R<PERSON><PERSON>AL_INDICATOR = 4,
    SPACINGMARK = 5,
    L = 6,
    V = 7,
    T = 8,
    LV = 9,
    LVT = 10,
    OTHER = 11,
    PREPEND = 12,
    E_BASE = 13,
    E_MODIFIER = 14,
    ZWJ = 15,
    GLUE_AFTER_ZWJ = 16,
    E_BASE_GAZ = 17
}
/**
 * The Emoji character property is an extension of UCD but shares the same namespace and structure
 * @see http://www.unicode.org/reports/tr51/tr51-14.html#Emoji_Properties_and_Data_Files
 *
 * Here we model Extended_Pictograhpic only to implement UAX #29 GB11
 * \p{Extended_Pictographic} Extend* ZWJ	×	\p{Extended_Pictographic}
 *
 * The Emoji character property should not be mixed with Grapheme_Cluster_Break since they are not exclusive
 */
export declare const EXTENDED_PICTOGRAPHIC = 101;
//# sourceMappingURL=boundaries.d.ts.map