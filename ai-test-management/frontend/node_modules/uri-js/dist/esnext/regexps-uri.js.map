{"version": 3, "file": "regexps-uri.js", "sourceRoot": "", "sources": ["../../src/regexps-uri.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEvC,MAAM,oBAAoB,KAAa;IACtC,MACC,OAAO,GAAG,UAAU,EACpB,GAAG,GAAG,SAAS,EACf,OAAO,GAAG,OAAO,EACjB,QAAQ,GAAG,SAAS,EACpB,QAAQ,GAAG,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,EAAG,kBAAkB;IAC1D,IAAI,GAAG,SAAS,EAChB,IAAI,GAAG,SAAS,EAChB,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,aAAa,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,EAAG,UAAU;IACvO,YAAY,GAAG,yBAAyB,EACxC,YAAY,GAAG,qCAAqC,EACpD,UAAU,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,CAAC,EAC9C,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,6EAA6E,CAAC,CAAC,CAAC,IAAI,EAAG,0CAA0C;IACrJ,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,EAAG,QAAQ;IAC1D,YAAY,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,SAAS,CAAC,EACnE,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,GAAG,GAAG,CAAC,EACxE,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,EACjG,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,OAAO,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,EACnK,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,OAAO,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC,EAAG,uBAAuB;IAC3M,YAAY,GAAG,MAAM,CAAC,kBAAkB,GAAG,KAAK,GAAG,kBAAkB,GAAG,KAAK,GAAG,kBAAkB,GAAG,KAAK,GAAG,kBAAkB,CAAC,EAChI,IAAI,GAAG,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,EACjC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,EAChE,aAAa,GAAG,MAAM,CAA6D,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,8CAA8C;IACxK,aAAa,GAAG,MAAM,CAAkD,QAAQ,GAAG,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,8CAA8C;IACxK,aAAa,GAAG,MAAM,CAAC,MAAM,CAAkC,IAAI,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,8CAA8C;IACxK,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,8CAA8C;IACxK,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,8CAA8C;IACxK,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,SAAS,GAAU,IAAI,GAAG,KAAK,GAAY,KAAK,CAAC,EAAE,8CAA8C;IACxK,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,SAAS,GAAkC,KAAK,CAAC,EAAE,8CAA8C;IACxK,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,SAAS,GAAkC,IAAI,CAAE,EAAE,6CAA6C;IACvK,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,SAAS,CAAwC,EAAE,4BAA4B;IACtJ,YAAY,GAAG,MAAM,CAAC,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EACxK,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC,EAAG,UAAU;IAC9E,UAAU,GAAG,MAAM,CAAC,YAAY,GAAG,OAAO,GAAG,OAAO,CAAC,EAAG,UAAU;IAClE,kBAAkB,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,cAAc,GAAG,QAAQ,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,EAAG,sCAAsC;IACzI,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC,EAClG,WAAW,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,kBAAkB,GAAG,GAAG,GAAG,YAAY,GAAG,GAAG,GAAG,UAAU,CAAC,GAAG,KAAK,CAAC,EAAG,UAAU;IACrH,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC,EACxF,KAAK,GAAG,MAAM,CAAC,WAAW,GAAG,GAAG,GAAG,YAAY,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS,CAAC,EAC5F,KAAK,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,EAC7B,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,EACxF,MAAM,GAAG,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC,EACnF,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,EAC/B,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,EAClC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,EACtG,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,EACtD,cAAc,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,EAAG,YAAY;IACzF,cAAc,GAAG,MAAM,CAAC,cAAc,GAAG,aAAa,CAAC,EAAG,YAAY;IACtE,cAAc,GAAG,MAAM,CAAC,WAAW,GAAG,aAAa,CAAC,EAAG,YAAY;IACnE,WAAW,GAAG,KAAK,GAAG,MAAM,GAAG,GAAG,EAClC,KAAK,GAAG,MAAM,CAAC,aAAa,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,WAAW,CAAC,EACtH,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC,EAC3E,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,EACtD,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,UAAU,GAAG,aAAa,CAAC,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,WAAW,CAAC,EACpI,IAAI,GAAG,MAAM,CAAC,OAAO,GAAG,KAAK,GAAG,UAAU,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,EAC5G,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,UAAU,GAAG,aAAa,CAAC,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,WAAW,CAAC,EACxI,SAAS,GAAG,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,EACnG,cAAc,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,SAAS,CAAC,EAC/C,aAAa,GAAG,MAAM,CAAC,OAAO,GAAG,KAAK,GAAG,UAAU,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,EAEnF,YAAY,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,aAAa,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,WAAW,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,EAC7U,aAAa,GAAG,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,aAAa,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,WAAW,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,EAC/T,aAAa,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,aAAa,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,WAAW,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,EACrS,YAAY,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,EAC5D,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAChH;IAED,OAAO;QACN,UAAU,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE,GAAG,CAAC;QAC3E,YAAY,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC;QAC9E,QAAQ,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC;QAChF,QAAQ,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC;QAChF,iBAAiB,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC;QACtF,SAAS,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,CAAC,EAAE,GAAG,CAAC;QACtG,YAAY,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,CAAC,EAAE,GAAG,CAAC;QAC7F,MAAM,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC;QAClE,UAAU,EAAG,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC;QAC1C,WAAW,EAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE,GAAG,CAAC;QACxE,WAAW,EAAG,IAAI,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC;QAC3C,WAAW,EAAG,IAAI,MAAM,CAAC,IAAI,GAAG,YAAY,GAAG,IAAI,CAAC;QACpD,WAAW,EAAG,IAAI,MAAM,CAAC,QAAQ,GAAG,YAAY,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,GAAG,QAAQ,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAE,sCAAsC;KACrL,CAAC;AACH,CAAC;AAED,eAAe,SAAS,CAAC,KAAK,CAAC,CAAC"}