<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600">
  <defs>
    <linearGradient id="dashboardGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f2f8ea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8fdf5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e8f4dc;stop-opacity:1" />
    </linearGradient>
    <pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="rgba(64, 158, 255, 0.1)"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#dashboardGradient)"/>
  <rect width="1200" height="600" fill="url(#dots)"/>
  
  <!-- Decorative elements -->
  <g opacity="0.3">
    <!-- Data visualization elements -->
    <rect x="50" y="50" width="8" height="40" fill="#409eff" rx="4"/>
    <rect x="65" y="30" width="8" height="60" fill="#67c23a" rx="4"/>
    <rect x="80" y="40" width="8" height="50" fill="#e6a23c" rx="4"/>
    <rect x="95" y="20" width="8" height="70" fill="#f56c6c" rx="4"/>
    
    <!-- Chart lines -->
    <path d="M 150 80 Q 180 60 210 70 T 270 50" stroke="#409eff" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 150 90 Q 180 110 210 100 T 270 120" stroke="#67c23a" stroke-width="2" fill="none" opacity="0.6"/>
  </g>
  
  <!-- Tech pattern on the right -->
  <g opacity="0.2" transform="translate(1000, 100)">
    <circle cx="0" cy="0" r="3" fill="#409eff"/>
    <circle cx="20" cy="10" r="2" fill="#67c23a"/>
    <circle cx="40" cy="-5" r="2.5" fill="#e6a23c"/>
    <circle cx="60" cy="15" r="2" fill="#f56c6c"/>
    
    <line x1="0" y1="0" x2="20" y2="10" stroke="#409eff" stroke-width="1" opacity="0.5"/>
    <line x1="20" y1="10" x2="40" y2="-5" stroke="#67c23a" stroke-width="1" opacity="0.5"/>
    <line x1="40" y1="-5" x2="60" y2="15" stroke="#e6a23c" stroke-width="1" opacity="0.5"/>
  </g>
  
  <!-- Floating geometric shapes -->
  <g opacity="0.1">
    <polygon points="200,500 220,480 240,500 220,520" fill="#409eff">
      <animateTransform attributeName="transform" type="rotate" values="0 220 500; 360 220 500" dur="20s" repeatCount="indefinite"/>
    </polygon>
    
    <polygon points="800,150 825,130 850,150 825,170" fill="#67c23a">
      <animateTransform attributeName="transform" type="rotate" values="0 825 150; -360 825 150" dur="25s" repeatCount="indefinite"/>
    </polygon>
  </g>
</svg>
