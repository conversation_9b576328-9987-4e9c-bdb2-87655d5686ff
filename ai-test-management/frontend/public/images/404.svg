<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 400">
  <defs>
    <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff7875;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff9c6e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="500" height="400" fill="#fafafa"/>
  
  <!-- 404 Text -->
  <g transform="translate(250, 150)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="80" font-weight="bold" fill="url(#errorGradient)" text-anchor="middle">404</text>
  </g>
  
  <!-- Broken robot/AI -->
  <g transform="translate(200, 220)">
    <!-- Robot body -->
    <rect x="20" y="20" width="60" height="80" fill="#e4e7ed" stroke="#dcdfe6" stroke-width="2" rx="10"/>
    
    <!-- Robot head -->
    <rect x="30" y="0" width="40" height="30" fill="#f5f7fa" stroke="#dcdfe6" stroke-width="2" rx="15"/>
    
    <!-- Broken eye (X) -->
    <g transform="translate(40, 10)">
      <line x1="0" y1="0" x2="8" y2="8" stroke="#f56c6c" stroke-width="2"/>
      <line x1="8" y1="0" x2="0" y2="8" stroke="#f56c6c" stroke-width="2"/>
    </g>
    
    <!-- Normal eye -->
    <circle cx="60" cy="14" r="4" fill="#409eff"/>
    
    <!-- Sad mouth -->
    <path d="M 40 20 Q 50 25 60 20" stroke="#909399" stroke-width="2" fill="none"/>
    
    <!-- Arms -->
    <rect x="5" y="35" width="20" height="8" fill="#e4e7ed" stroke="#dcdfe6" stroke-width="1" rx="4"/>
    <rect x="75" y="35" width="20" height="8" fill="#e4e7ed" stroke="#dcdfe6" stroke-width="1" rx="4"/>
    
    <!-- Legs -->
    <rect x="30" y="95" width="12" height="25" fill="#e4e7ed" stroke="#dcdfe6" stroke-width="1" rx="6"/>
    <rect x="58" y="95" width="12" height="25" fill="#e4e7ed" stroke="#dcdfe6" stroke-width="1" rx="6"/>
    
    <!-- Error sparks -->
    <g opacity="0.7">
      <path d="M 15 15 L 20 10 L 15 5 L 10 10 Z" fill="#f56c6c">
        <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite"/>
      </path>
      <path d="M 85 25 L 90 20 L 85 15 L 80 20 Z" fill="#e6a23c">
        <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite"/>
      </path>
      <path d="M 45 -5 L 50 -10 L 45 -15 L 40 -10 Z" fill="#f56c6c">
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
      </path>
    </g>
  </g>
  
  <!-- Floating question marks -->
  <g opacity="0.3">
    <text x="100" y="100" font-family="Arial, sans-serif" font-size="24" fill="#c0c4cc">?</text>
    <text x="380" y="120" font-family="Arial, sans-serif" font-size="20" fill="#c0c4cc">?</text>
    <text x="150" y="350" font-family="Arial, sans-serif" font-size="18" fill="#c0c4cc">?</text>
    <text x="350" y="320" font-family="Arial, sans-serif" font-size="22" fill="#c0c4cc">?</text>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="80" cy="80" r="3" fill="#e4e7ed" opacity="0.5"/>
  <circle cx="420" cy="100" r="2" fill="#e4e7ed" opacity="0.5"/>
  <circle cx="450" cy="300" r="4" fill="#e4e7ed" opacity="0.5"/>
  <circle cx="50" cy="350" r="2.5" fill="#e4e7ed" opacity="0.5"/>
</svg>
