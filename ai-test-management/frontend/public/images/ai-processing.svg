<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Central brain/AI icon -->
  <g transform="translate(100, 100)">
    <!-- Main circle -->
    <circle cx="0" cy="0" r="40" fill="url(#aiGradient)" filter="url(#glow)">
      <animate attributeName="r" values="40;45;40" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- AI text -->
    <text x="0" y="8" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white" text-anchor="middle">AI</text>
    
    <!-- Neural network connections -->
    <g opacity="0.7">
      <!-- Nodes -->
      <circle cx="-60" cy="-30" r="4" fill="#409eff">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0s"/>
      </circle>
      <circle cx="-60" cy="0" r="4" fill="#67c23a">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>
      </circle>
      <circle cx="-60" cy="30" r="4" fill="#e6a23c">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
      </circle>
      
      <circle cx="60" cy="-30" r="4" fill="#f56c6c">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.9s"/>
      </circle>
      <circle cx="60" cy="0" r="4" fill="#909399">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="1.2s"/>
      </circle>
      <circle cx="60" cy="30" r="4" fill="#409eff">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="1.5s"/>
      </circle>
      
      <!-- Connections -->
      <line x1="-60" y1="-30" x2="-40" y2="-15" stroke="#409eff" stroke-width="2" opacity="0.5">
        <animate attributeName="opacity" values="0.1;0.8;0.1" dur="1.5s" repeatCount="indefinite" begin="0s"/>
      </line>
      <line x1="-60" y1="0" x2="-40" y2="0" stroke="#67c23a" stroke-width="2" opacity="0.5">
        <animate attributeName="opacity" values="0.1;0.8;0.1" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>
      </line>
      <line x1="-60" y1="30" x2="-40" y2="15" stroke="#e6a23c" stroke-width="2" opacity="0.5">
        <animate attributeName="opacity" values="0.1;0.8;0.1" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
      </line>
      
      <line x1="40" y1="-15" x2="60" y2="-30" stroke="#f56c6c" stroke-width="2" opacity="0.5">
        <animate attributeName="opacity" values="0.1;0.8;0.1" dur="1.5s" repeatCount="indefinite" begin="0.9s"/>
      </line>
      <line x1="40" y1="0" x2="60" y2="0" stroke="#909399" stroke-width="2" opacity="0.5">
        <animate attributeName="opacity" values="0.1;0.8;0.1" dur="1.5s" repeatCount="indefinite" begin="1.2s"/>
      </line>
      <line x1="40" y1="15" x2="60" y2="30" stroke="#409eff" stroke-width="2" opacity="0.5">
        <animate attributeName="opacity" values="0.1;0.8;0.1" dur="1.5s" repeatCount="indefinite" begin="1.5s"/>
      </line>
    </g>
    
    <!-- Rotating outer ring -->
    <circle cx="0" cy="0" r="55" fill="none" stroke="url(#aiGradient)" stroke-width="2" stroke-dasharray="10,5" opacity="0.6">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Data flow particles -->
    <g opacity="0.8">
      <circle cx="0" cy="-70" r="2" fill="#409eff">
        <animateTransform attributeName="transform" type="rotate" values="0;360" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="-70" r="2" fill="#67c23a">
        <animateTransform attributeName="transform" type="rotate" values="120;480" dur="3s" repeatCount="indefinite"/>
      </circle>
      <circle cx="0" cy="-70" r="2" fill="#e6a23c">
        <animateTransform attributeName="transform" type="rotate" values="240;600" dur="3s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
</svg>
