<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="url(#bgGradient)"/>
  <rect width="1200" height="800" fill="url(#grid)"/>
  
  <!-- Floating shapes -->
  <circle cx="100" cy="100" r="30" fill="rgba(255,255,255,0.1)" opacity="0.8">
    <animateTransform attributeName="transform" type="translate" values="0,0; 20,10; 0,0" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="1000" cy="200" r="40" fill="rgba(255,255,255,0.08)" opacity="0.6">
    <animateTransform attributeName="transform" type="translate" values="0,0; -15,20; 0,0" dur="8s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="200" cy="600" r="25" fill="rgba(255,255,255,0.12)" opacity="0.7">
    <animateTransform attributeName="transform" type="translate" values="0,0; 10,-15; 0,0" dur="7s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="900" cy="700" r="35" fill="rgba(255,255,255,0.09)" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" values="0,0; -20,-10; 0,0" dur="9s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Tech elements -->
  <g opacity="0.3">
    <rect x="50" y="300" width="80" height="4" fill="rgba(255,255,255,0.2)" rx="2"/>
    <rect x="50" y="320" width="60" height="4" fill="rgba(255,255,255,0.15)" rx="2"/>
    <rect x="50" y="340" width="100" height="4" fill="rgba(255,255,255,0.1)" rx="2"/>
  </g>
  
  <g opacity="0.2">
    <rect x="1050" y="400" width="100" height="4" fill="rgba(255,255,255,0.2)" rx="2"/>
    <rect x="1050" y="420" width="80" height="4" fill="rgba(255,255,255,0.15)" rx="2"/>
    <rect x="1050" y="440" width="120" height="4" fill="rgba(255,255,255,0.1)" rx="2"/>
  </g>
</svg>
