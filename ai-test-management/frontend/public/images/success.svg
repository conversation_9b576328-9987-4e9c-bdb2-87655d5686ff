<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 300">
  <defs>
    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#67c23a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#85ce61;stop-opacity:1" />
    </linearGradient>
    <filter id="successGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="300" fill="#f0f9ff"/>
  
  <!-- Success circle -->
  <circle cx="150" cy="150" r="80" fill="url(#successGradient)" filter="url(#successGlow)">
    <animate attributeName="r" values="75;85;75" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Checkmark -->
  <g transform="translate(150, 150)">
    <path d="M -25 -5 L -10 10 L 25 -25" stroke="white" stroke-width="8" fill="none" stroke-linecap="round" stroke-linejoin="round">
      <animate attributeName="stroke-dasharray" values="0,100;60,100" dur="1s" fill="freeze"/>
      <animate attributeName="stroke-dashoffset" values="0;0" dur="1s" fill="freeze"/>
    </path>
  </g>
  
  <!-- Celebration particles -->
  <g opacity="0.8">
    <!-- Confetti -->
    <rect x="100" y="50" width="4" height="4" fill="#409eff" transform="rotate(45 102 52)">
      <animateTransform attributeName="transform" type="translate" values="0,0; 20,100; 0,200" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;1;0" dur="3s" repeatCount="indefinite"/>
    </rect>
    
    <rect x="200" y="60" width="4" height="4" fill="#e6a23c" transform="rotate(45 202 62)">
      <animateTransform attributeName="transform" type="translate" values="0,0; -15,120; 0,240" dur="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;1;0" dur="3.5s" repeatCount="indefinite"/>
    </rect>
    
    <circle cx="80" cy="80" r="2" fill="#f56c6c">
      <animateTransform attributeName="transform" type="translate" values="0,0; 30,150; 0,300" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;1;0" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="220" cy="70" r="2" fill="#67c23a">
      <animateTransform attributeName="transform" type="translate" values="0,0; -25,140; 0,280" dur="3.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;1;0" dur="3.8s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Stars -->
    <g transform="translate(100, 100)">
      <path d="M 0 -8 L 2 -2 L 8 -2 L 3 2 L 5 8 L 0 4 L -5 8 L -3 2 L -8 -2 L -2 -2 Z" fill="#409eff">
        <animateTransform attributeName="transform" type="rotate" values="0;360" dur="4s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <g transform="translate(200, 200)">
      <path d="M 0 -6 L 1.5 -1.5 L 6 -1.5 L 2.25 1.5 L 3.75 6 L 0 3 L -3.75 6 L -2.25 1.5 L -6 -1.5 L -1.5 -1.5 Z" fill="#e6a23c">
        <animateTransform attributeName="transform" type="rotate" values="0;-360" dur="5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.3;1;0.3" dur="2.5s" repeatCount="indefinite"/>
      </path>
    </g>
  </g>
  
  <!-- Ripple effect -->
  <circle cx="150" cy="150" r="80" fill="none" stroke="#67c23a" stroke-width="2" opacity="0.3">
    <animate attributeName="r" values="80;120;160" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.3;0.1;0" dur="2s" repeatCount="indefinite"/>
  </circle>
</svg>
