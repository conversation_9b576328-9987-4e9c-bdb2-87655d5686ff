<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300">
  <defs>
    <linearGradient id="emptyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f4fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#emptyGradient)"/>
  
  <!-- Empty box illustration -->
  <g transform="translate(150, 80)">
    <!-- Box -->
    <rect x="0" y="40" width="100" height="80" fill="#f5f7fa" stroke="#e4e7ed" stroke-width="2" rx="8"/>
    
    <!-- Box lid -->
    <path d="M 0 40 L 20 20 L 120 20 L 100 40 Z" fill="#ebeef5" stroke="#e4e7ed" stroke-width="2"/>
    
    <!-- Box side -->
    <path d="M 100 40 L 120 20 L 120 100 L 100 120 Z" fill="#e4e7ed" stroke="#dcdfe6" stroke-width="2"/>
    
    <!-- Empty indicator -->
    <circle cx="50" cy="80" r="15" fill="none" stroke="#c0c4cc" stroke-width="2" stroke-dasharray="5,5">
      <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Question mark -->
    <text x="50" y="87" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#909399" text-anchor="middle">?</text>
  </g>
  
  <!-- Floating dots -->
  <circle cx="100" cy="100" r="3" fill="#c0c4cc" opacity="0.5">
    <animate attributeName="cy" values="100;90;100" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="180" r="2" fill="#c0c4cc" opacity="0.3">
    <animate attributeName="cy" values="180;170;180" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="80" cy="220" r="2.5" fill="#c0c4cc" opacity="0.4">
    <animate attributeName="cy" values="220;210;220" dur="3.5s" repeatCount="indefinite"/>
  </circle>
</svg>
