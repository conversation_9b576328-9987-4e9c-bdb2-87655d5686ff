# 图片资源说明

本目录包含了AI测试管理系统前端使用的各种图片资源。

## 文件列表

### 背景图片
- `login-bg.svg` - 登录页面背景图，包含动画效果的科技风格背景
- `dashboard-bg.svg` - 仪表盘页面背景图，护眼的绿色主题背景

### 状态插图
- `empty-state.svg` - 空状态插图，用于显示暂无数据的页面
- `404.svg` - 404错误页面插图，包含损坏的机器人图案
- `success.svg` - 成功状态插图，包含庆祝动画效果

### AI相关图标
- `ai-processing.svg` - AI处理中的动画图标，用于显示AI分析进度

## 使用方式

### 在Vue组件中使用
```vue
<template>
  <img src="/images/ai-processing.svg" alt="AI处理中" class="ai-icon">
</template>
```

### 作为CSS背景使用
```css
.login-container {
  background-image: url('/images/login-bg.svg');
  background-size: cover;
  background-position: center;
}
```

## 图片特点

1. **SVG格式** - 所有图片都使用SVG格式，确保在不同分辨率下都能保持清晰
2. **动画效果** - 部分图片包含CSS动画，提升用户体验
3. **主题一致** - 所有图片都采用统一的色彩主题，与系统整体设计保持一致
4. **轻量化** - 优化的SVG代码，确保快速加载

## 色彩主题

- 主色调：#667eea 到 #764ba2 的渐变
- 成功色：#67c23a
- 警告色：#e6a23c
- 错误色：#f56c6c
- 信息色：#409eff

## 自定义修改

如需修改图片，可以：
1. 直接编辑SVG代码
2. 使用设计工具（如Figma、Sketch）导出新的SVG
3. 保持与系统主题色彩的一致性

## 浏览器兼容性

所有SVG图片都兼容现代浏览器：
- Chrome 4+
- Firefox 4+
- Safari 4+
- Edge 12+
- IE 9+
