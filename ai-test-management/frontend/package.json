{"name": "ai-test-management-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "^10.3.0", "axios": "^1.4.0", "echarts": "^5.4.3", "element-plus": "^2.3.8", "nprogress": "^0.2.0", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-echarts": "^6.6.1", "vue-router": "^4.2.4"}, "devDependencies": {"@types/node": "^20.4.5", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-vue": "^4.3.1", "@vue/eslint-config-typescript": "^11.0.3", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "sass": "^1.89.1", "typescript": "^5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}}