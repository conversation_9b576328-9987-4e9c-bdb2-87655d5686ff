-- AI测试管理系统数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ai_test_db;

-- 使用数据库
\c ai_test_db;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建项目表
CREATE TABLE IF NOT EXISTS projects (
    id SERIAL PRIMARY KEY,
    project_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) UNIQUE NOT NULL,
    description TEXT,
    creator_id INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建需求表
CREATE TABLE IF NOT EXISTS requirements (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(20) NOT NULL CHECK (category IN ('功能', '性能', '安全', '接口', '体验', '改进', '其它')),
    parent VARCHAR(200),
    module VARCHAR(100),
    level VARCHAR(10) DEFAULT 'BR' CHECK (level IN ('BR')),
    reviewer VARCHAR(100) NOT NULL,
    estimated INTEGER DEFAULT 8,
    criteria TEXT,
    remark TEXT,
    keywords VARCHAR(500),
    project_id INTEGER NOT NULL REFERENCES projects(id),
    reviewer_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建测试用例表
CREATE TABLE IF NOT EXISTS testcases (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    priority VARCHAR(10) NOT NULL CHECK (priority IN ('高', '中', '低')),
    status VARCHAR(20) DEFAULT '未开始' CHECK (status IN ('未开始', '进行中', '通过', '失败', '阻塞')),
    test_type VARCHAR(20) NOT NULL CHECK (test_type IN ('单元测试', '接口测试', '功能测试', '性能测试', '安全测试')),
    preconditions TEXT,
    postconditions TEXT,
    creator VARCHAR(100) NOT NULL,
    requirement_id INTEGER NOT NULL REFERENCES requirements(id),
    project_id INTEGER NOT NULL REFERENCES projects(id),
    creator_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建测试步骤表
CREATE TABLE IF NOT EXISTS test_steps (
    id SERIAL PRIMARY KEY,
    step_number INTEGER NOT NULL,
    description TEXT NOT NULL,
    expected_result TEXT NOT NULL,
    testcase_id INTEGER NOT NULL REFERENCES testcases(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_projects_creator_id ON projects(creator_id);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_requirements_project_id ON requirements(project_id);
CREATE INDEX IF NOT EXISTS idx_requirements_reviewer_id ON requirements(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_requirements_created_at ON requirements(created_at);
CREATE INDEX IF NOT EXISTS idx_testcases_requirement_id ON testcases(requirement_id);
CREATE INDEX IF NOT EXISTS idx_testcases_project_id ON testcases(project_id);
CREATE INDEX IF NOT EXISTS idx_testcases_creator_id ON testcases(creator_id);
CREATE INDEX IF NOT EXISTS idx_testcases_created_at ON testcases(created_at);
CREATE INDEX IF NOT EXISTS idx_test_steps_testcase_id ON test_steps(testcase_id);

-- 插入默认用户数据
INSERT INTO users (username, email, hashed_password, full_name, is_superuser, is_active) 
VALUES 
    ('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Caa', '系统管理员', TRUE, TRUE),
    ('tester', '<EMAIL>', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '测试工程师', FALSE, TRUE)
ON CONFLICT (username) DO NOTHING;

-- 插入示例项目数据
INSERT INTO projects (project_code, name, description, creator_id)
VALUES 
    ('DEMO001', '演示项目', '这是一个演示项目，用于展示系统功能', 1),
    ('TEST001', '测试项目', '用于测试各种功能的项目', 2)
ON CONFLICT (project_code) DO NOTHING;

-- 插入示例需求数据
INSERT INTO requirements (name, description, category, reviewer, estimated, project_id, reviewer_id)
VALUES 
    ('用户登录功能', '用户可以通过用户名和密码登录系统，支持记住密码功能', '功能', '张三', 8, 1, 1),
    ('数据展示功能', '系统能够以图表形式展示各种统计数据，支持多种图表类型', '功能', '李四', 16, 1, 2),
    ('系统性能优化', '优化系统响应速度，确保页面加载时间不超过2秒', '性能', '王五', 24, 1, 1)
ON CONFLICT DO NOTHING;

-- 插入示例测试用例数据
INSERT INTO testcases (title, description, priority, test_type, creator, requirement_id, project_id, creator_id)
VALUES 
    ('验证用户正常登录', '验证用户使用正确的用户名和密码能够成功登录系统', '高', '功能测试', '测试员A', 1, 1, 2),
    ('验证用户密码错误登录失败', '验证用户使用错误密码无法登录系统并显示错误提示', '高', '功能测试', '测试员A', 1, 1, 2),
    ('验证数据图表正常显示', '验证系统能够正确显示各种统计图表', '中', '功能测试', '测试员B', 2, 1, 2)
ON CONFLICT DO NOTHING;

-- 插入示例测试步骤数据
INSERT INTO test_steps (step_number, description, expected_result, testcase_id)
VALUES 
    (1, '打开登录页面', '页面正常显示登录表单', 1),
    (2, '输入正确的用户名和密码', '输入框正常显示内容', 1),
    (3, '点击登录按钮', '成功跳转到主页面并显示用户信息', 1),
    (1, '打开登录页面', '页面正常显示登录表单', 2),
    (2, '输入正确用户名和错误密码', '输入框正常显示内容', 2),
    (3, '点击登录按钮', '显示密码错误提示信息', 2)
ON CONFLICT DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为各表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_requirements_updated_at BEFORE UPDATE ON requirements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_testcases_updated_at BEFORE UPDATE ON testcases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
