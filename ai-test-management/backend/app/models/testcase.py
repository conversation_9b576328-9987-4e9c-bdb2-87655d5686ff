from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum


class TestCasePriority(str, enum.Enum):
    HIGH = "高"
    MEDIUM = "中"
    LOW = "低"


class TestCaseStatus(str, enum.Enum):
    NOT_STARTED = "未开始"
    IN_PROGRESS = "进行中"
    PASSED = "通过"
    FAILED = "失败"
    BLOCKED = "阻塞"


class TestCaseType(str, enum.Enum):
    UNIT = "单元测试"
    INTERFACE = "接口测试"
    FUNCTIONAL = "功能测试"
    PERFORMANCE = "性能测试"
    SECURITY = "安全测试"


class TestCase(Base):
    __tablename__ = "testcases"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    priority = Column(Enum(TestCasePriority), nullable=False)
    status = Column(Enum(TestCaseStatus), default=TestCaseStatus.NOT_STARTED)
    test_type = Column(Enum(TestCaseType), nullable=False)
    preconditions = Column(Text, nullable=True)
    postconditions = Column(Text, nullable=True)
    creator = Column(String(100), nullable=False)
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=False)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    requirement = relationship("Requirement", back_populates="testcases")
    project = relationship("Project", back_populates="testcases")
    creator_user = relationship("User", back_populates="testcases")
    steps = relationship("TestStep", back_populates="testcase", cascade="all, delete-orphan")


class TestStep(Base):
    __tablename__ = "test_steps"
    
    id = Column(Integer, primary_key=True, index=True)
    step_number = Column(Integer, nullable=False)
    description = Column(Text, nullable=False)
    expected_result = Column(Text, nullable=False)
    testcase_id = Column(Integer, ForeignKey("testcases.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    testcase = relationship("TestCase", back_populates="steps")
