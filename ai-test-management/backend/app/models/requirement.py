from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base
import enum


class RequirementCategory(str, enum.Enum):
    FUNCTIONAL = "功能"
    PERFORMANCE = "性能"
    SECURITY = "安全"
    INTERFACE = "接口"
    EXPERIENCE = "体验"
    IMPROVEMENT = "改进"
    OTHER = "其它"


class RequirementLevel(str, enum.Enum):
    BR = "BR"  # Business Requirement


class Requirement(Base):
    __tablename__ = "requirements"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=False)
    category = Column(Enum(RequirementCategory), nullable=False)
    parent = Column(String(200), nullable=True)
    module = Column(String(100), nullable=True)
    level = Column(Enum(RequirementLevel), default=RequirementLevel.BR)
    reviewer = Column(String(100), nullable=False)
    estimated = Column(Integer, default=8)
    criteria = Column(Text, nullable=True)
    remark = Column(Text, nullable=True)
    keywords = Column(String(500), nullable=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    project = relationship("Project", back_populates="requirements")
    reviewer_user = relationship("User", back_populates="requirements")
    testcases = relationship("TestCase", back_populates="requirement", cascade="all, delete-orphan")
