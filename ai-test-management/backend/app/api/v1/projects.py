from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...controllers.project import project_controller
from ...schemas.project import Project, ProjectCreate, ProjectUpdate, ProjectWithStats
from .auth import get_current_user
from ...schemas.user import User

router = APIRouter()


@router.get("/", response_model=List[Project])
def read_projects(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user),
) -> Any:
    """获取项目列表"""
    projects = project_controller.get_multi_with_search(
        db, skip=skip, limit=limit, search=search
    )
    return projects


@router.post("/", response_model=Project)
def create_project(
    *,
    db: Session = Depends(get_db),
    project_in: ProjectCreate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """创建项目"""
    # 检查项目名称是否已存在
    existing_project = project_controller.get_by_name(db, name=project_in.name)
    if existing_project:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="项目名称已存在"
        )
    
    # 检查项目编号是否已存在
    existing_code = project_controller.get_by_code(db, project_code=project_in.project_code)
    if existing_code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="项目编号已存在"
        )
    
    project = project_controller.create_with_creator(
        db, obj_in=project_in, creator_id=current_user.id
    )
    return project


@router.get("/{project_id}", response_model=Project)
def read_project(
    *,
    db: Session = Depends(get_db),
    project_id: int,
    current_user: User = Depends(get_current_user),
) -> Any:
    """获取项目详情"""
    project = project_controller.get(db, id=project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    return project


@router.put("/{project_id}", response_model=Project)
def update_project(
    *,
    db: Session = Depends(get_db),
    project_id: int,
    project_in: ProjectUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """更新项目"""
    project = project_controller.get(db, id=project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    
    # 检查权限（只有创建者或超级用户可以修改）
    if project.creator_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此项目"
        )
    
    # 如果要修改项目名称，检查是否重复
    if project_in.name and project_in.name != project.name:
        existing_project = project_controller.get_by_name(db, name=project_in.name)
        if existing_project:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="项目名称已存在"
            )
    
    # 如果要修改项目编号，检查是否重复
    if project_in.project_code and project_in.project_code != project.project_code:
        existing_code = project_controller.get_by_code(db, project_code=project_in.project_code)
        if existing_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="项目编号已存在"
            )
    
    project = project_controller.update(db, db_obj=project, obj_in=project_in)
    return project


@router.delete("/{project_id}")
def delete_project(
    *,
    db: Session = Depends(get_db),
    project_id: int,
    current_user: User = Depends(get_current_user),
) -> Any:
    """删除项目"""
    project = project_controller.get(db, id=project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    
    # 检查权限（只有创建者或超级用户可以删除）
    if project.creator_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此项目"
        )
    
    project_controller.remove(db, id=project_id)
    return {"message": "项目删除成功"}
