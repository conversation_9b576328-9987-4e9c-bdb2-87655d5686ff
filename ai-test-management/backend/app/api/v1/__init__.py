from fastapi import APIRouter

from .auth import router as auth_router
from .users import router as users_router
from .projects import router as projects_router
from .requirements import router as requirements_router
from .testcases import router as testcases_router
from .dashboard import router as dashboard_router
from .websocket import router as websocket_router

api_router = APIRouter()

api_router.include_router(auth_router, prefix="/auth", tags=["authentication"])
api_router.include_router(users_router, prefix="/users", tags=["users"])
api_router.include_router(projects_router, prefix="/projects", tags=["projects"])
api_router.include_router(requirements_router, prefix="/requirements", tags=["requirements"])
api_router.include_router(testcases_router, prefix="/testcases", tags=["testcases"])
api_router.include_router(dashboard_router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(websocket_router, prefix="/ws", tags=["websocket"])
