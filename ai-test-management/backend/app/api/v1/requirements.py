from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
import os

from ...core.database import get_db
from ...core.config import settings
from ...controllers.requirement import requirement_controller
from ...schemas.requirement import Requirement, RequirementCreate, RequirementUpdate
from ...agents.utils import save_uploaded_file, get_file_extension
from .auth import get_current_user
from ...schemas.user import User

router = APIRouter()


@router.get("/", response_model=List[Requirement])
def read_requirements(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="搜索关键词"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    current_user: User = Depends(get_current_user),
) -> Any:
    """获取需求列表"""
    requirements = requirement_controller.get_multi_with_search(
        db, skip=skip, limit=limit, search=search, project_id=project_id
    )
    return requirements


@router.post("/", response_model=Requirement)
def create_requirement(
    *,
    db: Session = Depends(get_db),
    requirement_in: RequirementCreate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """创建需求"""
    requirement = requirement_controller.create(db, obj_in=requirement_in)
    return requirement


@router.get("/{requirement_id}", response_model=Requirement)
def read_requirement(
    *,
    db: Session = Depends(get_db),
    requirement_id: int,
    current_user: User = Depends(get_current_user),
) -> Any:
    """获取需求详情"""
    requirement = requirement_controller.get(db, id=requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="需求不存在"
        )
    return requirement


@router.put("/{requirement_id}", response_model=Requirement)
def update_requirement(
    *,
    db: Session = Depends(get_db),
    requirement_id: int,
    requirement_in: RequirementUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """更新需求"""
    requirement = requirement_controller.get(db, id=requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="需求不存在"
        )
    
    requirement = requirement_controller.update(db, db_obj=requirement, obj_in=requirement_in)
    return requirement


@router.delete("/{requirement_id}")
def delete_requirement(
    *,
    db: Session = Depends(get_db),
    requirement_id: int,
    current_user: User = Depends(get_current_user),
) -> Any:
    """删除需求"""
    requirement = requirement_controller.get(db, id=requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="需求不存在"
        )
    
    requirement_controller.remove(db, id=requirement_id)
    return {"message": "需求删除成功"}


@router.post("/upload")
async def upload_requirement_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
) -> Any:
    """上传需求文档"""
    # 检查文件类型
    file_ext = get_file_extension(file.filename)
    if file_ext not in ['.txt', '.docx']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只支持 .txt 和 .docx 文件格式"
        )
    
    # 检查文件大小
    content = await file.read()
    if len(content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件大小不能超过 {settings.MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # 保存文件
    try:
        file_path = await save_uploaded_file(content, file.filename, settings.UPLOAD_DIR)
        return {
            "message": "文件上传成功",
            "file_path": file_path,
            "filename": file.filename
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件保存失败: {str(e)}"
        )
