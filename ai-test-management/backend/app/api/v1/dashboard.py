from typing import Any
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func

from ...core.database import get_db
from ...models.project import Project
from ...models.requirement import Requirement
from ...models.testcase import TestCase
from .auth import get_current_user
from ...schemas.user import User

router = APIRouter()


@router.get("/stats")
def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """获取仪表盘统计数据"""
    
    # 项目统计
    total_projects = db.query(func.count(Project.id)).scalar()
    
    # 需求统计
    total_requirements = db.query(func.count(Requirement.id)).scalar()
    requirement_by_category = db.query(
        Requirement.category,
        func.count(Requirement.id).label('count')
    ).group_by(Requirement.category).all()
    
    # 测试用例统计
    total_testcases = db.query(func.count(TestCase.id)).scalar()
    testcase_by_status = db.query(
        TestCase.status,
        func.count(TestCase.id).label('count')
    ).group_by(TestCase.status).all()
    
    testcase_by_priority = db.query(
        TestCase.priority,
        func.count(TestCase.id).label('count')
    ).group_by(TestCase.priority).all()
    
    testcase_by_type = db.query(
        TestCase.test_type,
        func.count(TestCase.id).label('count')
    ).group_by(TestCase.test_type).all()
    
    # 最近项目
    recent_projects = db.query(Project).order_by(Project.created_at.desc()).limit(5).all()
    
    return {
        "overview": {
            "total_projects": total_projects,
            "total_requirements": total_requirements,
            "total_testcases": total_testcases,
            "total_defects": 0  # 暂时为0，后续添加缺陷管理
        },
        "requirement_stats": {
            "by_category": [{"category": item[0], "count": item[1]} for item in requirement_by_category]
        },
        "testcase_stats": {
            "by_status": [{"status": item[0], "count": item[1]} for item in testcase_by_status],
            "by_priority": [{"priority": item[0], "count": item[1]} for item in testcase_by_priority],
            "by_type": [{"type": item[0], "count": item[1]} for item in testcase_by_type]
        },
        "recent_projects": [
            {
                "id": project.id,
                "name": project.name,
                "project_code": project.project_code,
                "created_at": project.created_at
            } for project in recent_projects
        ]
    }
