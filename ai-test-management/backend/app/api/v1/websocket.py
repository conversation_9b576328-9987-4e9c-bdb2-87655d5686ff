from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from typing import Dict, List
import json
import asyncio
from ...agents.requirement_agents import start_runtime as start_requirement_runtime, RequirementFilesMessage, ResponseMessage
from ...agents.testcase_agents import start_runtime as start_testcase_runtime, RequirementMessage as TestCaseRequirementMessage
from autogen_core import MessageContext, ClosureContext

router = APIRouter()

# 存储WebSocket连接
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理不同类型的消息
            if message_data.get("type") == "requirement_analysis":
                await handle_requirement_analysis(websocket, message_data)
            elif message_data.get("type") == "testcase_generation":
                await handle_testcase_generation(websocket, message_data)
            else:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Unknown message type"
                }))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)


async def handle_requirement_analysis(websocket: WebSocket, message_data: dict):
    """处理需求分析请求"""
    try:
        # 创建需求分析消息
        requirement_files = RequirementFilesMessage(
            user_id=message_data.get("user_id", "default"),
            files=message_data.get("files", []),
            content=message_data.get("content", ""),
            task=message_data.get("task", "分析需求文档")
        )
        
        # 定义结果收集函数
        async def collect_result(ctx: ClosureContext, message: ResponseMessage, msg_ctx: MessageContext):
            await websocket.send_text(json.dumps({
                "type": "requirement_analysis_result",
                "source": message.source,
                "content": message.content,
                "is_final": message.is_final
            }))
        
        # 启动需求分析运行时
        await start_requirement_runtime(
            requirement_files,
            collect_result,
            user_input_func=None  # 暂时不支持用户交互
        )
        
    except Exception as e:
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": f"需求分析失败: {str(e)}"
        }))


async def handle_testcase_generation(websocket: WebSocket, message_data: dict):
    """处理测试用例生成请求"""
    try:
        # 创建测试用例生成消息
        requirement_message = TestCaseRequirementMessage(
            id=message_data.get("requirement_id"),
            name=message_data.get("requirement_name", ""),
            description=message_data.get("requirement_description", ""),
            project_id=message_data.get("project_id"),
            reviewer=message_data.get("reviewer", "系统"),
            scenario=message_data.get("scenario", ""),
            task=message_data.get("task", "生成测试用例")
        )
        
        # 定义结果收集函数
        async def collect_result(ctx: ClosureContext, message: ResponseMessage, msg_ctx: MessageContext):
            await websocket.send_text(json.dumps({
                "type": "testcase_generation_result",
                "source": message.source,
                "content": message.content,
                "is_final": message.is_final
            }))
        
        # 启动测试用例生成运行时
        await start_testcase_runtime(
            requirement_message,
            collect_result,
            user_input_func=None  # 暂时不支持用户交互
        )
        
    except Exception as e:
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": f"测试用例生成失败: {str(e)}"
        }))
