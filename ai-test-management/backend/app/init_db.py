"""
初始化数据库脚本
创建默认用户和示例数据
"""
from sqlalchemy.orm import Session
from .core.database import SessionLocal, engine
from .models import Base
from .controllers.user import user_controller
from .schemas.user import UserCreate


def init_db() -> None:
    """初始化数据库"""
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    try:
        # 检查是否已有超级用户
        user = user_controller.get_by_username(db, username="admin")
        if not user:
            # 创建默认超级用户
            user_in = UserCreate(
                username="admin",
                email="<EMAIL>",
                password="admin123",
                full_name="系统管理员",
                is_superuser=True,
                is_active=True
            )
            user = user_controller.create(db, obj_in=user_in)
            print(f"创建超级用户: {user.username}")
        
        # 创建测试用户
        test_user = user_controller.get_by_username(db, username="tester")
        if not test_user:
            user_in = UserCreate(
                username="tester",
                email="<EMAIL>",
                password="test123",
                full_name="测试工程师",
                is_superuser=False,
                is_active=True
            )
            test_user = user_controller.create(db, obj_in=user_in)
            print(f"创建测试用户: {test_user.username}")
            
    finally:
        db.close()


if __name__ == "__main__":
    print("初始化数据库...")
    init_db()
    print("数据库初始化完成!")
