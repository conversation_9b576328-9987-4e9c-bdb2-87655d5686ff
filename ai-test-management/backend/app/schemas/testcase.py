from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from ..models.testcase import TestCasePriority, TestCaseStatus, TestCaseType


class TestStepBase(BaseModel):
    description: str
    expected_result: str


class TestStepCreate(TestStepBase):
    step_number: int
    testcase_id: int


class TestStep(TestStepBase):
    id: int
    step_number: int
    testcase_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class TestCaseBase(BaseModel):
    title: str
    description: Optional[str] = None
    priority: TestCasePriority
    status: TestCaseStatus = TestCaseStatus.NOT_STARTED
    test_type: TestCaseType
    preconditions: Optional[str] = None
    postconditions: Optional[str] = None
    creator: str
    requirement_id: int
    project_id: int


class TestCaseCreate(TestCaseBase):
    steps: Optional[List[TestStepBase]] = None


class TestCaseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[TestCasePriority] = None
    status: Optional[TestCaseStatus] = None
    test_type: Optional[TestCaseType] = None
    preconditions: Optional[str] = None
    postconditions: Optional[str] = None


class TestCase(TestCaseBase):
    id: int
    creator_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    steps: List[TestStep] = []

    class Config:
        from_attributes = True
