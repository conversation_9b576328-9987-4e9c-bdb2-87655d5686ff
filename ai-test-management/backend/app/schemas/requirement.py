from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from ..models.requirement import RequirementCategory, RequirementLevel


class RequirementBase(BaseModel):
    name: str
    description: str
    category: RequirementCategory
    parent: Optional[str] = None
    module: Optional[str] = None
    level: RequirementLevel = RequirementLevel.BR
    reviewer: str
    estimated: int = 8
    criteria: Optional[str] = None
    remark: Optional[str] = None
    keywords: Optional[str] = None
    project_id: int


class RequirementCreate(RequirementBase):
    pass


class RequirementUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[RequirementCategory] = None
    parent: Optional[str] = None
    module: Optional[str] = None
    level: Optional[RequirementLevel] = None
    reviewer: Optional[str] = None
    estimated: Optional[int] = None
    criteria: Optional[str] = None
    remark: Optional[str] = None
    keywords: Optional[str] = None


class Requirement(RequirementBase):
    id: int
    reviewer_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class RequirementSelect(BaseModel):
    id: int
    name: str
    description: str
    project_id: int
    reviewer: str

    class Config:
        from_attributes = True
