from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime


class ProjectBase(BaseModel):
    project_code: str
    name: str
    description: Optional[str] = None


class ProjectCreate(ProjectBase):
    pass


class ProjectUpdate(BaseModel):
    project_code: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None


class Project(ProjectBase):
    id: int
    creator_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ProjectWithStats(Project):
    requirement_count: int = 0
    testcase_count: int = 0
    defect_count: int = 0
