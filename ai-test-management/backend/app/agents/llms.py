from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
import os
import logging
from ..core.config import settings

# 配置日志
logger = logging.getLogger("model_client")

try:
    model_client = OpenAIChatCompletionClient(
        model=settings.LLM_MODEL,
        base_url=settings.LLM_API_BASE,
        api_key=settings.LLM_API_KEY,
        max_retries=settings.LLM_MAX_RETRIES,
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": False,
            "family": ModelFamily.UNKNOWN,
        },
    )
    
    model_client_json = OpenAIChatCompletionClient(
        model=settings.LLM_MODEL,
        base_url=settings.LLM_API_BASE,
        api_key=settings.LLM_API_KEY,
        max_retries=settings.LLM_MAX_RETRIES,
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
        },
    )
    
    logger.info(f"初始化模型客户端成功: {settings.LLM_MODEL}, API Base: {settings.LLM_API_BASE}")
except Exception as e:
    logger.error(f"初始化模型客户端失败: {str(e)}")
    raise
