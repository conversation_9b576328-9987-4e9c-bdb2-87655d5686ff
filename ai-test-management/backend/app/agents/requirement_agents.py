import json
from dataclasses import dataclass
from typing import Callable, Optional, Awaitable, Any

from autogen_agentchat.agents import Assistant<PERSON>gent, UserProxyAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage, UserInputRequestedEvent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import RoutedAgent, type_subscription, message_handler, MessageContext, SingleThreadedAgentRuntime, \
    DefaultTopicId, TypeSubscription, ClosureAgent, CancellationToken, ClosureContext, TopicId
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType
from pydantic import BaseModel, Field

from ..controllers.requirement import requirement_controller
from ..schemas.requirement import RequirementCreate
from llama_index.core import SimpleDirectoryReader, Document
from .llms import model_client, model_client_json

from .utils import extract_text_from_llm

# 定义主题类型
requirement_acquisition_topic_type = "requirement_acquisition"
requirement_analysis_topic_type = "requirement_analysis"
requirement_output_topic_type = "requirement_output"
requirement_database_topic_type = "requirement_database"
markdown_acquisition_topic_type = "markdown_acquisition"

task_result_topic_type = "collect_result"


class RequirementList(BaseModel):
    requirements: list[RequirementCreate] = Field(..., description="业务需求列表")


class RequirementFilesMessage(BaseModel):
    user_id: str = Field(..., description="用户ID")
    files: list[str] = Field(..., description="需求文件路径列表")
    content: str = Field(..., description="用户输入的内容")
    task: str = Field(default="分析需求文档", description="任务描述")


class ResponseMessage(BaseModel):
    source: str
    content: str
    is_final: bool = False


@dataclass
class RequirementMessage:
    source: str
    content: Any


@type_subscription(topic_type=markdown_acquisition_topic_type)
class MarkdownParserAgent(RoutedAgent):
    @message_handler
    async def handle_message(self, message: RequirementFilesMessage, ctx: MessageContext) -> None:
        # 1、调用marker获取markdown文件
        # 2、调用langchain进行分块
        # 3、具体自行可以实现一下
        documents = []
        for doc in documents:
            await self.publish_message(
                RequirementMessage(source=self.id.type, content=doc),
                topic_id=TopicId(requirement_acquisition_topic_type, source=self.id.key))


@type_subscription(topic_type=requirement_acquisition_topic_type)
class RequirementAcquisitionAgent(RoutedAgent):
    def __init__(self, input_func=None):
        super().__init__("requirement acquisition agent")
        self.input_func = input_func

    @message_handler
    async def handle_message(self, message: RequirementFilesMessage, ctx: MessageContext) -> None:
        # 发送到前端提示
        await self.publish_message(ResponseMessage(source="user", content=f"收到用户指令，准备开始需求分析"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        try:
            # 从文件中读取文档内容
            doc_content = await self.get_document_from_llm_files(message.files)

            # 发送处理状态到前端
            await self.publish_message(
                ResponseMessage(source="文档解析智能体", content="文件解析完成，开始对文档进行深入解析"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 创建需求获取智能体
            acquisition_agent = AssistantAgent(
                name="requirement_acquisition_agent",
                model_client=model_client,
                system_message="""
                你是一位专业的软件需求文档分析师，专长于从原始需求文档中提取关键信息以支持后续的软件测试活动。
                请仔细阅读并理解提供的需求文档内容（可能包含文本、图表、流程图信息），然后进行整理和摘要。

                重点提取和归纳以下信息:
                1.  **主要功能需求**: 清晰描述系统应具备的核心功能。
                2.  **非功能性需求**: 如性能指标、安全性要求、可用性、兼容性等。
                3.  **业务背景与目标**: 解释该需求的业务价值和要解决的问题。
                4.  **用户角色与关键使用场景**: 识别不同的用户类型及其典型交互流程。
                5.  **核心术语与概念定义**: 列出并解释文档中的关键名词或特殊概念。
                6.  **数据需求**: 涉及的关键数据结构、输入/输出数据格式等。
                7.  **依赖关系与约束**: 识别与其他系统/模块的依赖或技术/环境限制。
                8.  **潜在歧义与待确认点**: 标记出文档中描述不清、可能存在多种解释或需要进一步澄清的部分。

                请以结构化、层次清晰的 Markdown 格式输出你的分析摘要。确保信息准确、简洁，为后续生成详细的测试需求奠定基础。

                """,
                model_client_stream=True,
            )
            acquisition_content = ""

            # 运行需求获取流程
            if self.input_func:
                user_proxy = UserProxyAgent(
                    name="user_proxy",
                    input_func=self.input_func
                )

                # 设置对话终止条件
                termination_en = TextMentionTermination("APPROVE")
                termination_zh = TextMentionTermination("同意")
                team = RoundRobinGroupChat([acquisition_agent, user_proxy],
                                           termination_condition=termination_en | termination_zh)

                stream = team.run_stream(task=f"请分析以下需求文档内容:\n\n{doc_content}")
                update_count = 0

                # 存储需求获取记录
                acquisition_memory = ListMemory()

                async for msg in stream:
                    # 模拟流式输出
                    if isinstance(msg, ModelClientStreamingChunkEvent):
                        await self.publish_message(
                            ResponseMessage(source="文档解析智能体", content=msg.content),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                        continue

                    # 统计需求获取更新次数并保存结果
                    if isinstance(msg, TextMessage):
                        # 保存需求获取记录
                        await acquisition_memory.add(
                            MemoryContent(content=msg.model_dump_json(), mime_type=MemoryMimeType.JSON))

                        if msg.source == "requirement_acquisition_agent":
                            # 用户参与反馈的次数
                            update_count += 1
                            acquisition_content = msg.content
                            continue

                    # 等待用户输入对需求获取的反馈
                    if isinstance(msg, UserInputRequestedEvent) and msg.source == "user_proxy":
                        await self.publish_message(
                            ResponseMessage(source=msg.source, content="请输入修改建议或者直接点击同意"),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                        continue

                # 如果用户反馈次数大于1，则整合修改后的内容
                if update_count > 1:
                    # 整合智能体
                    summarize_agent = AssistantAgent(
                        name="summarize_agent",
                        system_message="""你是一位需求整理优化专家，根据上下文对话信息，输出用户最终期望的优化后的需求分析。""",
                        model_client=model_client,
                        memory=[acquisition_memory],
                        model_client_stream=True,
                    )

                    stream = summarize_agent.run_stream(
                        task="结合上下文对话信息，输出优化后的完整需求分析，markdown格式输出")
                    async for msg in stream:
                        # 流式输出到前端界面
                        if isinstance(msg, ModelClientStreamingChunkEvent):
                            # 流式输出结果到前端界面
                            await self.publish_message(
                                ResponseMessage(source="需求优化智能体", content=msg.content),
                                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                            continue
                        # 获取优化后的完整结果，将数据传递给下一个智能体
                        if isinstance(msg, TaskResult):
                            acquisition_content = msg.messages[-1].content
                            continue
            else:
                # 用户没有反馈
                task = f"请分析以下需求文档内容:\n\n{doc_content}"
                stream = acquisition_agent.run_stream(task=task)

                async for msg in stream:
                    if isinstance(msg, ModelClientStreamingChunkEvent):
                        await self.publish_message(
                            ResponseMessage(source="需求获取智能体", content=msg.content),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                        continue
                    if isinstance(msg, TaskResult):
                        acquisition_content = msg.messages[-1].content
                        continue

            # 发送给下一个智能体
            await self.publish_message(
                RequirementMessage(source=self.id.type, content=acquisition_content),
                topic_id=TopicId(requirement_analysis_topic_type, source=self.id.key))

        except Exception as e:
            error_msg = f"需求获取过程出错: {str(e)}"
            print(error_msg)
            await self.publish_message(
                ResponseMessage(source="需求获取智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

    async def get_document_from_files(self, files: list[str]) -> str:
        """获取文件内容"""
        try:
            data = SimpleDirectoryReader(input_files=files).load_data()
            doc = Document(text="\n\n".join([d.text for d in data[0:]]))
            return doc.text
        except Exception as e:
            raise Exception(f"文件读取失败: {str(e)}")

    async def get_document_from_llm_files(self, files: list[str]) -> str:
        """获取文件内容，支持图片、流程图、表格等数据"""
        extract_contents = ""
        for file in files:
            contents = extract_text_from_llm(file)
            extract_contents += contents + "\n\n--------------\n\n"
        return extract_contents


@type_subscription(topic_type=requirement_analysis_topic_type)
class RequirementAnalysisAgent(RoutedAgent):
    def __init__(self):
        super().__init__("requirement analysis agent")
        self._prompt = """
        根据如下格式的需求文档，进行需求分析，输出需求分析报告：
            ## 1. Background
            - **角色定位**: 资深软件测试需求分析师，具备跨领域测试经验
            - **核心职责**: 将模糊需求转化为可执行的测试方案，识别需求盲区与风险点
            - **行业经验**: 5年以上金融/医疗/物联网领域测试体系构建经验

            ## 2. Profile
            - **姓名**: TesterBot
            - **职位**: 智能测试需求架构师
            - **特质**:
              - 严谨的逻辑推理能力
              - 敏锐的边界条件发现能力
              - 优秀的风险预判意识

            ## 3. Skills
            - 掌握ISTQB/敏捷测试方法论
            - 精通测试用例设计方法（等价类/边界值/场景法等）
            - 熟练使用JIRA/TestRail/XMind
            - 擅长需求可测试性评估
            - 精通API/性能/安全测试策略制定

            ## 4. Goals
            1. 解析用户原始需求，明确测试范围
            2. 识别隐含需求与潜在风险点
            3. 生成结构化测试需求文档
            4. 输出可量化的验收标准
            5. 建立需求追溯矩阵

            ## 5. Constraints
            - 不涉及具体测试代码实现
            - 不替代人工需求评审
            - 保持技术中立立场
            - 遵守ISTQB伦理规范

            ## 6. Output Format
            ```markdown
            # 测试需求分析文档

            ## 测试目标
            - [清晰的功能目标描述]

            ## 需求拆解
            | 需求ID | 需求描述 | 测试类型 | 优先级 | 验收标准 |
            |--------|----------|----------|--------|----------|

            ## 风险分析
            - **高优先级风险**:
              - [风险描述] → [缓解方案]

            ## 测试策略
            - [功能测试]:
              - 覆盖场景:
                - [场景1]
                - [场景2]
            - [非功能测试]:
              - 性能指标: [RPS ≥ 1000]
              - 安全要求: [OWASP TOP10覆盖]

            ## 待澄清项
            - [问题1] (需业务方确认)
            - [问题2] (需架构师确认)
            ```
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, ctx: MessageContext) -> None:
        # 创建需求分析智能体
        analysis_agent = AssistantAgent(
            name="requirement_analysis_agent",
            model_client=model_client,
            system_message=self._prompt,
            model_client_stream=True,
        )

        # 发送状态消息到前端
        await self.publish_message(
            ResponseMessage(source="需求分析智能体", content="开始进行需求分析......"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 构建任务描述
        task = f"请根据以下需求内容进行分析，并输出规范的需求分析报告：\n\n{message.content}"
        analysis_report = ""

        # 流式执行需求分析
        stream = analysis_agent.run_stream(task=task)
        async for msg in stream:
            if isinstance(msg, ModelClientStreamingChunkEvent):
                # 流式输出结果到前端界面
                await self.publish_message(
                    ResponseMessage(source="需求分析智能体", content=msg.content),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                continue
            if isinstance(msg, TaskResult):
                analysis_report = msg.messages[-1].content
                continue

        # 发送给下一个智能体
        await self.publish_message(
            RequirementMessage(source=self.id.type, content=analysis_report),
            topic_id=TopicId(requirement_output_topic_type, source=self.id.key))


async def start_runtime(requirement_files: RequirementFilesMessage,
                        collect_result: Callable[[ClosureContext, ResponseMessage, MessageContext], Awaitable[None]],
                        user_input_func: Callable[[str, Optional[CancellationToken]], Awaitable[str]] = None):
    """启动需求分析运行时"""

    runtime = SingleThreadedAgentRuntime()

    # 注册智能体
    await RequirementAcquisitionAgent.register(
        runtime,
        requirement_acquisition_topic_type,
        lambda: RequirementAcquisitionAgent(input_func=user_input_func)
    )
    await RequirementAnalysisAgent.register(
        runtime,
        requirement_analysis_topic_type,
        lambda: RequirementAnalysisAgent()
    )
    await MarkdownParserAgent.register(
        runtime,
        markdown_acquisition_topic_type,
        lambda: MarkdownParserAgent()
    )

    # 定义闭包智能体接收消息
    await ClosureAgent.register_closure(
        runtime,
        "closure_agent",
        collect_result,
        subscriptions=lambda: [TypeSubscription(topic_type=task_result_topic_type, agent_type="closure_agent")],
    )

    # 启动运行时并发布消息
    runtime.start()
    await runtime.publish_message(
        requirement_files,
        topic_id=DefaultTopicId(type=requirement_acquisition_topic_type)
    )

    # 等待所有任务完成并关闭运行时
    await runtime.stop_when_idle()
    await runtime.close()
