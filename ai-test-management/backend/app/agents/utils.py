import os
from typing import List
from docx import Document
import aiofiles


def extract_text_from_llm(file_path: str) -> str:
    """从文件中提取文本内容"""
    try:
        if file_path.endswith('.txt'):
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        elif file_path.endswith('.docx'):
            doc = Document(file_path)
            text = []
            for paragraph in doc.paragraphs:
                text.append(paragraph.text)
            return '\n'.join(text)
        else:
            return f"不支持的文件格式: {file_path}"
    except Exception as e:
        return f"文件读取失败: {str(e)}"


async def save_uploaded_file(file_content: bytes, filename: str, upload_dir: str = "uploads") -> str:
    """保存上传的文件"""
    os.makedirs(upload_dir, exist_ok=True)
    file_path = os.path.join(upload_dir, filename)
    
    async with aiofiles.open(file_path, 'wb') as f:
        await f.write(file_content)
    
    return file_path


def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return os.path.splitext(filename)[1].lower()
