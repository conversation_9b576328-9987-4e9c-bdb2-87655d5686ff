from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc
from ..models.testcase import TestCase, TestStep
from ..schemas.testcase import TestCaseCreate, TestCaseUpdate
from .base import CRUDBase


class CRUDTestCase(CRUDBase[TestCase, TestCaseCreate, TestCaseUpdate]):
    def create_with_steps(self, db: Session, *, obj_in: TestCaseCreate) -> TestCase:
        # 创建测试用例
        testcase_data = obj_in.dict(exclude={'steps'})
        db_testcase = TestCase(**testcase_data)
        db.add(db_testcase)
        db.commit()
        db.refresh(db_testcase)
        
        # 创建测试步骤
        if obj_in.steps:
            for i, step in enumerate(obj_in.steps, 1):
                db_step = TestStep(
                    step_number=i,
                    description=step.description,
                    expected_result=step.expected_result,
                    testcase_id=db_testcase.id
                )
                db.add(db_step)
            db.commit()
            db.refresh(db_testcase)
        
        return db_testcase

    def get_multi_with_search(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        project_id: Optional[int] = None,
        requirement_id: Optional[int] = None
    ) -> List[TestCase]:
        query = db.query(TestCase)
        
        if project_id:
            query = query.filter(TestCase.project_id == project_id)
        
        if requirement_id:
            query = query.filter(TestCase.requirement_id == requirement_id)
        
        if search:
            query = query.filter(
                or_(
                    TestCase.title.ilike(f"%{search}%"),
                    TestCase.description.ilike(f"%{search}%")
                )
            )
        
        return query.order_by(desc(TestCase.created_at)).offset(skip).limit(limit).all()

    def count_with_search(
        self,
        db: Session,
        *,
        search: Optional[str] = None,
        project_id: Optional[int] = None,
        requirement_id: Optional[int] = None
    ) -> int:
        query = db.query(TestCase)
        
        if project_id:
            query = query.filter(TestCase.project_id == project_id)
        
        if requirement_id:
            query = query.filter(TestCase.requirement_id == requirement_id)
        
        if search:
            query = query.filter(
                or_(
                    TestCase.title.ilike(f"%{search}%"),
                    TestCase.description.ilike(f"%{search}%")
                )
            )
        
        return query.count()

    def get_by_requirement(self, db: Session, *, requirement_id: int) -> List[TestCase]:
        return db.query(TestCase).filter(TestCase.requirement_id == requirement_id).all()


testcase_controller = CRUDTestCase(TestCase)
