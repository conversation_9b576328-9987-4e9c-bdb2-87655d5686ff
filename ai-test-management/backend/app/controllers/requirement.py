from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc
from ..models.requirement import Requirement
from ..schemas.requirement import RequirementCreate, RequirementUpdate
from .base import CRUDBase


class CRUDRequirement(CRUDBase[Requirement, RequirementCreate, RequirementUpdate]):
    def get_multi_with_search(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        project_id: Optional[int] = None
    ) -> List[Requirement]:
        query = db.query(Requirement)
        
        if project_id:
            query = query.filter(Requirement.project_id == project_id)
        
        if search:
            query = query.filter(
                or_(
                    Requirement.name.ilike(f"%{search}%"),
                    Requirement.description.ilike(f"%{search}%"),
                    Requirement.keywords.ilike(f"%{search}%")
                )
            )
        
        return query.order_by(desc(Requirement.created_at)).offset(skip).limit(limit).all()

    def count_with_search(
        self,
        db: Session,
        *,
        search: Optional[str] = None,
        project_id: Optional[int] = None
    ) -> int:
        query = db.query(Requirement)
        
        if project_id:
            query = query.filter(Requirement.project_id == project_id)
        
        if search:
            query = query.filter(
                or_(
                    Requirement.name.ilike(f"%{search}%"),
                    Requirement.description.ilike(f"%{search}%"),
                    Requirement.keywords.ilike(f"%{search}%")
                )
            )
        
        return query.count()

    def get_by_project(self, db: Session, *, project_id: int) -> List[Requirement]:
        return db.query(Requirement).filter(Requirement.project_id == project_id).all()

    async def create(self, *, obj_in: RequirementCreate) -> Requirement:
        """异步创建需求"""
        # 这里可以添加异步数据库操作
        # 暂时使用同步方式
        pass


requirement_controller = CRUDRequirement(Requirement)
