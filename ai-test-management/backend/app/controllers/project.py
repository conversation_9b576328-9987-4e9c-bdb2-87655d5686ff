from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc
from ..models.project import Project
from ..schemas.project import ProjectCreate, ProjectUpdate
from .base import CRUDBase


class CRUDProject(CRUDBase[Project, ProjectCreate, ProjectUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Project]:
        return db.query(Project).filter(Project.name == name).first()

    def get_by_code(self, db: Session, *, project_code: str) -> Optional[Project]:
        return db.query(Project).filter(Project.project_code == project_code).first()

    def create_with_creator(self, db: Session, *, obj_in: ProjectCreate, creator_id: int) -> Project:
        db_obj = Project(
            project_code=obj_in.project_code,
            name=obj_in.name,
            description=obj_in.description,
            creator_id=creator_id
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi_with_search(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        creator_id: Optional[int] = None
    ) -> List[Project]:
        query = db.query(Project)
        
        if creator_id:
            query = query.filter(Project.creator_id == creator_id)
        
        if search:
            query = query.filter(
                or_(
                    Project.name.ilike(f"%{search}%"),
                    Project.description.ilike(f"%{search}%"),
                    Project.project_code.ilike(f"%{search}%")
                )
            )
        
        return query.order_by(desc(Project.created_at)).offset(skip).limit(limit).all()

    def count_with_search(
        self,
        db: Session,
        *,
        search: Optional[str] = None,
        creator_id: Optional[int] = None
    ) -> int:
        query = db.query(Project)
        
        if creator_id:
            query = query.filter(Project.creator_id == creator_id)
        
        if search:
            query = query.filter(
                or_(
                    Project.name.ilike(f"%{search}%"),
                    Project.description.ilike(f"%{search}%"),
                    Project.project_code.ilike(f"%{search}%")
                )
            )
        
        return query.count()


project_controller = CRUDProject(Project)
