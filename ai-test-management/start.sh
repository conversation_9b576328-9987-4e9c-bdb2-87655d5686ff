#!/bin/bash

# AI测试管理系统启动脚本

echo "==================================="
echo "AI测试管理系统启动脚本"
echo "==================================="

# 检查是否安装了必要的工具
check_requirements() {
    echo "检查系统要求..."

    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "错误: 未找到Python3，请先安装Python 3.9+"
        exit 1
    fi

    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "错误: 未找到Node.js，请先安装Node.js 16+"
        exit 1
    fi

    # 检查Docker（可选）
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        echo "检测到Docker，可以使用Docker模式启动"
        DOCKER_AVAILABLE=true
    else
        echo "未检测到Docker，将使用本地模式"
        DOCKER_AVAILABLE=false
    fi

    # 检查PostgreSQL
    if ! command -v psql &> /dev/null && [ "$DOCKER_AVAILABLE" = false ]; then
        echo "警告: 未找到PostgreSQL，请确保PostgreSQL已安装并运行，或使用Docker模式"
    fi

    echo "系统要求检查完成"
}

# 设置后端
setup_backend() {
    echo "设置后端环境..."
    cd backend
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        echo "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    echo "安装Python依赖..."
    pip install -r requirements.txt
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        echo "创建环境变量文件..."
        cp .env.example .env
        echo "请编辑 backend/.env 文件，配置数据库连接和AI模型API密钥"
    fi
    
    cd ..
}

# 设置前端
setup_frontend() {
    echo "设置前端环境..."
    cd frontend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        npm install
    fi
    
    cd ..
}

# 初始化数据库
init_database() {
    echo "初始化数据库..."
    cd backend
    source venv/bin/activate
    
    # 运行数据库迁移
    echo "运行数据库迁移..."
    alembic upgrade head
    
    # 初始化数据
    echo "初始化数据..."
    python -m app.init_db
    
    cd ..
}

# 启动服务
start_services() {
    echo "启动服务..."
    
    # 启动后端
    echo "启动后端服务..."
    cd backend
    source venv/bin/activate
    python start.py &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 5
    
    # 启动前端
    echo "启动前端服务..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo "==================================="
    echo "服务启动完成！"
    echo "后端API: http://localhost:8000"
    echo "前端应用: http://localhost:3000"
    echo "API文档: http://localhost:8000/docs"
    echo "==================================="
    echo "默认账号："
    echo "管理员: admin / admin123"
    echo "测试员: tester / test123"
    echo "==================================="
    echo "按 Ctrl+C 停止服务"
    
    # 等待用户中断
    trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
    wait
}

# 启动Docker服务
start_docker() {
    echo "使用Docker启动服务..."

    # 启动数据库服务
    echo "启动数据库服务..."
    docker-compose -f docker-compose.dev.yml up -d

    # 等待数据库启动
    echo "等待数据库启动..."
    sleep 10

    # 启动后端
    echo "启动后端服务..."
    cd backend
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    source venv/bin/activate
    pip install -r requirements.txt
    python start.py &
    BACKEND_PID=$!
    cd ..

    # 等待后端启动
    sleep 5

    # 启动前端
    echo "启动前端服务..."
    cd frontend
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    npm run dev &
    FRONTEND_PID=$!
    cd ..

    echo "==================================="
    echo "Docker模式启动完成！"
    echo "数据库: localhost:5432"
    echo "Redis: localhost:6379"
    echo "后端API: http://localhost:8000"
    echo "前端应用: http://localhost:3000"
    echo "==================================="

    # 等待用户中断
    trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; docker-compose -f docker-compose.dev.yml down; exit" INT
    wait
}

# 主函数
main() {
    case "$1" in
        "setup")
            check_requirements
            setup_backend
            setup_frontend
            echo "环境设置完成！运行 './start.sh init' 初始化数据库"
            ;;
        "init")
            init_database
            echo "数据库初始化完成！运行 './start.sh start' 启动服务"
            ;;
        "start")
            start_services
            ;;
        "docker")
            check_requirements
            if [ "$DOCKER_AVAILABLE" = true ]; then
                start_docker
            else
                echo "错误: Docker未安装或不可用"
                exit 1
            fi
            ;;
        *)
            echo "用法: $0 {setup|init|start|docker}"
            echo "  setup  - 设置开发环境"
            echo "  init   - 初始化数据库"
            echo "  start  - 启动服务（本地模式）"
            echo "  docker - 启动服务（Docker模式）"
            echo ""
            echo "本地模式首次使用请依次执行："
            echo "  ./start.sh setup"
            echo "  ./start.sh init"
            echo "  ./start.sh start"
            echo ""
            echo "Docker模式（推荐）："
            echo "  ./start.sh docker"
            exit 1
            ;;
    esac
}

main "$@"
