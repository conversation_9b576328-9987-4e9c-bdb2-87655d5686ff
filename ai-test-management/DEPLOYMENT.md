# AI测试管理系统部署指南

## 系统概述

AI测试管理系统是一个基于AI的智能化测试管理平台，集成了需求分析、测试用例生成、自动化测试等功能。

### 技术架构

- **后端**: FastAPI + Python 3.9+ + AutoGen 0.5.5
- **前端**: Vue 3 + TypeScript + Element Plus
- **数据库**: PostgreSQL 12+
- **AI框架**: AutoGen多智能体架构
- **部署**: Docker + Docker Compose

## 快速启动

### 方式一：Docker模式（推荐）

1. **前置要求**
   ```bash
   # 确保已安装Docker和Docker Compose
   docker --version
   docker-compose --version
   ```

2. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ai-test-management
   ```

3. **启动服务**
   ```bash
   chmod +x start.sh
   ./start.sh docker
   ```

4. **访问系统**
   - 前端应用: http://localhost:3000
   - 后端API: http://localhost:8000
   - API文档: http://localhost:8000/docs

### 方式二：本地开发模式

1. **环境准备**
   ```bash
   # Python 3.9+
   python3 --version
   
   # Node.js 16+
   node --version
   
   # PostgreSQL 12+
   psql --version
   ```

2. **设置环境**
   ```bash
   ./start.sh setup
   ```

3. **初始化数据库**
   ```bash
   ./start.sh init
   ```

4. **启动服务**
   ```bash
   ./start.sh start
   ```

## 配置说明

### 环境变量配置

编辑 `backend/.env` 文件：

```bash
# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/ai_test_db

# AI模型配置
LLM_MODEL=deepseek-chat
LLM_API_BASE=https://api.deepseek.com/v1
LLM_API_KEY=your-api-key-here

# JWT配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 数据库配置

1. **创建数据库**
   ```sql
   CREATE DATABASE ai_test_db;
   ```

2. **运行初始化脚本**
   ```bash
   psql -U postgres -d ai_test_db -f database/init.sql
   ```

## 默认账号

系统预置了以下测试账号：

- **管理员**: admin / admin123
- **测试员**: tester / test123

## 功能模块

### 1. 仪表盘
- 项目概览统计
- 需求和用例分布图表
- 最近项目列表

### 2. 项目管理
- 项目CRUD操作
- 项目搜索和过滤
- 项目统计信息

### 3. 需求管理
- **需求分析**: AI智能分析需求文档
- **需求列表**: 需求的增删改查
- **文档上传**: 支持.txt和.docx格式

### 4. 用例管理
- **AI生成**: 基于需求自动生成测试用例
- **用例管理**: 测试用例的完整生命周期管理
- **步骤管理**: 详细的测试步骤定义

### 5. 自动化测试（Demo）
- UI自动化测试
- 接口自动化测试
- 性能测试
- 安全测试

### 6. 其他功能（Demo）
- 缺陷管理
- 测试报告
- 用户管理

## AI智能体架构

### 需求分析智能体链
1. **需求获取智能体**: 解析和提取需求文档内容
2. **需求分析智能体**: 深度分析需求并生成结构化输出
3. **数据库智能体**: 将分析结果保存到数据库

### 测试用例生成智能体链
1. **用例生成智能体**: 基于需求生成测试用例
2. **用例结构化智能体**: 格式化和优化测试用例
3. **数据库智能体**: 保存测试用例到数据库

## 生产环境部署

### 1. 环境准备

```bash
# 服务器要求
- CPU: 4核心以上
- 内存: 8GB以上
- 存储: 50GB以上
- 操作系统: Ubuntu 20.04+ / CentOS 8+
```

### 2. Docker部署

```bash
# 1. 克隆代码
git clone <repository-url>
cd ai-test-management

# 2. 配置环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件

# 3. 构建和启动
docker-compose up -d

# 4. 查看日志
docker-compose logs -f
```

### 3. 反向代理配置（Nginx）

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 监控和维护

### 1. 日志管理

```bash
# 查看应用日志
docker-compose logs backend
docker-compose logs frontend

# 查看数据库日志
docker-compose logs postgres
```

### 2. 数据备份

```bash
# 备份数据库
docker exec ai_test_postgres pg_dump -U postgres ai_test_db > backup.sql

# 恢复数据库
docker exec -i ai_test_postgres psql -U postgres ai_test_db < backup.sql
```

### 3. 性能监控

- 监控CPU、内存使用率
- 监控数据库连接数
- 监控API响应时间
- 监控磁盘空间使用

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证数据库连接字符串
   - 检查防火墙设置

2. **AI模型调用失败**
   - 验证API密钥是否正确
   - 检查网络连接
   - 查看API调用限制

3. **前端页面无法访问**
   - 检查前端服务状态
   - 验证端口是否被占用
   - 检查代理配置

### 日志分析

```bash
# 查看详细错误日志
docker-compose logs --tail=100 backend

# 实时监控日志
docker-compose logs -f backend
```

## 开发指南

### 添加新功能

1. **后端API开发**
   - 在 `backend/app/models/` 中定义数据模型
   - 在 `backend/app/schemas/` 中定义Pydantic模式
   - 在 `backend/app/controllers/` 中实现业务逻辑
   - 在 `backend/app/api/v1/` 中添加API路由

2. **前端页面开发**
   - 在 `frontend/src/views/` 中创建页面组件
   - 在 `frontend/src/api/` 中添加API接口
   - 在 `frontend/src/router/` 中配置路由
   - 在 `frontend/src/types/` 中定义类型

### 代码规范

- 后端遵循PEP 8规范
- 前端使用ESLint和Prettier
- 提交前运行代码检查
- 编写单元测试

## 技术支持

如有问题，请：

1. 查看本文档的故障排除部分
2. 检查GitHub Issues
3. 提交新的Issue描述问题
4. 联系开发团队

---

**注意**: 这是一个演示系统，部分功能（如自动化测试、缺陷管理等）仅为Demo展示。在生产环境中使用前，请根据实际需求进行完善和测试。
